/**
 * 通用类型定义
 * <AUTHOR>
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

// 分页响应类型
export interface PageResponse<T = any> {
  records: T[]
  total: number
  current: number
  size: number
  pages: number
}

// 分页查询参数
export interface PageParams {
  pageNum?: number
  pageSize?: number
}

// 基础实体类型
export interface BaseEntity {
  id: number
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  deleted?: number
  version?: number
}

// 选项类型
export interface Option {
  label: string
  value: string | number
  disabled?: boolean
  children?: Option[]
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  show?: boolean
}

// 表单规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 菜单类型
export interface MenuItem {
  id: number
  parentId: number
  name: string
  title: string
  path: string
  component?: string
  icon?: string
  type: number
  sortOrder: number
  status: number
  visible: number
  permission?: string
  children?: MenuItem[]
}

// 路由元信息
export interface RouteMeta {
  title?: string
  icon?: string
  requiresAuth?: boolean
  roles?: string[]
  permissions?: string[]
  hidden?: boolean
  keepAlive?: boolean
  affix?: boolean
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  status: number
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
  lastLoginIp?: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
  rememberMe?: boolean
}

// 文件上传响应
export interface UploadResponse {
  url: string
  name: string
  size: number
}

// 字典项
export interface DictItem {
  id: number
  itemCode: string
  itemName: string
  itemValue: string
  sortOrder: number
  status: number
}
