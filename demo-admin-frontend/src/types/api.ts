/**
 * API相关类型定义
 * <AUTHOR>
 */

import type { BaseEntity, PageParams } from './common'

// 用户相关类型
export interface User extends BaseEntity {
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  gender: number
  genderDesc: string
  avatar?: string
  status: number
  statusDesc: string
  deptId?: number
  lastLoginTime?: string
  lastLoginIp?: string
  roles: Array<{
    id: number
    name: string
    code: string
  }>
}

export interface UserQueryParams extends PageParams {
  username?: string
  realName?: string
  email?: string
  phone?: string
  status?: number
  deptId?: number
}

export interface UserCreateRequest {
  username: string
  password: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  status?: number
  deptId?: number
  roleIds?: number[]
  remark?: string
}

export interface UserUpdateRequest {
  realName?: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  status?: number
  deptId?: number
  remark?: string
}

// 角色相关类型
export interface Role extends BaseEntity {
  name: string
  code: string
  description?: string
  status: number
  statusDesc: string
  sortOrder: number
  dataScope: number
  isSystem: number
  permissions: Array<{
    id: number
    name: string
    code: string
    type: string
  }>
}

export interface RoleQueryParams extends PageParams {
  name?: string
  code?: string
  status?: number
}

export interface RoleCreateRequest {
  name: string
  code: string
  description?: string
  sortOrder?: number
  dataScope?: number
  permissionIds?: number[]
  remark?: string
}

// 菜单相关类型
export interface Menu extends BaseEntity {
  parentId: number
  name: string
  title: string
  path?: string
  component?: string
  icon?: string
  type: number
  sortOrder: number
  status: number
  visible: number
  permission?: string
  children?: Menu[]
}

export interface MenuCreateRequest {
  parentId: number
  name: string
  title: string
  path?: string
  component?: string
  icon?: string
  type: number
  sortOrder?: number
  visible?: number
  permission?: string
  remark?: string
}

// 系统配置相关类型
export interface Config extends BaseEntity {
  configKey: string
  configValue: string
  configType: string
  description?: string
  isEncrypted: number
  isSystem: number
  status: number
}

export interface ConfigQueryParams extends PageParams {
  configKey?: string
  configType?: string
}

// 数据字典相关类型
export interface Dict extends BaseEntity {
  dictCode: string
  dictName: string
  description?: string
  status: number
  items: Array<{
    id: number
    itemCode: string
    itemName: string
    itemValue: string
    sortOrder: number
    status: number
  }>
}

export interface DictQueryParams extends PageParams {
  dictCode?: string
  dictName?: string
  status?: number
}

// 个人中心相关类型
export interface ProfileInfo {
  id: number
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  gender: number
  genderDesc: string
  avatar?: string
  lastLoginTime?: string
  lastLoginIp?: string
  passwordUpdateTime?: string
  createTime: string
}

export interface ProfileUpdateRequest {
  realName?: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  remark?: string
}

export interface PasswordChangeRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface SettingsInfo {
  theme: string
  language: string
  timezone: string
  dateFormat: string
  timeFormat: string
  menuCollapsed: number
  pageSize: number
  notificationEnabled: number
  emailNotification: number
  smsNotification: number
}

export interface SettingsUpdateRequest {
  theme?: string
  language?: string
  timezone?: string
  dateFormat?: string
  timeFormat?: string
  menuCollapsed?: number
  pageSize?: number
  notificationEnabled?: number
  emailNotification?: number
  smsNotification?: number
}

// 设备信息
export interface DeviceInfo {
  id: number
  deviceId: string
  deviceName: string
  deviceType: string
  osName: string
  browserName: string
  browserVersion: string
  ipAddress: string
  location: string
  loginTime: string
  lastActiveTime: string
  status: number
  statusDesc: string
  isCurrent: number
}

// 用户日志
export interface UserLog {
  id: number
  operationType: string
  operationDesc: string
  requestMethod: string
  requestUrl: string
  responseResult: string
  errorMessage?: string
  ipAddress: string
  location: string
  deviceId: string
  executionTime: number
  createTime: string
}

export interface UserLogQueryParams extends PageParams {
  operationType?: string
  startTime?: string
  endTime?: string
}
