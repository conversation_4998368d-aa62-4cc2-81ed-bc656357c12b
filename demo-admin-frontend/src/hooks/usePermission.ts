/**
 * 权限控制Hook
 * <AUTHOR>
 */

import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限控制Hook
 */
export function usePermission() {
  const authStore = useAuthStore()

  /**
   * 检查是否有指定权限
   * @param permission 权限标识
   */
  const hasPermission = (permission: string): boolean => {
    return authStore.hasPermission(permission)
  }

  /**
   * 检查是否有任一权限
   * @param permissions 权限标识数组
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return authStore.hasAnyPermission(permissions)
  }

  /**
   * 检查是否有所有权限
   * @param permissions 权限标识数组
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => authStore.hasPermission(permission))
  }

  /**
   * 检查是否有指定角色
   * @param role 角色标识
   */
  const hasRole = (role: string): boolean => {
    return authStore.hasRole(role)
  }

  /**
   * 检查是否有任一角色
   * @param roles 角色标识数组
   */
  const hasAnyRole = (roles: string[]): boolean => {
    return authStore.hasAnyRole(roles)
  }

  /**
   * 检查是否有所有角色
   * @param roles 角色标识数组
   */
  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every(role => authStore.hasRole(role))
  }

  /**
   * 检查是否是超级管理员
   */
  const isSuperAdmin = computed(() => {
    return authStore.hasRole('super_admin') || authStore.hasRole('admin')
  })

  /**
   * 检查是否有管理员权限
   */
  const isAdmin = computed(() => {
    return authStore.hasRole('admin')
  })

  /**
   * 获取当前用户权限列表
   */
  const permissions = computed(() => {
    return authStore.permissions
  })

  /**
   * 获取当前用户角色列表
   */
  const roles = computed(() => {
    return authStore.roles
  })

  /**
   * 权限检查函数（支持权限和角色组合检查）
   * @param options 检查选项
   */
  const checkAuth = (options: {
    permissions?: string[]
    roles?: string[]
    operator?: 'AND' | 'OR' // 操作符：AND表示同时满足，OR表示满足任一
  }): boolean => {
    const { permissions = [], roles = [], operator = 'AND' } = options

    let hasPermissionResult = true
    let hasRoleResult = true

    // 检查权限
    if (permissions.length > 0) {
      hasPermissionResult = hasAnyPermission(permissions)
    }

    // 检查角色
    if (roles.length > 0) {
      hasRoleResult = hasAnyRole(roles)
    }

    // 根据操作符返回结果
    if (operator === 'OR') {
      return hasPermissionResult || hasRoleResult
    } else {
      return hasPermissionResult && hasRoleResult
    }
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isSuperAdmin,
    isAdmin,
    permissions,
    roles,
    checkAuth
  }
}
