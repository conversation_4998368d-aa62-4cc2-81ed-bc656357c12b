/**
 * 应用入口文件
 * <AUTHOR>
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// 导入样式
import 'element-plus/dist/index.css'
import '@/assets/styles/global.scss'

// 导入组件
import App from './App.vue'
import router from './router'

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入指令
import { setupDirectives } from '@/directives'

// 导入错误处理
import { setupGlobalErrorHandler } from '@/utils/error'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)

// 安装指令
setupDirectives(app)

// 设置全局错误处理
setupGlobalErrorHandler()

// 全局属性
app.config.globalProperties.$env = import.meta.env

// 挂载应用
app.mount('#app')
