/**
 * 权限常量定义
 * <AUTHOR>
 */

// 系统管理权限
export const SYSTEM_PERMISSIONS = {
  // 用户管理
  USER: {
    LIST: 'system:user:list',
    ADD: 'system:user:add',
    EDIT: 'system:user:edit',
    DELETE: 'system:user:delete',
    EXPORT: 'system:user:export',
    IMPORT: 'system:user:import',
    RESET_PASSWORD: 'system:user:resetPassword',
    UPDATE_STATUS: 'system:user:updateStatus',
    ASSIGN_ROLE: 'system:user:role'
  },
  
  // 角色管理
  ROLE: {
    QUERY: 'system:role:query',
    ADD: 'system:role:add',
    EDIT: 'system:role:edit',
    DELETE: 'system:role:delete',
    PERMISSION: 'system:role:permission'
  },
  
  // 菜单管理
  MENU: {
    LIST: 'system:menu:list',
    ADD: 'system:menu:add',
    EDIT: 'system:menu:edit',
    DELETE: 'system:menu:delete'
  },
  
  // 系统配置
  CONFIG: {
    LIST: 'system:config:list',
    ADD: 'system:config:add',
    EDIT: 'system:config:edit',
    DELETE: 'system:config:delete'
  },
  
  // 日志管理
  LOG: {
    LIST: 'system:log:list',
    VIEW: 'system:log:view',
    EXPORT: 'system:log:export',
    CLEAN: 'system:log:clean',
    STATISTICS: 'system:log:statistics'
  }
} as const

// 角色常量
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  USER: 'user'
} as const

// 权限组合
export const PERMISSION_GROUPS = {
  // 系统管理员权限组
  SYSTEM_ADMIN: [
    SYSTEM_PERMISSIONS.USER.LIST,
    SYSTEM_PERMISSIONS.USER.ADD,
    SYSTEM_PERMISSIONS.USER.EDIT,
    SYSTEM_PERMISSIONS.USER.DELETE,
    SYSTEM_PERMISSIONS.ROLE.QUERY,
    SYSTEM_PERMISSIONS.ROLE.ADD,
    SYSTEM_PERMISSIONS.ROLE.EDIT,
    SYSTEM_PERMISSIONS.ROLE.DELETE,
    SYSTEM_PERMISSIONS.MENU.LIST,
    SYSTEM_PERMISSIONS.MENU.ADD,
    SYSTEM_PERMISSIONS.MENU.EDIT,
    SYSTEM_PERMISSIONS.MENU.DELETE,
    SYSTEM_PERMISSIONS.CONFIG.LIST,
    SYSTEM_PERMISSIONS.CONFIG.ADD,
    SYSTEM_PERMISSIONS.CONFIG.EDIT,
    SYSTEM_PERMISSIONS.CONFIG.DELETE,
    SYSTEM_PERMISSIONS.LOG.LIST,
    SYSTEM_PERMISSIONS.LOG.VIEW,
    SYSTEM_PERMISSIONS.LOG.EXPORT,
    SYSTEM_PERMISSIONS.LOG.CLEAN,
    SYSTEM_PERMISSIONS.LOG.STATISTICS
  ],
  
  // 普通管理员权限组
  ADMIN: [
    SYSTEM_PERMISSIONS.USER.LIST,
    SYSTEM_PERMISSIONS.USER.ADD,
    SYSTEM_PERMISSIONS.USER.EDIT,
    SYSTEM_PERMISSIONS.ROLE.QUERY,
    SYSTEM_PERMISSIONS.MENU.LIST,
    SYSTEM_PERMISSIONS.CONFIG.LIST,
    SYSTEM_PERMISSIONS.LOG.LIST,
    SYSTEM_PERMISSIONS.LOG.VIEW,
    SYSTEM_PERMISSIONS.LOG.STATISTICS
  ],
  
  // 普通用户权限组
  USER: [
    SYSTEM_PERMISSIONS.USER.LIST,
    SYSTEM_PERMISSIONS.ROLE.QUERY,
    SYSTEM_PERMISSIONS.MENU.LIST
  ]
} as const

// 导出所有权限的扁平化数组
export const ALL_PERMISSIONS = Object.values(SYSTEM_PERMISSIONS)
  .flatMap(module => Object.values(module))

// 权限描述映射
export const PERMISSION_DESCRIPTIONS = {
  [SYSTEM_PERMISSIONS.USER.LIST]: '查看用户列表',
  [SYSTEM_PERMISSIONS.USER.ADD]: '新增用户',
  [SYSTEM_PERMISSIONS.USER.EDIT]: '编辑用户',
  [SYSTEM_PERMISSIONS.USER.DELETE]: '删除用户',
  [SYSTEM_PERMISSIONS.USER.EXPORT]: '导出用户',
  [SYSTEM_PERMISSIONS.USER.IMPORT]: '导入用户',
  [SYSTEM_PERMISSIONS.USER.RESET_PASSWORD]: '重置用户密码',
  [SYSTEM_PERMISSIONS.USER.UPDATE_STATUS]: '更新用户状态',
  [SYSTEM_PERMISSIONS.USER.ASSIGN_ROLE]: '分配用户角色',
  
  [SYSTEM_PERMISSIONS.ROLE.QUERY]: '查看角色列表',
  [SYSTEM_PERMISSIONS.ROLE.ADD]: '新增角色',
  [SYSTEM_PERMISSIONS.ROLE.EDIT]: '编辑角色',
  [SYSTEM_PERMISSIONS.ROLE.DELETE]: '删除角色',
  [SYSTEM_PERMISSIONS.ROLE.PERMISSION]: '分配角色权限',
  
  [SYSTEM_PERMISSIONS.MENU.LIST]: '查看菜单列表',
  [SYSTEM_PERMISSIONS.MENU.ADD]: '新增菜单',
  [SYSTEM_PERMISSIONS.MENU.EDIT]: '编辑菜单',
  [SYSTEM_PERMISSIONS.MENU.DELETE]: '删除菜单',
  
  [SYSTEM_PERMISSIONS.CONFIG.LIST]: '查看系统配置',
  [SYSTEM_PERMISSIONS.CONFIG.ADD]: '新增系统配置',
  [SYSTEM_PERMISSIONS.CONFIG.EDIT]: '编辑系统配置',
  [SYSTEM_PERMISSIONS.CONFIG.DELETE]: '删除系统配置',
  
  [SYSTEM_PERMISSIONS.LOG.LIST]: '查看日志列表',
  [SYSTEM_PERMISSIONS.LOG.VIEW]: '查看日志详情',
  [SYSTEM_PERMISSIONS.LOG.EXPORT]: '导出日志',
  [SYSTEM_PERMISSIONS.LOG.CLEAN]: '清理日志',
  [SYSTEM_PERMISSIONS.LOG.STATISTICS]: '查看日志统计'
} as const

// 角色描述映射
export const ROLE_DESCRIPTIONS = {
  [ROLES.SUPER_ADMIN]: '超级管理员',
  [ROLES.ADMIN]: '管理员',
  [ROLES.USER]: '普通用户'
} as const
