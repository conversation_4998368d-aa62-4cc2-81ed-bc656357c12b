/**
 * 角色管理API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/common'

// 角色查询参数
export interface RoleQueryParams {
  pageNum?: number
  pageSize?: number
  roleCode?: string
  roleName?: string
  status?: number
  startTime?: string
  endTime?: string
}

// 角色信息
export interface RoleInfo {
  id: number
  roleCode: string
  roleName: string
  description?: string
  status: number
  statusDesc?: string
  sortOrder?: number
  dataScope?: number
  dataScopeDesc?: string
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  permissions?: Array<{
    id: number
    menuName: string
    permission: string
  }>
}

// 角色创建请求
export interface RoleCreateRequest {
  roleCode: string
  roleName: string
  description?: string
  sortOrder?: number
  dataScope?: number
  permissionIds?: number[]
  remark?: string
}

// 角色更新请求
export interface RoleUpdateRequest {
  roleName?: string
  description?: string
  status?: number
  sortOrder?: number
  dataScope?: number
  permissionIds?: number[]
  remark?: string
}

// 权限分配请求
export interface RolePermissionAssignRequest {
  permissionIds: number[]
}

/**
 * 分页查询角色
 */
export const getRolePageApi = (params: RoleQueryParams) => {
  return http.get<PageResponse<RoleInfo>>('/system/role/page', { params })
}

/**
 * 获取角色列表
 */
export const getRoleListApi = (params: RoleQueryParams) => {
  return http.get<RoleInfo[]>('/system/role/list', { params })
}

/**
 * 获取所有启用的角色（用于用户分配）
 */
export const getEnabledRolesApi = () => {
  return http.get<RoleInfo[]>('/system/role/list', { 
    params: { status: 1 } 
  })
}

/**
 * 根据ID获取角色
 */
export const getRoleByIdApi = (id: number) => {
  return http.get<RoleInfo>(`/system/role/${id}`)
}

/**
 * 创建角色
 */
export const createRoleApi = (data: RoleCreateRequest) => {
  return http.post<RoleInfo>('/system/role', data)
}

/**
 * 更新角色
 */
export const updateRoleApi = (id: number, data: RoleUpdateRequest) => {
  return http.put<RoleInfo>(`/system/role/${id}`, data)
}

/**
 * 删除角色
 */
export const deleteRoleApi = (id: number) => {
  return http.delete<void>(`/system/role/${id}`)
}

/**
 * 批量删除角色
 */
export const batchDeleteRolesApi = (ids: number[]) => {
  return http.delete<void>('/system/role/batch', { data: ids })
}

/**
 * 启用角色
 */
export const enableRoleApi = (id: number) => {
  return http.put<void>(`/system/role/${id}/enable`)
}

/**
 * 禁用角色
 */
export const disableRoleApi = (id: number) => {
  return http.put<void>(`/system/role/${id}/disable`)
}

/**
 * 分配角色权限
 */
export const assignRolePermissionsApi = (id: number, data: RolePermissionAssignRequest) => {
  return http.put<void>(`/system/role/${id}/permissions`, data)
}

/**
 * 获取角色权限ID列表
 */
export const getRolePermissionIdsApi = (id: number) => {
  return http.get<number[]>(`/system/role/${id}/permissions`)
}

/**
 * 检查角色编码是否存在
 */
export const checkRoleCodeApi = (roleCode: string, excludeId?: number) => {
  return http.get<boolean>('/system/role/check/code', { 
    params: { roleCode, excludeId } 
  })
}

/**
 * 检查角色名称是否存在
 */
export const checkRoleNameApi = (roleName: string, excludeId?: number) => {
  return http.get<boolean>('/system/role/check/name', { 
    params: { roleName, excludeId } 
  })
}

/**
 * 导出角色数据
 */
export const exportRolesApi = (params: RoleQueryParams) => {
  return http.get<RoleInfo[]>('/system/role/export', { params })
}
