/**
 * 菜单管理API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/common'

// 菜单查询参数
export interface MenuQueryParams {
  menuName?: string
  status?: number
  menuType?: number
}

// 菜单信息
export interface MenuInfo {
  id: number
  parentId: number
  menuName: string
  menuTitle?: string
  path?: string
  component?: string
  icon?: string
  menuType: number
  menuTypeDesc?: string
  sortOrder?: number
  status: number
  statusDesc?: string
  visible?: number
  visibleDesc?: string
  permission?: string
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  children?: MenuInfo[]
}

// 菜单树形响应
export interface MenuTreeResponse {
  id: number
  parentId: number
  name: string
  title: string
  path: string
  component: string
  icon: string
  type: number
  sortOrder: number
  status: number
  visible: number
  permission: string
  children: MenuTreeResponse[]
}

// 菜单创建请求
export interface MenuCreateRequest {
  parentId: number
  menuName: string
  menuTitle?: string
  path?: string
  component?: string
  icon?: string
  menuType: number
  sortOrder?: number
  visible?: number
  permission?: string
  remark?: string
}

// 菜单更新请求
export interface MenuUpdateRequest {
  parentId?: number
  menuName?: string
  menuTitle?: string
  path?: string
  component?: string
  icon?: string
  menuType?: number
  sortOrder?: number
  status?: number
  visible?: number
  permission?: string
  remark?: string
}

/**
 * 获取菜单树形结构
 */
export const getMenuTreeApi = () => {
  return http.get<MenuInfo[]>('/system/menu/tree')
}

/**
 * 获取启用的菜单树形结构（用于路由生成）
 */
export const getEnabledMenuTreeApi = () => {
  return http.get<MenuTreeResponse[]>('/system/menu/tree/enabled')
}

/**
 * 获取菜单列表
 */
export const getMenuListApi = (params: MenuQueryParams) => {
  return http.get<MenuInfo[]>('/system/menu/list', { params })
}

/**
 * 根据ID获取菜单
 */
export const getMenuByIdApi = (id: number) => {
  return http.get<MenuInfo>(`/system/menu/${id}`)
}

/**
 * 创建菜单
 */
export const createMenuApi = (data: MenuCreateRequest) => {
  return http.post<MenuInfo>('/system/menu', data)
}

/**
 * 更新菜单
 */
export const updateMenuApi = (id: number, data: MenuUpdateRequest) => {
  return http.put<MenuInfo>(`/system/menu/${id}`, data)
}

/**
 * 删除菜单
 */
export const deleteMenuApi = (id: number) => {
  return http.delete<void>(`/system/menu/${id}`)
}

/**
 * 批量删除菜单
 */
export const batchDeleteMenusApi = (ids: number[]) => {
  return http.delete<void>('/system/menu/batch', { data: ids })
}

/**
 * 启用菜单
 */
export const enableMenuApi = (id: number) => {
  return http.put<void>(`/system/menu/${id}/enable`)
}

/**
 * 禁用菜单
 */
export const disableMenuApi = (id: number) => {
  return http.put<void>(`/system/menu/${id}/disable`)
}

/**
 * 调整菜单排序
 */
export const updateMenuSortApi = (id: number, sortOrder: number) => {
  return http.put<void>(`/system/menu/${id}/sort`, { sortOrder })
}

/**
 * 移动菜单位置
 */
export const moveMenuApi = (id: number, targetId: number, position: 'before' | 'after' | 'inner') => {
  return http.put<void>(`/system/menu/${id}/move`, { targetId, position })
}

/**
 * 根据父菜单ID获取子菜单列表
 */
export const getChildrenByParentIdApi = (parentId: number) => {
  return http.get<MenuInfo[]>(`/system/menu/children/${parentId}`)
}

/**
 * 获取菜单权限列表（用于角色权限分配）
 */
export const getMenuPermissionsApi = () => {
  return http.get<Array<{
    id: number
    menuName: string
    permission: string
    parentId: number
    children?: Array<{
      id: number
      menuName: string
      permission: string
    }>
  }>>('/system/menu/permissions')
}
