/**
 * 系统配置管理API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/common'

// 配置查询参数
export interface ConfigQueryParams {
  pageNum?: number
  pageSize?: number
  configKey?: string
  configType?: string
  status?: number
  startTime?: string
  endTime?: string
}

// 配置信息
export interface ConfigInfo {
  id: number
  configKey: string
  configValue: string
  configType: string
  configTypeDesc?: string
  description?: string
  isEncrypted: number
  isSystem: number
  status: number
  statusDesc?: string
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  showValue?: boolean // 前端用于控制加密值显示
}

// 配置创建请求
export interface ConfigCreateRequest {
  configKey: string
  configValue: string
  configType: string
  description?: string
  isEncrypted?: number
  remark?: string
}

// 配置更新请求
export interface ConfigUpdateRequest {
  configValue?: string
  configType?: string
  description?: string
  status?: number
  isEncrypted?: number
  remark?: string
}

// 配置导入请求
export interface ConfigImportRequest {
  file: File
  updateExisting?: boolean
}

/**
 * 分页查询配置
 */
export const getConfigPageApi = (params: ConfigQueryParams) => {
  return http.get<PageResponse<ConfigInfo>>('/system/config/page', { params })
}

/**
 * 获取配置列表
 */
export const getConfigListApi = (params: ConfigQueryParams) => {
  return http.get<ConfigInfo[]>('/system/config/list', { params })
}

/**
 * 根据ID获取配置
 */
export const getConfigByIdApi = (id: number) => {
  return http.get<ConfigInfo>(`/system/config/${id}`)
}

/**
 * 根据配置键获取配置
 */
export const getConfigByKeyApi = (configKey: string) => {
  return http.get<ConfigInfo>(`/system/config/key/${configKey}`)
}

/**
 * 创建配置
 */
export const createConfigApi = (data: ConfigCreateRequest) => {
  return http.post<ConfigInfo>('/system/config', data)
}

/**
 * 更新配置
 */
export const updateConfigApi = (id: number, data: ConfigUpdateRequest) => {
  return http.put<ConfigInfo>(`/system/config/${id}`, data)
}

/**
 * 删除配置
 */
export const deleteConfigApi = (id: number) => {
  return http.delete<void>(`/system/config/${id}`)
}

/**
 * 批量删除配置
 */
export const batchDeleteConfigsApi = (ids: number[]) => {
  return http.delete<void>('/system/config/batch', { data: ids })
}

/**
 * 启用配置
 */
export const enableConfigApi = (id: number) => {
  return http.put<void>(`/system/config/${id}/enable`)
}

/**
 * 禁用配置
 */
export const disableConfigApi = (id: number) => {
  return http.put<void>(`/system/config/${id}/disable`)
}

/**
 * 刷新配置缓存
 */
export const refreshConfigCacheApi = () => {
  return http.post<void>('/system/config/refresh-cache')
}

/**
 * 检查配置键是否存在
 */
export const checkConfigKeyApi = (configKey: string, excludeId?: number) => {
  return http.get<boolean>('/system/config/check/key', { 
    params: { configKey, excludeId } 
  })
}

/**
 * 导出配置
 */
export const exportConfigApi = (params: ConfigQueryParams) => {
  return http.get<Blob>('/system/config/export', { 
    params,
    responseType: 'blob'
  })
}

/**
 * 导入配置
 */
export const importConfigApi = (data: FormData) => {
  return http.post<{
    successCount: number
    failureCount: number
    failureList: Array<{
      configKey: string
      reason: string
    }>
  }>('/system/config/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取配置类型列表
 */
export const getConfigTypesApi = () => {
  return http.get<Array<{
    value: string
    label: string
    description: string
  }>>('/system/config/types')
}

/**
 * 获取配置模板
 */
export const getConfigTemplateApi = () => {
  return http.get<Blob>('/system/config/template', {
    responseType: 'blob'
  })
}

/**
 * 批量更新配置状态
 */
export const batchUpdateConfigStatusApi = (ids: number[], status: number) => {
  return http.put<void>('/system/config/batch/status', { ids, status })
}

/**
 * 获取配置统计信息
 */
export const getConfigStatsApi = () => {
  return http.get<{
    totalCount: number
    enabledCount: number
    disabledCount: number
    systemCount: number
    businessCount: number
    typeStats: Array<{
      type: string
      count: number
    }>
  }>('/system/config/stats')
}
