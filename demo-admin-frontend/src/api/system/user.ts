/**
 * 用户管理API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { ApiResponse, PageResponse } from '@/types/common'

// 用户查询参数
export interface UserQueryParams {
  pageNum?: number
  pageSize?: number
  username?: string
  realName?: string
  email?: string
  phone?: string
  status?: number
  deptId?: number
  startTime?: string
  endTime?: string
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  genderDesc?: string
  avatar?: string
  status: number
  statusDesc?: string
  deptId?: number
  lastLoginTime?: string
  lastLoginIp?: string
  loginFailCount?: number
  lockTime?: string
  passwordUpdateTime?: string
  remark?: string
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  roles?: Array<{
    id: number
    roleCode: string
    roleName: string
  }>
}

// 用户创建请求
export interface UserCreateRequest {
  username: string
  password: string
  confirmPassword?: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  status?: number
  deptId?: number
  roleIds?: number[]
  remark?: string
}

// 用户更新请求
export interface UserUpdateRequest {
  id: number
  realName?: string
  nickname?: string
  email?: string
  phone?: string
  gender?: number
  status?: number
  deptId?: number
  roleIds?: number[]
  remark?: string
}

// 密码重置请求
export interface UserPasswordResetRequest {
  newPassword: string
}

// 用户角色分配请求
export interface UserRoleAssignRequest {
  roleIds: number[]
}

/**
 * 分页查询用户
 */
export const getUserPageApi = (params: UserQueryParams) => {
  return http.get<PageResponse<UserInfo>>('/system/user/page', { params })
}

/**
 * 获取用户列表
 */
export const getUserListApi = (params: UserQueryParams) => {
  return http.get<UserInfo[]>('/system/user/list', { params })
}

/**
 * 根据ID获取用户
 */
export const getUserByIdApi = (id: number) => {
  return http.get<UserInfo>(`/system/user/${id}`)
}

/**
 * 创建用户
 */
export const createUserApi = (data: UserCreateRequest) => {
  return http.post<UserInfo>('/system/user', data)
}

/**
 * 更新用户
 */
export const updateUserApi = (id: number, data: UserUpdateRequest) => {
  return http.put<UserInfo>('/system/user', { ...data, id })
}

/**
 * 删除用户
 */
export const deleteUserApi = (id: number) => {
  return http.delete<void>(`/system/user/${id}`)
}

/**
 * 批量删除用户
 */
export const batchDeleteUsersApi = (ids: number[]) => {
  return http.delete<void>('/system/user/batch', { data: ids })
}

/**
 * 启用用户
 */
export const enableUserApi = (id: number) => {
  return http.put<void>(`/system/user/${id}/enable`)
}

/**
 * 禁用用户
 */
export const disableUserApi = (id: number) => {
  return http.put<void>(`/system/user/${id}/disable`)
}

/**
 * 重置用户密码
 */
export const resetUserPasswordApi = (id: number, data: UserPasswordResetRequest) => {
  return http.put<void>(`/system/user/${id}/password/reset`, data)
}

/**
 * 分配用户角色
 */
export const assignUserRolesApi = (id: number, data: UserRoleAssignRequest) => {
  return http.put<void>(`/system/user/${id}/roles`, data)
}

/**
 * 检查用户名是否存在
 */
export const checkUsernameApi = (username: string, excludeId?: number) => {
  return http.get<boolean>('/system/user/check/username', { 
    params: { username, excludeId } 
  })
}

/**
 * 检查邮箱是否存在
 */
export const checkEmailApi = (email: string, excludeId?: number) => {
  return http.get<boolean>('/system/user/check/email', { 
    params: { email, excludeId } 
  })
}

/**
 * 检查手机号是否存在
 */
export const checkPhoneApi = (phone: string, excludeId?: number) => {
  return http.get<boolean>('/system/user/check/phone', { 
    params: { phone, excludeId } 
  })
}

/**
 * 导出用户数据
 */
export const exportUsersApi = (params: UserQueryParams) => {
  return http.download<Blob>('/system/user/export', { 
    params
  })
}
