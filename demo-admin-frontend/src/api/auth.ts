/**
 * 认证相关API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { LoginForm, UserInfo, ApiResponse } from '@/types/common'

// 登录响应类型
export interface LoginResponse {
  token: string
  tokenType: string
  expiresIn: number
  userId: number
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  permissions: string[]
  roles: string[]
}

// 刷新Token请求类型
export interface RefreshTokenRequest {
  refreshToken: string
}

// 刷新Token响应类型
export interface RefreshTokenResponse {
  token: string
  refreshToken: string
  expiresIn: number
}

/**
 * 用户登录
 */
export const loginApi = (data: LoginForm) => {
  return http.post<LoginResponse>('/auth/login', data)
}

/**
 * 获取用户信息
 */
export const getUserInfoApi = () => {
  return http.get<UserInfo>('/auth/me')
}

/**
 * 用户登出
 */
export const logoutApi = () => {
  return http.post<void>('/auth/logout')
}

/**
 * 刷新Token
 */
export const refreshTokenApi = (data: RefreshTokenRequest) => {
  return http.post<RefreshTokenResponse>('/auth/refresh', data)
}

/**
 * 获取验证码
 */
export const getCaptchaApi = () => {
  return http.get<{ image: string; uuid: string }>('/auth/captcha')
}
