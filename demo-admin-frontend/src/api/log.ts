import request from '@/utils/request'

/**
 * 日志查询请求接口
 */
export interface LogQueryRequest {
  keyword?: string
  logType?: string
  userId?: number
  username?: string
  moduleName?: string
  operationType?: string
  requestUri?: string
  method?: string
  ip?: string
  success?: boolean
  level?: string
  exceptionType?: string
  browser?: string
  os?: string
  startTime?: string
  endTime?: string
  minExecutionTime?: number
  maxExecutionTime?: number
    userIds?: number[]
  moduleNames?: string[]
  operationTypes?: string[]
  ips?: string[]
  levels?: string[]
  sortField?: string
  sortOrder?: 'asc' | 'desc'
  pageNum?: number
  pageSize?: number
}

/**
 * 日志响应接口
 */
export interface LogResponse {
  id: number
  createTime: string
  logType: string
  userId?: number
  username?: string
  description?: string
  requestUri?: string
  method?: string
  ip?: string
  executionTime?: number
  success?: boolean
  errorMsg?: string
  moduleName?: string
  operationType?: string
  browser?: string
  os?: string
  level?: string
  exceptionType?: string
  threadName?: string
  }

/**
 * 分页响应接口
 */
export interface PageResult<T> {
  current: number
  size: number
  total: number
  records: T[]
}

/**
 * 日志统计响应接口
 */
export interface LogStatisticsResponse {
  totalLogCount: number
  exceptionLogCount: number
  performanceLogCount: number
  successLogCount: number
  failedLogCount: number
  slowQueryCount: number
  avgExecutionTime?: number
  minExecutionTime?: number
  maxExecutionTime?: number
  errorRate?: number
  successRate?: number
  logTypeStatistics: Array<{
    logType: string
    count: number
  }>
  userStatistics: Array<{
    username: string
    count: number
  }>
  moduleStatistics: Array<{
    moduleName: string
    count: number
  }>
  operationStatistics: Array<{
    operationType: string
    count: number
  }>
  performanceMetrics: {
    successCount: number
    failedCount: number
    slowQueryCount: number
    minExecutionTime: number
    maxExecutionTime: number
    totalCount: number
  }
  dateTrend: Array<{
    date: string
    count: number
  }>
  moduleDistribution: Array<{
    moduleName: string
    count: number
  }>
  userActivity: Array<{
    username: string
    count: number
  }>
  operationFrequency: Array<{
    operationType: string
    count: number
  }>
  performanceTrend: Array<{
    timestamp: string
    executionTime: number
  }>
}

/**
 * 日志API
 */
export const logApi = {
  /**
   * 分页查询日志列表
   */
  getLogPage(params: LogQueryRequest) {
    return request.get<PageResult<LogResponse>>('/logs/page', { params })
  },

  /**
   * 查询日志列表
   */
  getLogList(params: LogQueryRequest) {
    return request.get<LogResponse[]>('/logs/list', { params })
  },

  /**
   * 获取日志统计信息
   */
  getLogStatistics(params: LogQueryRequest) {
    return request.get<LogStatisticsResponse>('/logs/statistics', { params })
  },

  /**
   * 获取最近的日志
   */
  getRecentLogs(limit = 10) {
    return request.get<LogResponse[]>('/logs/recent', { params: { limit } })
  },

  /**
   * 获取异常日志
   */
  getExceptionLogs(params?: { startTime?: string; endTime?: string }) {
    return request.get<LogResponse[]>('/logs/exceptions', { params })
  },

  /**
   * 获取性能日志
   */
  getPerformanceLogs(params?: { startTime?: string; endTime?: string; threshold?: number }) {
    return request.get<LogResponse[]>('/logs/performance', { params })
  },

  /**
   * 获取慢查询日志
   */
  getSlowQueryLogs(params?: { threshold?: number; startTime?: string; endTime?: string }) {
    return request.get<LogResponse[]>('/logs/slow-queries', { params })
  },

  /**
   * 获取错误日志
   */
  getErrorLogs(params?: { startTime?: string; endTime?: string }) {
    return request.get<LogResponse[]>('/logs/errors', { params })
  },

  /**
   * 获取用户操作轨迹
   */
  getUserOperationTrail(userId: number, params?: { startTime?: string; endTime?: string }) {
    return request.get<LogResponse[]>(`/logs/user-trail/${userId}`, { params })
  },

  /**
   * 获取热门操作
   */
  getPopularOperations(params?: { startTime?: string; endTime?: string; limit?: number }) {
    return request.get<Array<{ operationType: string; count: number }>>('/logs/popular-operations', { params })
  },

  /**
   * 获取日志配置
   */
  getLogConfig() {
    return request.get<Record<string, any>>('/logs/config')
  },

  /**
   * 更新日志配置
   */
  updateLogConfig(config: Record<string, any>) {
    return request.put<boolean>('/logs/config', config)
  },

  /**
   * 清理过期日志
   */
  cleanLogs(beforeDate: string) {
    return request.delete<boolean>('/logs/clean', { params: { beforeDate } })
  },

  /**
   * 导出日志数据
   */
  exportLogs(params: LogQueryRequest) {
    return request.download<Blob>('/logs/export', { 
      params
    })
  }
}