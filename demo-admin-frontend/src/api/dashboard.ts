/**
 * 仪表盘API
 * <AUTHOR>
 */

import { http } from '@/utils/request'
import type { ApiResponse } from '@/types/common'

// 仪表盘统计信息
export interface DashboardStatistics {
  userCount: number
  roleCount: number
  menuCount: number
  onlineUserCount: number
  todayActiveUserCount: number
  systemInfo: SystemInfo
  recentActivities: RecentActivity[]
  userGrowthTrend: GrowthTrendItem[]
  systemLoad: SystemLoad
  statisticsTime: string
}

// 系统信息
export interface SystemInfo {
  systemVersion: string
  environment: string
  javaVersion: string
  jvmName: string
  osName: string
  osVersion: string
  serverTime: string
  memory: MemoryInfo
  database: string
  redis: string
}

// 内存信息
export interface MemoryInfo {
  total: string
  used: string
  free: string
  max: string
  usageRate: string
}

// 最近活动
export interface RecentActivity {
  username: string
  action: string
  time: string
}

// 增长趋势项
export interface GrowthTrendItem {
  date: string
  newUsers: number
  totalUsers: number
}

// 系统负载
export interface SystemLoad {
  cpuUsage: string
  memoryUsage: string
  diskUsage: string
  status: 'normal' | 'warning'
}

// 最近活动响应
export interface RecentActivityResponse {
  activities: RecentActivity[]
  total: number
}

/**
 * 获取仪表盘统计数据
 */
export const getDashboardStatisticsApi = () => {
  return http.get<DashboardStatistics>('/dashboard/statistics')
}

/**
 * 获取在线用户数量
 */
export const getOnlineUserCountApi = () => {
  return http.get<number>('/dashboard/online-users')
}

/**
 * 获取系统信息
 */
export const getSystemInfoApi = () => {
  return http.get<SystemInfo>('/dashboard/system-info')
}

/**
 * 获取最近活动
 */
export const getRecentActivityApi = (limit: number = 10) => {
  return http.get<RecentActivityResponse>('/dashboard/recent-activity', { 
    params: { limit } 
  })
}