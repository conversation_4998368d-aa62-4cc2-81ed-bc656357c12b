<!--
  侧边栏菜单项组件
  <AUTHOR>
-->

<template>
  <div v-if="!item.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="resolvePath(item.path)"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </template>
      
      <sidebar-item
        v-for="child in visibleChildren"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path)"
      />
    </el-sub-menu>
    
    <!-- 单个菜单项 -->
    <el-menu-item
      v-else
      :index="resolvePath(item.path)"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ item.meta?.title }}</span>
      </template>
    </el-menu-item>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

interface Props {
  item: RouteRecordRaw
  basePath: string
}

const props = defineProps<Props>()
const authStore = useAuthStore()

// 可见的子菜单
const visibleChildren = computed(() => {
  if (!props.item.children) return []
  
  return props.item.children.filter(child => {
    // 过滤隐藏的菜单
    if (child.meta?.hidden) return false
    
    // 检查权限
    if (child.meta?.permissions && child.meta.permissions.length > 0) {
      return authStore.hasAnyPermission(child.meta.permissions)
    }
    
    // 检查角色
    if (child.meta?.roles && child.meta.roles.length > 0) {
      return authStore.hasAnyRole(child.meta.roles)
    }
    
    return true
  })
})

// 是否有子菜单
const hasChildren = computed(() => {
  return visibleChildren.value.length > 0
})

// 解析路径
const resolvePath = (routePath: string) => {
  if (routePath.startsWith('/')) {
    return routePath
  }
  return `${props.basePath}/${routePath}`.replace(/\/+/g, '/')
}
</script>

<style lang="scss" scoped>
.el-menu-item,
.el-sub-menu {
  .el-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
  }
}
</style>
