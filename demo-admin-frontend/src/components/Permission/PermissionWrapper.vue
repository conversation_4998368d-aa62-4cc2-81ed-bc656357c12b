<!--
  权限控制包装组件
  <AUTHOR>
-->

<template>
  <div v-if="hasAuth">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermission } from '@/hooks/usePermission'

interface Props {
  // 权限标识
  permissions?: string | string[]
  // 角色标识
  roles?: string | string[]
  // 操作符：AND表示同时满足，OR表示满足任一
  operator?: 'AND' | 'OR'
}

const props = withDefaults(defineProps<Props>(), {
  permissions: () => [],
  roles: () => [],
  operator: 'AND'
})

const { checkAuth } = usePermission()

// 检查是否有权限
const hasAuth = computed(() => {
  const permissions = Array.isArray(props.permissions) 
    ? props.permissions 
    : props.permissions ? [props.permissions] : []
    
  const roles = Array.isArray(props.roles) 
    ? props.roles 
    : props.roles ? [props.roles] : []

  // 如果没有设置权限和角色，默认显示
  if (permissions.length === 0 && roles.length === 0) {
    return true
  }

  return checkAuth({
    permissions,
    roles,
    operator: props.operator
  })
})
</script>
