<!--
  应用根组件
  <AUTHOR>
-->

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 初始化应用
onMounted(() => {
  appStore.initApp()
})
</script>

<template>
  <div id="app">
    <!-- 全局加载遮罩 -->
    <div v-if="appStore.pageLoading" class="page-loading">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <span class="loading-text">加载中...</span>
    </div>

    <!-- 路由视图 -->
    <router-view />
  </div>
</template>

<style lang="scss">
#app {
  height: 100%;
  width: 100%;
}

.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-icon {
    font-size: 32px;
    color: #409eff;
    animation: rotate 2s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 14px;
    color: #606266;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
