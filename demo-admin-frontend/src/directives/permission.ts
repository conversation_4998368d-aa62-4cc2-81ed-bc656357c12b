/**
 * 权限控制指令
 * <AUTHOR>
 */

import type { App, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令 v-permission
 * 用法：v-permission="['system:user:add']" 或 v-permission="'system:user:add'"
 */
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value) {
      let hasPermission = false

      if (Array.isArray(value)) {
        // 数组形式：检查是否拥有任一权限
        hasPermission = authStore.hasAnyPermission(value)
      } else if (typeof value === 'string') {
        // 字符串形式：检查单个权限
        hasPermission = authStore.hasPermission(value)
      }

      if (!hasPermission) {
        // 没有权限则移除元素
        el.parentNode?.removeChild(el)
      }
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { value, oldValue } = binding

    // 如果权限值没有变化，不需要重新检查
    if (JSON.stringify(value) === JSON.stringify(oldValue)) {
      return
    }

    const authStore = useAuthStore()

    if (value) {
      let hasPermission = false

      if (Array.isArray(value)) {
        hasPermission = authStore.hasAnyPermission(value)
      } else if (typeof value === 'string') {
        hasPermission = authStore.hasPermission(value)
      }

      if (!hasPermission) {
        // 没有权限则隐藏元素
        el.style.display = 'none'
      } else {
        // 有权限则显示元素
        el.style.display = ''
      }
    }
  }
}

/**
 * 角色指令 v-role
 * 用法：v-role="['admin', 'user']" 或 v-role="'admin'"
 */
const role = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value) {
      let hasRole = false

      if (Array.isArray(value)) {
        // 数组形式：检查是否拥有任一角色
        hasRole = authStore.hasAnyRole(value)
      } else if (typeof value === 'string') {
        // 字符串形式：检查单个角色
        hasRole = authStore.hasRole(value)
      }

      if (!hasRole) {
        // 没有角色权限则移除元素
        el.parentNode?.removeChild(el)
      }
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { value, oldValue } = binding

    // 如果角色值没有变化，不需要重新检查
    if (JSON.stringify(value) === JSON.stringify(oldValue)) {
      return
    }

    const authStore = useAuthStore()

    if (value) {
      let hasRole = false

      if (Array.isArray(value)) {
        hasRole = authStore.hasAnyRole(value)
      } else if (typeof value === 'string') {
        hasRole = authStore.hasRole(value)
      }

      if (!hasRole) {
        // 没有角色权限则隐藏元素
        el.style.display = 'none'
      } else {
        // 有角色权限则显示元素
        el.style.display = ''
      }
    }
  }
}

/**
 * 权限和角色组合指令 v-auth
 * 用法：v-auth="{ permissions: ['system:user:add'], roles: ['admin'] }"
 */
const auth = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const authStore = useAuthStore()

    if (value && typeof value === 'object') {
      const { permissions, roles, operator = 'AND' } = value
      let hasPermission = true
      let hasRole = true

      // 检查权限
      if (permissions) {
        if (Array.isArray(permissions)) {
          hasPermission = authStore.hasAnyPermission(permissions)
        } else if (typeof permissions === 'string') {
          hasPermission = authStore.hasPermission(permissions)
        }
      }

      // 检查角色
      if (roles) {
        if (Array.isArray(roles)) {
          hasRole = authStore.hasAnyRole(roles)
        } else if (typeof roles === 'string') {
          hasRole = authStore.hasRole(roles)
        }
      }

      // 根据操作符判断最终权限
      let finalAuth = false
      if (operator === 'OR') {
        // OR：权限或角色任一满足即可
        finalAuth = hasPermission || hasRole
      } else {
        // AND：权限和角色都必须满足
        finalAuth = hasPermission && hasRole
      }

      if (!finalAuth) {
        // 没有权限则移除元素
        el.parentNode?.removeChild(el)
      }
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { value, oldValue } = binding

    // 如果权限值没有变化，不需要重新检查
    if (JSON.stringify(value) === JSON.stringify(oldValue)) {
      return
    }

    const authStore = useAuthStore()

    if (value && typeof value === 'object') {
      const { permissions, roles, operator = 'AND' } = value
      let hasPermission = true
      let hasRole = true

      // 检查权限
      if (permissions) {
        if (Array.isArray(permissions)) {
          hasPermission = authStore.hasAnyPermission(permissions)
        } else if (typeof permissions === 'string') {
          hasPermission = authStore.hasPermission(permissions)
        }
      }

      // 检查角色
      if (roles) {
        if (Array.isArray(roles)) {
          hasRole = authStore.hasAnyRole(roles)
        } else if (typeof roles === 'string') {
          hasRole = authStore.hasRole(roles)
        }
      }

      // 根据操作符判断最终权限
      let finalAuth = false
      if (operator === 'OR') {
        finalAuth = hasPermission || hasRole
      } else {
        finalAuth = hasPermission && hasRole
      }

      if (!finalAuth) {
        // 没有权限则隐藏元素
        el.style.display = 'none'
      } else {
        // 有权限则显示元素
        el.style.display = ''
      }
    }
  }
}

/**
 * 安装权限指令
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permission)
  app.directive('role', role)
  app.directive('auth', auth)
}

export default {
  permission,
  role,
  auth
}
