/**
 * 路由配置
 * <AUTHOR>
 */

import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import type { RouteMeta } from '@/types/common'

// 基础路由（不需要认证）
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hidden: true
    } as RouteMeta
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    } as RouteMeta
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '无权限访问',
      hidden: true
    } as RouteMeta
  },
  {
    path: '/debug/token',
    name: 'TokenDebug',
    component: () => import('@/views/debug/TokenDebug.vue'),
    meta: {
      title: 'Token诊断',
      hidden: true
    } as RouteMeta
  },
  {
    path: '/test/token-fix',
    name: 'TokenFixTest',
    component: () => import('@/views/test/TokenFixTest.vue'),
    meta: {
      title: 'Token修复测试',
      hidden: true
    } as RouteMeta
  }
]

// 需要认证的路由
const asyncRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layouts/DefaultLayout.vue'),
    redirect: '/dashboard',
    meta: {
      title: '首页',
      requiresAuth: true
    } as RouteMeta,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard',
          affix: true,
          requiresAuth: true
        } as RouteMeta
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '系统管理',
      icon: 'Setting',
      requiresAuth: true
    } as RouteMeta,
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/user/Index.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          permissions: ['system:user:list']
        } as RouteMeta
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/role/Index.vue'),
        meta: {
          title: '角色管理',
          icon: 'UserFilled',
          requiresAuth: true,
          permissions: ['system:role:query']
        } as RouteMeta
      },
      {
        path: 'menu',
        name: 'SystemMenu',
        component: () => import('@/views/system/menu/Index.vue'),
        meta: {
          title: '菜单管理',
          icon: 'Menu',
          requiresAuth: true,
          permissions: ['system:menu:list']
        } as RouteMeta
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config/Index.vue'),
        meta: {
          title: '系统设置',
          icon: 'Tools',
          requiresAuth: true,
          permissions: ['system:config:list']
        } as RouteMeta
      },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/views/log/Index.vue'),
        meta: {
          title: '日志管理',
          icon: 'Document',
          requiresAuth: true,
          permissions: ['system:log:list']
        } as RouteMeta
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/layouts/DefaultLayout.vue'),
    meta: {
      title: '个人中心',
      hidden: true,
      requiresAuth: true
    } as RouteMeta,
    children: [
      {
        path: 'index',
        name: 'ProfileIndex',
        component: () => import('@/views/profile/Index.vue'),
        meta: {
          title: '个人信息',
          requiresAuth: true
        } as RouteMeta
      }
    ]
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [...constantRoutes, ...asyncRoutes],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const appStore = useAppStore()

  // 设置页面加载状态
  appStore.setPageLoading(true)

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 检查权限
    if (to.meta.permissions && to.meta.permissions.length > 0) {
      const hasPermission = authStore.hasAnyPermission(to.meta.permissions)
      if (!hasPermission) {
        // 无权限，跳转到403页面
        next('/403')
        return
      }
    }

    // 检查角色
    if (to.meta.roles && to.meta.roles.length > 0) {
      const hasRole = authStore.hasAnyRole(to.meta.roles)
      if (!hasRole) {
        // 无角色权限，跳转到403页面
        next('/403')
        return
      }
    }
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

router.afterEach(() => {
  const appStore = useAppStore()
  appStore.setPageLoading(false)
})

// 404路由（必须放在最后）
router.addRoute({
  path: '/:pathMatch(.*)*',
  redirect: '/404'
})

export default router
