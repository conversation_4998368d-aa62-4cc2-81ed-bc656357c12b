<!--
  个人中心页面
  <AUTHOR>
-->

<template>
  <div class="profile-page">
    <el-row :gutter="20">
      <!-- 个人信息卡片 -->
      <el-col :xs="24" :md="8">
        <el-card shadow="hover" class="profile-card">
          <div class="profile-header">
            <el-avatar :size="80" :src="userInfo.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="profile-info">
              <h3 class="username">{{ userInfo.realName || userInfo.username }}</h3>
              <p class="role">{{ userInfo.roles?.join(', ') || '普通用户' }}</p>
            </div>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userInfo.loginCount || 0 }}</div>
              <div class="stat-label">登录次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userInfo.lastLoginTime) }}</div>
              <div class="stat-label">最后登录</div>
            </div>
          </div>
          
          <div class="profile-actions">
            <el-button type="primary" @click="handleEditProfile">
              编辑资料
            </el-button>
            <el-button @click="handleChangePassword">
              修改密码
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <!-- 详细信息 -->
      <el-col :xs="24" :md="16">
        <el-card shadow="hover" class="info-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
              <el-button type="primary" size="small" @click="handleEditProfile">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </div>
          </template>
          
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">用户名：</span>
              <span class="info-value">{{ userInfo.username }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">真实姓名：</span>
              <span class="info-value">{{ userInfo.realName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">昵称：</span>
              <span class="info-value">{{ userInfo.nickname || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">邮箱：</span>
              <span class="info-value">{{ userInfo.email || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">手机号：</span>
              <span class="info-value">{{ userInfo.phone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">性别：</span>
              <span class="info-value">{{ getGenderText(userInfo.gender) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">注册时间：</span>
              <span class="info-value">{{ formatDate(userInfo.createTime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后登录IP：</span>
              <span class="info-value">{{ userInfo.lastLoginIp || '-' }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 安全设置 -->
        <el-card shadow="hover" class="security-card">
          <template #header>
            <div class="card-header">
              <span>安全设置</span>
            </div>
          </template>
          
          <div class="security-list">
            <div class="security-item">
              <div class="security-info">
                <div class="security-title">登录密码</div>
                <div class="security-desc">用于登录系统的密码</div>
              </div>
              <el-button type="primary" @click="handleChangePassword">
                修改密码
              </el-button>
            </div>
            <div class="security-item">
              <div class="security-info">
                <div class="security-title">登录设备</div>
                <div class="security-desc">管理您的登录设备</div>
              </div>
              <el-button @click="handleManageDevices">
                管理设备
              </el-button>
            </div>
            <div class="security-item">
              <div class="security-info">
                <div class="security-title">操作日志</div>
                <div class="security-desc">查看您的操作记录</div>
              </div>
              <el-button @click="handleViewLogs">
                查看日志
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Edit } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { formatDate } from '@/utils/common'

const authStore = useAuthStore()

// 用户信息
const userInfo = ref({
  username: 'admin',
  realName: '管理员',
  nickname: 'Admin',
  email: '<EMAIL>',
  phone: '13800138000',
  gender: 1,
  avatar: '',
  roles: ['超级管理员'],
  createTime: '2025-01-01 00:00:00',
  lastLoginTime: '2025-01-27 12:00:00',
  lastLoginIp: '127.0.0.1',
  loginCount: 100
})

// 获取性别文本
const getGenderText = (gender: number) => {
  const genderMap: Record<number, string> = {
    0: '女',
    1: '男',
    2: '未知'
  }
  return genderMap[gender] || '未知'
}

// 编辑资料
const handleEditProfile = () => {
  ElMessage.info('编辑资料功能开发中...')
}

// 修改密码
const handleChangePassword = () => {
  ElMessage.info('修改密码功能开发中...')
}

// 管理设备
const handleManageDevices = () => {
  ElMessage.info('设备管理功能开发中...')
}

// 查看日志
const handleViewLogs = () => {
  ElMessage.info('操作日志功能开发中...')
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 这里可以调用API获取用户信息
    // const response = await getUserInfoApi()
    // userInfo.value = response.data
    
    // 暂时使用store中的用户信息
    if (authStore.userInfo) {
      Object.assign(userInfo.value, authStore.userInfo)
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.profile-page {
  .profile-card {
    margin-bottom: 20px;
    
    .profile-header {
      text-align: center;
      margin-bottom: 20px;
      
      .profile-info {
        margin-top: 16px;
        
        .username {
          font-size: 20px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .role {
          font-size: 14px;
          color: #909399;
          margin: 0;
        }
      }
    }
    
    .profile-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
      padding: 20px 0;
      border-top: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .profile-actions {
      display: flex;
      gap: 12px;
      
      .el-button {
        flex: 1;
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .info-list {
      .info-item {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          width: 120px;
          color: #606266;
          flex-shrink: 0;
        }
        
        .info-value {
          color: #303133;
          flex: 1;
        }
      }
    }
  }
  
  .security-card {
    .security-list {
      .security-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .security-info {
          .security-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .security-desc {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .profile-page {
    .profile-stats {
      .stat-item .stat-value {
        font-size: 16px;
      }
    }
    
    .profile-actions {
      flex-direction: column;
    }
    
    .security-item {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 12px;
    }
  }
}
</style>
