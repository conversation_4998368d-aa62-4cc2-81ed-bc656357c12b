<!--
  仪表盘页面
  <AUTHOR>
-->

<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">仪表盘</h1>
          <p class="page-description">欢迎使用 Demo Admin 后台管理系统</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Refresh" 
            :loading="loading"
            @click="refreshData"
          >
            刷新数据
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-icon user">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(dashboardData.userCount) }}</div>
            <div class="stat-label">用户总数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-icon role">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(dashboardData.roleCount) }}</div>
            <div class="stat-label">角色数量</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-icon menu">
            <el-icon><Menu /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(dashboardData.menuCount) }}</div>
            <div class="stat-label">菜单数量</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-icon online">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(dashboardData.onlineUserCount) }}</div>
            <div class="stat-label">在线用户</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-card class="quick-actions" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>快捷操作</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <div class="quick-action-item" @click="$router.push('/system/user')">
            <el-icon class="action-icon"><User /></el-icon>
            <div class="action-text">用户管理</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="quick-action-item" @click="$router.push('/system/role')">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <div class="action-text">角色管理</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="quick-action-item" @click="$router.push('/system/menu')">
            <el-icon class="action-icon"><Menu /></el-icon>
            <div class="action-text">菜单管理</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <div class="quick-action-item" @click="$router.push('/system/config')">
            <el-icon class="action-icon"><Tools /></el-icon>
            <div class="action-text">系统设置</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 系统信息 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <el-card class="system-info" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">{{ dashboardData.systemInfo.systemVersion || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行环境：</span>
              <span class="info-value">{{ dashboardData.systemInfo.environment || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Java版本：</span>
              <span class="info-value">{{ dashboardData.systemInfo.javaVersion || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">操作系统：</span>
              <span class="info-value">{{ dashboardData.systemInfo.osName || '-' }} {{ dashboardData.systemInfo.osVersion || '' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库：</span>
              <span class="info-value">{{ dashboardData.systemInfo.database || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">缓存：</span>
              <span class="info-value">{{ dashboardData.systemInfo.redis || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存使用：</span>
              <span class="info-value">{{ dashboardData.systemInfo.memory?.usageRate || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器时间：</span>
              <span class="info-value">{{ formatTime(dashboardData.systemInfo.serverTime) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card class="recent-activity" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          
          <div class="activity-list">
            <div v-if="dashboardData.recentActivities && dashboardData.recentActivities.length > 0">
              <div v-for="(activity, index) in dashboardData.recentActivities" :key="index" class="activity-item">
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
                <div class="activity-content">{{ activity.username }} {{ activity.action }}</div>
              </div>
            </div>
            <div v-else class="empty-activity">
              <span>暂无最近活动</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { User, UserFilled, Menu, Tools, Connection, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  getDashboardStatisticsApi, 
  getOnlineUserCountApi,
  getSystemInfoApi,
  getRecentActivityApi,
  type DashboardStatistics,
  type SystemInfo,
  type RecentActivity,
  type RecentActivityResponse
} from '@/api/dashboard'

// 响应式数据
const loading = ref(false)
const dashboardData = ref<DashboardStatistics>({
  userCount: 0,
  roleCount: 0,
  menuCount: 0,
  onlineUserCount: 0,
  todayActiveUserCount: 0,
  systemInfo: {
    systemVersion: '',
    environment: '',
    javaVersion: '',
    jvmName: '',
    osName: '',
    osVersion: '',
    serverTime: '',
    memory: {
      total: '0 MB',
      used: '0 MB',
      free: '0 MB',
      max: '0 MB',
      usageRate: '0%'
    },
    database: '',
    redis: ''
  },
  recentActivities: [],
  userGrowthTrend: [],
  systemLoad: {
    cpuUsage: '0%',
    memoryUsage: '0%',
    diskUsage: '0%',
    status: 'normal'
  },
  statisticsTime: ''
})

const autoRefreshTimer = ref<number>()

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const response = await getDashboardStatisticsApi()
    dashboardData.value = response.data
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('获取仪表盘数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await fetchDashboardData()
  ElMessage.success('数据已刷新')
}

// 格式化数字
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null || isNaN(num)) {
    return '0'
  }
  const number = Number(num)
  if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'k'
  }
  return number.toString()
}

// 格式化时间
const formatTime = (timeStr: string): string => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

// 自动刷新
const startAutoRefresh = () => {
  autoRefreshTimer.value = window.setInterval(() => {
    // 只刷新在线用户数
    getOnlineUserCountApi().then(response => {
      dashboardData.value.onlineUserCount = response.data
    }).catch(error => {
      console.error('刷新在线用户数失败:', error)
    })
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }
}

// 生命周期
onMounted(async () => {
  await fetchDashboardData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .dashboard-header {
    margin-bottom: 24px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-description {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          font-size: 24px;
          color: #ffffff;
        }
        
        &.user {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.role {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.menu {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.online {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 24px;
    
    .quick-action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
        transform: translateY(-2px);
      }
      
      .action-icon {
        font-size: 32px;
        color: #409eff;
        margin-bottom: 8px;
      }
      
      .action-text {
        font-size: 14px;
        color: #303133;
      }
    }
  }
  
  .card-header {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .system-info {
    margin-bottom: 24px;
    
    .info-list {
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          color: #606266;
        }
        
        .info-value {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
  
  .recent-activity {
    .activity-list {
      .activity-item {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-time {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .activity-content {
          font-size: 14px;
          color: #303133;
        }
      }
      
      .empty-activity {
        text-align: center;
        padding: 40px 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .stats-cards .stat-card {
      margin-bottom: 16px;
    }
    
    .quick-actions .quick-action-item {
      margin-bottom: 16px;
    }
  }
}
</style>
