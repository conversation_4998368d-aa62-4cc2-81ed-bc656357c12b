<!--
  系统设置页面
  <AUTHOR>
-->

<template>
  <div class="config-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
          <el-button
            type="primary"
            @click="handleAdd"
            v-permission="'system:config:add'"
          >
            <el-icon><Plus /></el-icon>
            新增配置
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="配置键">
          <el-input v-model="queryForm.configKey" placeholder="请输入配置键" clearable />
        </el-form-item>
        <el-form-item label="配置类型">
          <el-select v-model="queryForm.configType" placeholder="请选择配置类型" clearable>
            <el-option label="系统配置" value="system" />
            <el-option label="业务配置" value="business" />
            <el-option label="界面配置" value="ui" />
            <el-option label="安全配置" value="security" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleImport" v-permission="'system:config:import'">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button type="warning" @click="handleExport" v-permission="'system:config:export'">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="configKey" label="配置键" width="200" show-overflow-tooltip />
        <el-table-column prop="configValue" label="配置值" width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="config-value-cell">
              <span v-if="!row.isEncrypted">{{ row.configValue }}</span>
              <span v-else class="encrypted-value">******</span>
              <el-button
                v-if="row.isEncrypted"
                size="small"
                text
                @click="toggleValueVisibility(row)"
                class="toggle-btn"
              >
                <el-icon><View v-if="!row.showValue" /><Hide v-else /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="configType" label="配置类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.configType)">
              {{ getTypeText(row.configType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="isEncrypted" label="加密" width="80">
          <template #default="{ row }">
            <el-tag :type="row.isEncrypted === 1 ? 'warning' : 'info'" size="small">
              {{ row.isEncrypted === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isSystem" label="系统内置" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isSystem === 1 ? 'warning' : 'success'" size="small">
              {{ row.isSystem === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              v-permission="'system:config:edit'"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-permission="'system:config:edit'"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleView(row)"
              v-permission="'system:config:list'"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              :disabled="row.isSystem === 1"
              @click="handleDelete(row)"
              v-permission="'system:config:delete'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRows.length > 0">
        <span>已选择 {{ selectedRows.length }} 项</span>
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          v-permission="'system:config:delete'"
        >
          批量删除
        </el-button>
        <el-button
          type="warning"
          size="small"
          @click="handleBatchExport"
          v-permission="'system:config:export'"
        >
          批量导出
        </el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 配置表单弹窗 -->
    <ConfigFormDialog
      v-model:visible="formDialogVisible"
      :form-data="currentConfig"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 配置详情弹窗 -->
    <ConfigDetailDialog
      v-model:visible="detailDialogVisible"
      :config-data="currentConfig"
    />

    <!-- 导入弹窗 -->
    <ConfigImportDialog
      v-model:visible="importDialogVisible"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Upload, Download, View, Hide } from '@element-plus/icons-vue'
import { getConfigPageApi, deleteConfigApi, enableConfigApi, disableConfigApi, exportConfigApi } from '@/api/system/config'
import type { ConfigInfo, ConfigQueryParams } from '@/api/system/config'
import ConfigFormDialog from './components/ConfigFormDialog.vue'
import ConfigDetailDialog from './components/ConfigDetailDialog.vue'
import ConfigImportDialog from './components/ConfigImportDialog.vue'

// 查询表单
const queryForm = reactive<ConfigQueryParams>({
  configKey: '',
  configType: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
})

// 表格数据
const tableData = ref<ConfigInfo[]>([])
const loading = ref(false)
const selectedRows = ref<ConfigInfo[]>([])

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 弹窗控制
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const importDialogVisible = ref(false)
const currentConfig = ref<Partial<ConfigInfo>>({})
const isEdit = ref(false)

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    system: 'primary',
    business: 'success',
    ui: 'warning',
    security: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    system: '系统配置',
    business: '业务配置',
    ui: '界面配置',
    security: '安全配置'
  }
  return typeMap[type] || '未知'
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  queryForm.pageNum = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, {
    configKey: '',
    configType: '',
    status: undefined,
    pageNum: 1,
    pageSize: 10
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  currentConfig.value = {}
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: ConfigInfo) => {
  currentConfig.value = { ...row }
  isEdit.value = true
  formDialogVisible.value = true
}

// 查看详情
const handleView = (row: ConfigInfo) => {
  currentConfig.value = { ...row }
  detailDialogVisible.value = true
}

// 切换值显示状态
const toggleValueVisibility = (row: ConfigInfo) => {
  row.showValue = !row.showValue
}

// 状态切换
const handleStatusChange = async (row: ConfigInfo) => {
  try {
    if (row.status === 1) {
      await enableConfigApi(row.id)
      ElMessage.success('配置启用成功')
    } else {
      await disableConfigApi(row.id)
      ElMessage.success('配置禁用成功')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error('状态切换失败')
  }
}

// 删除
const handleDelete = async (row: ConfigInfo) => {
  if (row.isSystem === 1) {
    ElMessage.warning('系统内置配置不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${row.configKey}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteConfigApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const systemConfigs = selectedRows.value.filter(row => row.isSystem === 1)
  if (systemConfigs.length > 0) {
    ElMessage.warning('选中的配置中包含系统内置配置，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个配置吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用批量删除API
    ElMessage.success('批量删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出
const handleExport = async () => {
  try {
    await exportConfigApi(queryForm)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量导出
const handleBatchExport = async () => {
  try {
    const ids = selectedRows.value.map(row => row.id)
    // 这里应该调用批量导出API
    ElMessage.success('批量导出成功')
  } catch (error) {
    ElMessage.error('批量导出失败')
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  formDialogVisible.value = false
  fetchData()
}

// 导入成功回调
const handleImportSuccess = () => {
  importDialogVisible.value = false
  fetchData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  fetchData()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getConfigPageApi(queryForm)
    tableData.value = response.data.records
    pagination.total = response.data.total
    pagination.current = response.data.current
    pagination.size = response.data.size
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.config-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-form {
    margin-bottom: 20px;
  }

  .config-value-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .encrypted-value {
      color: #909399;
      font-style: italic;
    }

    .toggle-btn {
      padding: 2px;
      min-height: auto;
    }
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;

    span {
      color: #606266;
      font-size: 14px;
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}
</style>
