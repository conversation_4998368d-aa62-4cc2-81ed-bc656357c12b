<!--
  配置表单弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    :title="isEdit ? '编辑配置' : '新增配置'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="配置键" prop="configKey">
        <el-input
          v-model="form.configKey"
          placeholder="请输入配置键，如：system.title"
          maxlength="100"
          show-word-limit
          :disabled="isEdit"
        />
      </el-form-item>

      <el-form-item label="配置值" prop="configValue">
        <el-input
          v-model="form.configValue"
          :type="form.isEncrypted ? 'password' : 'text'"
          placeholder="请输入配置值"
          maxlength="1000"
          show-word-limit
          :show-password="form.isEncrypted === 1"
        />
      </el-form-item>

      <el-form-item label="配置类型" prop="configType">
        <el-select v-model="form.configType" placeholder="请选择配置类型" style="width: 100%">
          <el-option
            v-for="type in configTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <span>{{ type.label }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ type.description }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="配置描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入配置描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="是否加密" prop="isEncrypted">
        <el-radio-group v-model="form.isEncrypted">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>加密配置的值在列表中将以 ****** 显示</span>
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { createConfigApi, updateConfigApi, checkConfigKeyApi, getConfigTypesApi } from '@/api/system/config'
import type { ConfigInfo, ConfigCreateRequest, ConfigUpdateRequest } from '@/api/system/config'

// Props
interface Props {
  visible: boolean
  formData: Partial<ConfigInfo>
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: () => ({}),
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const configTypes = ref<Array<{ value: string; label: string; description: string }>>([])

// 表单数据
const form = reactive<ConfigCreateRequest & { status?: number }>({
  configKey: '',
  configValue: '',
  configType: '',
  description: '',
  isEncrypted: 0,
  status: 1,
  remark: ''
})

// 表单验证规则
const rules: FormRules = {
  configKey: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { min: 2, max: 100, message: '配置键长度在 2 到 100 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9._-]*$/, message: '配置键只能包含字母、数字、点、下划线和横线，且以字母开头', trigger: 'blur' },
    { validator: validateConfigKey, trigger: 'blur' }
  ],
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' },
    { max: 1000, message: '配置值不能超过1000个字符', trigger: 'blur' }
  ],
  configType: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '配置描述不能超过200个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
}

// 配置键验证
async function validateConfigKey(rule: any, value: string, callback: any) {
  if (!value) {
    callback()
    return
  }

  try {
    const excludeId = props.isEdit ? (props.formData.id as number) : undefined
    const response = await checkConfigKeyApi(value, excludeId)
    if (response.data) {
      callback(new Error('配置键已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        resetForm()
        if (props.isEdit && props.formData) {
          Object.assign(form, props.formData)
        }
      })
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载配置类型
const loadConfigTypes = async () => {
  try {
    const response = await getConfigTypesApi()
    configTypes.value = response.data
  } catch (error) {
    // 使用默认配置类型
    configTypes.value = [
      { value: 'system', label: '系统配置', description: '系统核心配置' },
      { value: 'business', label: '业务配置', description: '业务相关配置' },
      { value: 'ui', label: '界面配置', description: '界面显示配置' },
      { value: 'security', label: '安全配置', description: '安全相关配置' }
    ]
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    configKey: '',
    configValue: '',
    configType: '',
    description: '',
    isEncrypted: 0,
    status: 1,
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (props.isEdit) {
      // 编辑配置
      const updateData: ConfigUpdateRequest = {
        configValue: form.configValue,
        configType: form.configType,
        description: form.description,
        status: form.status,
        isEncrypted: form.isEncrypted,
        remark: form.remark
      }
      await updateConfigApi(props.formData.id as number, updateData)
      ElMessage.success('配置更新成功')
    } else {
      // 新增配置
      const createData: ConfigCreateRequest = {
        configKey: form.configKey,
        configValue: form.configValue,
        configType: form.configType,
        description: form.description,
        isEncrypted: form.isEncrypted,
        remark: form.remark
      }
      await createConfigApi(createData)
      ElMessage.success('配置创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 组件挂载时加载配置类型
onMounted(() => {
  loadConfigTypes()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;

  .el-icon {
    font-size: 14px;
  }
}
</style>
