<!--
  配置详情弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    title="配置详情"
    v-model="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="config-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="配置键" :span="2">
          <el-tag type="primary">{{ configData.configKey }}</el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="配置值" :span="2">
          <div class="config-value">
            <span v-if="!configData.isEncrypted || showValue">{{ configData.configValue }}</span>
            <span v-else class="encrypted-value">******</span>
            <el-button
              v-if="configData.isEncrypted"
              size="small"
              text
              @click="toggleValueVisibility"
              class="toggle-btn"
            >
              <el-icon><View v-if="!showValue" /><Hide v-else /></el-icon>
            </el-button>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="配置类型">
          <el-tag :type="getTypeTagType(configData.configType)">
            {{ getTypeText(configData.configType) }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="状态">
          <el-tag :type="configData.status === 1 ? 'success' : 'danger'">
            {{ configData.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="是否加密">
          <el-tag :type="configData.isEncrypted === 1 ? 'warning' : 'info'" size="small">
            {{ configData.isEncrypted === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="系统内置">
          <el-tag :type="configData.isSystem === 1 ? 'warning' : 'success'" size="small">
            {{ configData.isSystem === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="配置描述" :span="2">
          {{ configData.description || '暂无描述' }}
        </el-descriptions-item>

        <el-descriptions-item label="备注" :span="2">
          {{ configData.remark || '暂无备注' }}
        </el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ configData.createTime }}
        </el-descriptions-item>

        <el-descriptions-item label="更新时间">
          {{ configData.updateTime }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 配置使用示例 -->
      <div class="usage-example" v-if="configData.configKey">
        <h4>使用示例</h4>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="Java" name="java">
            <el-input
              type="textarea"
              :value="getJavaExample()"
              readonly
              :rows="4"
              class="code-example"
            />
          </el-tab-pane>
          <el-tab-pane label="JavaScript" name="javascript">
            <el-input
              type="textarea"
              :value="getJavaScriptExample()"
              readonly
              :rows="4"
              class="code-example"
            />
          </el-tab-pane>
          <el-tab-pane label="API" name="api">
            <el-input
              type="textarea"
              :value="getApiExample()"
              readonly
              :rows="4"
              class="code-example"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="copyConfigKey">
          <el-icon><CopyDocument /></el-icon>
          复制配置键
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Hide, CopyDocument } from '@element-plus/icons-vue'
import type { ConfigInfo } from '@/api/system/config'

// Props
interface Props {
  visible: boolean
  configData: Partial<ConfigInfo>
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  configData: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const showValue = ref(false)
const activeTab = ref('java')

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      showValue.value = false
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    system: 'primary',
    business: 'success',
    ui: 'warning',
    security: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    system: '系统配置',
    business: '业务配置',
    ui: '界面配置',
    security: '安全配置'
  }
  return typeMap[type] || '未知'
}

// 切换值显示状态
const toggleValueVisibility = () => {
  showValue.value = !showValue.value
}

// 获取Java使用示例
const getJavaExample = () => {
  return `// 在Spring Boot中使用
@Value("\${${props.configData.configKey}}")
private String configValue;

// 或者使用ConfigService
String value = configService.getConfig("${props.configData.configKey}");`
}

// 获取JavaScript使用示例
const getJavaScriptExample = () => {
  return `// 在前端中使用
import { getConfigByKeyApi } from '@/api/system/config'

const config = await getConfigByKeyApi('${props.configData.configKey}')
console.log(config.data.configValue)`
}

// 获取API使用示例
const getApiExample = () => {
  return `# 获取配置值
GET /api/system/config/key/${props.configData.configKey}

# 响应示例
{
  "code": 200,
  "message": "success",
  "data": {
    "configKey": "${props.configData.configKey}",
    "configValue": "${props.configData.configValue}"
  }
}`
}

// 复制配置键
const copyConfigKey = async () => {
  try {
    await navigator.clipboard.writeText(props.configData.configKey || '')
    ElMessage.success('配置键已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = props.configData.configKey || ''
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('配置键已复制到剪贴板')
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.config-detail {
  .config-value {
    display: flex;
    align-items: center;
    gap: 8px;

    .encrypted-value {
      color: #909399;
      font-style: italic;
    }

    .toggle-btn {
      padding: 2px;
      min-height: auto;
    }
  }

  .usage-example {
    margin-top: 20px;

    h4 {
      margin-bottom: 15px;
      color: #303133;
      font-size: 16px;
    }

    .code-example {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      
      :deep(.el-textarea__inner) {
        background-color: #f5f7fa;
        border: 1px solid #e4e7ed;
        color: #606266;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
