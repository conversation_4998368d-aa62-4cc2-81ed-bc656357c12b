<!--
  配置导入弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    title="导入配置"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-dialog">
      <!-- 导入说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
        class="import-tips"
      >
        <template #default>
          <ul>
            <li>支持Excel (.xlsx) 和 CSV (.csv) 格式文件</li>
            <li>文件大小不能超过 10MB</li>
            <li>必须包含：配置键、配置值、配置类型、配置描述等字段</li>
            <li>配置键不能重复，重复时可选择是否覆盖</li>
          </ul>
        </template>
      </el-alert>

      <!-- 文件上传 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          :accept="'.xlsx,.csv'"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          drag
          class="upload-dragger"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/csv 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 导入选项 -->
      <div class="import-options">
        <el-form :model="importForm" label-width="120px">
          <el-form-item label="重复处理">
            <el-radio-group v-model="importForm.updateExisting">
              <el-radio :label="false">跳过重复项</el-radio>
              <el-radio :label="true">覆盖重复项</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="导入模式">
            <el-radio-group v-model="importForm.importMode">
              <el-radio label="validate">仅验证</el-radio>
              <el-radio label="import">直接导入</el-radio>
            </el-radio-group>
            <div class="form-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>仅验证模式会检查数据格式但不会实际导入</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 导入结果 -->
      <div class="import-result" v-if="importResult">
        <el-alert
          :title="getResultTitle()"
          :type="getResultType()"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="result-stats">
              <p>成功：{{ importResult.successCount }} 条</p>
              <p>失败：{{ importResult.failureCount }} 条</p>
            </div>
            
            <!-- 失败详情 -->
            <div v-if="importResult.failureList && importResult.failureList.length > 0" class="failure-details">
              <el-divider content-position="left">失败详情</el-divider>
              <el-table :data="importResult.failureList" size="small" max-height="200">
                <el-table-column prop="configKey" label="配置键" width="200" />
                <el-table-column prop="reason" label="失败原因" />
              </el-table>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="downloadTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
        <el-button 
          type="primary" 
          @click="handleImport" 
          :loading="importing"
          :disabled="!selectedFile"
        >
          {{ importForm.importMode === 'validate' ? '验证' : '导入' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Download, InfoFilled } from '@element-plus/icons-vue'
import { importConfigApi, getConfigTemplateApi } from '@/api/system/config'
import type { UploadFile, UploadInstance } from 'element-plus'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const uploadRef = ref<UploadInstance>()
const importing = ref(false)
const selectedFile = ref<File | null>(null)

// 导入表单
const importForm = reactive({
  updateExisting: false,
  importMode: 'import'
})

// 导入结果
const importResult = ref<{
  successCount: number
  failureCount: number
  failureList: Array<{
    configKey: string
    reason: string
  }>
} | null>(null)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      resetDialog()
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置弹窗
const resetDialog = () => {
  selectedFile.value = null
  importResult.value = null
  importForm.updateExisting = false
  importForm.importMode = 'import'
  uploadRef.value?.clearFiles()
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'text/csv' ||
                     file.name.endsWith('.xlsx') ||
                     file.name.endsWith('.csv')
  
  if (!isValidType) {
    ElMessage.error('只能上传 Excel 或 CSV 格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传
}

// 文件选择变化
const handleFileChange = (file: UploadFile) => {
  if (file.raw) {
    selectedFile.value = file.raw
    importResult.value = null
  }
}

// 文件移除
const handleFileRemove = () => {
  selectedFile.value = null
  importResult.value = null
}

// 下载模板
const downloadTemplate = async () => {
  try {
    const response = await getConfigTemplateApi()
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '配置导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
  }
}

// 执行导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  try {
    importing.value = true
    
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('updateExisting', String(importForm.updateExisting))
    formData.append('importMode', importForm.importMode)

    const response = await importConfigApi(formData)
    importResult.value = response.data

    if (importForm.importMode === 'validate') {
      ElMessage.success('数据验证完成')
    } else {
      if (response.data.failureCount === 0) {
        ElMessage.success(`导入成功！共导入 ${response.data.successCount} 条配置`)
        emit('success')
      } else {
        ElMessage.warning(`导入完成！成功 ${response.data.successCount} 条，失败 ${response.data.failureCount} 条`)
      }
    }
  } catch (error) {
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 获取结果标题
const getResultTitle = () => {
  if (!importResult.value) return ''
  
  if (importForm.importMode === 'validate') {
    return '验证结果'
  }
  
  if (importResult.value.failureCount === 0) {
    return '导入成功'
  } else if (importResult.value.successCount === 0) {
    return '导入失败'
  } else {
    return '导入完成（部分失败）'
  }
}

// 获取结果类型
const getResultType = () => {
  if (!importResult.value) return 'info'
  
  if (importResult.value.failureCount === 0) {
    return 'success'
  } else if (importResult.value.successCount === 0) {
    return 'error'
  } else {
    return 'warning'
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.import-dialog {
  .import-tips {
    margin-bottom: 20px;

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 5px;
      }
    }
  }

  .upload-section {
    margin-bottom: 20px;

    .upload-dragger {
      width: 100%;
    }
  }

  .import-options {
    margin-bottom: 20px;

    .form-tip {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 4px;
      font-size: 12px;
      color: #909399;

      .el-icon {
        font-size: 14px;
      }
    }
  }

  .import-result {
    .result-stats {
      margin-bottom: 10px;

      p {
        margin: 5px 0;
      }
    }

    .failure-details {
      margin-top: 15px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
