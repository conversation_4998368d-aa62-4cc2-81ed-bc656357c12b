<!--
  角色管理页面
  <AUTHOR>
-->

<template>
  <div class="role-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <el-button
            type="primary"
            @click="handleAdd"
            v-permission="'system:role:add'"
          >
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="角色名称">
          <el-input v-model="queryForm.roleName" placeholder="请输入角色名称" clearable />
        </el-form-item>
        <el-form-item label="角色编码">
          <el-input v-model="queryForm.roleCode" placeholder="请输入角色编码" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="roleName" label="角色名称" />
        <el-table-column prop="roleCode" label="角色编码" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              v-permission="'system:role:edit'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-permission="'system:role:edit'"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handlePermission(row)"
              v-permission="'system:role:permission'"
            >
              权限
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              v-permission="'system:role:delete'"
              :disabled="row.isSystem === 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 角色表单弹窗 -->
    <RoleFormDialog
      v-model:visible="formDialogVisible"
      :form-data="currentRole"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 权限分配弹窗 -->
    <RolePermissionDialog
      v-model:visible="permissionDialogVisible"
      :role-data="currentRole"
      @success="handlePermissionSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { getRolePageApi, deleteRoleApi, enableRoleApi, disableRoleApi } from '@/api/system/role'
import type { RoleInfo, RoleQueryParams } from '@/api/system/role'
import RoleFormDialog from './components/RoleFormDialog.vue'
import RolePermissionDialog from './components/RolePermissionDialog.vue'

// 查询表单
const queryForm = reactive<RoleQueryParams>({
  roleName: '',
  roleCode: '',
  status: undefined,
  pageNum: 1,
  pageSize: 10
})

// 表格数据
const tableData = ref<RoleInfo[]>([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 弹窗控制
const formDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const currentRole = ref<Partial<RoleInfo>>({})
const isEdit = ref(false)

// 搜索
const handleSearch = () => {
  pagination.current = 1
  queryForm.pageNum = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, {
    roleName: '',
    roleCode: '',
    status: undefined,
    pageNum: 1,
    pageSize: 10
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  currentRole.value = {}
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: RoleInfo) => {
  currentRole.value = { ...row }
  isEdit.value = true
  formDialogVisible.value = true
}

// 权限设置
const handlePermission = (row: RoleInfo) => {
  currentRole.value = { ...row }
  permissionDialogVisible.value = true
}

// 状态切换
const handleStatusChange = async (row: RoleInfo) => {
  try {
    if (row.status === 1) {
      await enableRoleApi(row.id)
      ElMessage.success('角色启用成功')
    } else {
      await disableRoleApi(row.id)
      ElMessage.success('角色禁用成功')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error('状态切换失败')
  }
}

// 删除
const handleDelete = async (row: RoleInfo) => {
  if (row.isSystem === 1) {
    ElMessage.warning('系统内置角色不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.roleName}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteRoleApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  formDialogVisible.value = false
  fetchData()
}

// 权限分配成功回调
const handlePermissionSuccess = () => {
  permissionDialogVisible.value = false
  fetchData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  queryForm.pageSize = size
  fetchData()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  queryForm.pageNum = current
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getRolePageApi(queryForm)
    tableData.value = response.data.records
    pagination.total = response.data.total
    pagination.current = response.data.current
    pagination.size = response.data.size
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.role-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-form {
    margin-bottom: 20px;
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}
</style>
