<!--
  角色表单弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    :title="isEdit ? '编辑角色' : '新增角色'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="form.roleName"
          placeholder="请输入角色名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="角色编码" prop="roleCode">
        <el-input
          v-model="form.roleCode"
          placeholder="请输入角色编码"
          maxlength="50"
          show-word-limit
          :disabled="isEdit"
        />
      </el-form-item>

      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入角色描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="0"
          :max="999"
          placeholder="请输入排序"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="数据权限" prop="dataScope">
        <el-select v-model="form.dataScope" placeholder="请选择数据权限范围" style="width: 100%">
          <el-option label="全部数据" :value="1" />
          <el-option label="本部门及以下数据" :value="2" />
          <el-option label="本部门数据" :value="3" />
          <el-option label="仅本人数据" :value="4" />
          <el-option label="自定义数据" :value="5" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="2"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createRoleApi, updateRoleApi, checkRoleCodeApi } from '@/api/system/role'
import type { RoleInfo, RoleCreateRequest, RoleUpdateRequest } from '@/api/system/role'

// Props
interface Props {
  visible: boolean
  formData: Partial<RoleInfo>
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: () => ({}),
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 表单数据
const form = reactive<RoleCreateRequest & { status?: number }>({
  roleName: '',
  roleCode: '',
  description: '',
  sortOrder: 0,
  dataScope: 1,
  status: 1,
  remark: ''
})

// 表单验证规则
const rules: FormRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  roleCode: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '角色编码只能包含大写字母、数字和下划线，且以大写字母或下划线开头', trigger: 'blur' },
    { validator: validateRoleCode, trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '角色描述不能超过200个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 999, message: '排序值在 0 到 999 之间', trigger: 'blur' }
  ],
  dataScope: [
    { required: true, message: '请选择数据权限范围', trigger: 'change' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
}

// 角色编码验证
async function validateRoleCode(rule: any, value: string, callback: any) {
  if (!value) {
    callback()
    return
  }

  try {
    const excludeId = props.isEdit ? (props.formData.id as number) : undefined
    const response = await checkRoleCodeApi(value, excludeId)
    if (response.data) {
      callback(new Error('角色编码已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        resetForm()
        if (props.isEdit && props.formData) {
          Object.assign(form, props.formData)
        }
      })
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    roleName: '',
    roleCode: '',
    description: '',
    sortOrder: 0,
    dataScope: 1,
    status: 1,
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (props.isEdit) {
      // 编辑角色
      const updateData: RoleUpdateRequest = {
        roleName: form.roleName,
        description: form.description,
        sortOrder: form.sortOrder,
        dataScope: form.dataScope,
        status: form.status,
        remark: form.remark
      }
      await updateRoleApi(props.formData.id as number, updateData)
      ElMessage.success('角色更新成功')
    } else {
      // 新增角色
      const createData: RoleCreateRequest = {
        roleName: form.roleName,
        roleCode: form.roleCode,
        description: form.description,
        sortOrder: form.sortOrder,
        dataScope: form.dataScope,
        remark: form.remark
      }
      await createRoleApi(createData)
      ElMessage.success('角色创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
