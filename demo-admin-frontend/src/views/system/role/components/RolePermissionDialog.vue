<!--
  角色权限分配弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    title="分配权限"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="permission-dialog">
      <div class="role-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="角色名称">{{ roleData.roleName }}</el-descriptions-item>
          <el-descriptions-item label="角色编码">{{ roleData.roleCode }}</el-descriptions-item>
          <el-descriptions-item label="角色描述" :span="2">{{ roleData.description || '暂无描述' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="permission-tree">
        <div class="tree-header">
          <span>权限分配</span>
          <div class="tree-actions">
            <el-button size="small" @click="handleExpandAll">
              {{ isExpandAll ? '收起全部' : '展开全部' }}
            </el-button>
            <el-button size="small" @click="handleCheckAll">
              {{ isCheckAll ? '取消全选' : '全选' }}
            </el-button>
          </div>
        </div>

        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="treeProps"
          :default-checked-keys="checkedKeys"
          :default-expanded-keys="expandedKeys"
          show-checkbox
          node-key="id"
          check-strictly
          class="permission-tree-component"
          @check="handleTreeCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon v-if="data.icon" class="node-icon">
                <component :is="data.icon" />
              </el-icon>
              <span class="node-label">{{ data.menuName }}</span>
              <el-tag v-if="data.permission" size="small" type="info" class="node-permission">
                {{ data.permission }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type ElTree } from 'element-plus'
import { getMenuPermissionsApi } from '@/api/system/menu'
import { assignRolePermissionsApi, getRolePermissionIdsApi } from '@/api/system/role'
import type { RoleInfo } from '@/api/system/role'

// Props
interface Props {
  visible: boolean
  roleData: Partial<RoleInfo>
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  roleData: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 权限树节点类型
interface PermissionTreeNode {
  id: number
  menuName: string
  permission: string
  parentId: number
  icon?: string
  children?: PermissionTreeNode[]
}

// 响应式数据
const dialogVisible = ref(false)
const treeRef = ref<InstanceType<typeof ElTree>>()
const submitLoading = ref(false)
const isExpandAll = ref(false)
const isCheckAll = ref(false)

// 权限树数据
const permissionTree = ref<PermissionTreeNode[]>([])
const checkedKeys = ref<number[]>([])
const expandedKeys = ref<number[]>([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'menuName'
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        loadPermissionTree()
        loadRolePermissions()
      })
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载权限树
const loadPermissionTree = async () => {
  try {
    const response = await getMenuPermissionsApi()
    permissionTree.value = response.data
    
    // 默认展开第一层
    expandedKeys.value = response.data.map(item => item.id)
  } catch (error) {
    ElMessage.error('加载权限树失败')
  }
}

// 加载角色权限
const loadRolePermissions = async () => {
  if (!props.roleData.id) return

  try {
    const response = await getRolePermissionIdsApi(props.roleData.id)
    checkedKeys.value = response.data
    
    // 设置树形组件的选中状态
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCheckedKeys(checkedKeys.value)
      }
    })
  } catch (error) {
    ElMessage.error('加载角色权限失败')
  }
}

// 展开/收起全部
const handleExpandAll = () => {
  if (!treeRef.value) return

  if (isExpandAll.value) {
    // 收起全部
    expandedKeys.value = []
    treeRef.value.setExpandedKeys([])
  } else {
    // 展开全部
    const allKeys = getAllNodeKeys(permissionTree.value)
    expandedKeys.value = allKeys
    treeRef.value.setExpandedKeys(allKeys)
  }
  isExpandAll.value = !isExpandAll.value
}

// 全选/取消全选
const handleCheckAll = () => {
  if (!treeRef.value) return

  if (isCheckAll.value) {
    // 取消全选
    checkedKeys.value = []
    treeRef.value.setCheckedKeys([])
  } else {
    // 全选
    const allKeys = getAllNodeKeys(permissionTree.value)
    checkedKeys.value = allKeys
    treeRef.value.setCheckedKeys(allKeys)
  }
  isCheckAll.value = !isCheckAll.value
}

// 获取所有节点的key
const getAllNodeKeys = (nodes: PermissionTreeNode[]): number[] => {
  const keys: number[] = []
  
  const traverse = (nodeList: PermissionTreeNode[]) => {
    nodeList.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return keys
}

// 树形选择变化
const handleTreeCheck = (data: PermissionTreeNode, checked: any) => {
  checkedKeys.value = checked.checkedKeys
  
  // 更新全选状态
  const allKeys = getAllNodeKeys(permissionTree.value)
  isCheckAll.value = checkedKeys.value.length === allKeys.length
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交权限分配
const handleSubmit = async () => {
  if (!props.roleData.id) return

  try {
    submitLoading.value = true
    
    // 获取当前选中的权限ID
    const selectedKeys = treeRef.value?.getCheckedKeys() as number[] || []
    
    await assignRolePermissionsApi(props.roleData.id, {
      permissionIds: selectedKeys
    })
    
    ElMessage.success('权限分配成功')
    emit('success')
  } catch (error) {
    ElMessage.error('权限分配失败')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.permission-dialog {
  .role-info {
    margin-bottom: 20px;
  }

  .permission-tree {
    .tree-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;

      .tree-actions {
        display: flex;
        gap: 10px;
      }
    }

    .permission-tree-component {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 10px;

      .tree-node {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .node-icon {
          font-size: 16px;
          color: #606266;
        }

        .node-label {
          flex: 1;
          font-size: 14px;
        }

        .node-permission {
          font-size: 12px;
          margin-left: auto;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
