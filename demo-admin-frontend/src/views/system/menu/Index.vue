<!--
  菜单管理页面
  <AUTHOR>
-->

<template>
  <div class="menu-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>菜单管理</span>
          <el-button
            type="primary"
            @click="handleAdd"
            v-permission="'system:menu:add'"
          >
            <el-icon><Plus /></el-icon>
            新增菜单
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="菜单名称">
          <el-input v-model="queryForm.menuName" placeholder="请输入菜单名称" clearable />
        </el-form-item>
        <el-form-item label="菜单类型">
          <el-select v-model="queryForm.menuType" placeholder="请选择菜单类型" clearable>
            <el-option label="目录" :value="1" />
            <el-option label="菜单" :value="2" />
            <el-option label="按钮" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="handleExpandAll">
            <el-icon><Operation /></el-icon>
            {{ isExpandAll ? '收起全部' : '展开全部' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        border
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
      >
        <el-table-column prop="menuTitle" label="菜单名称" width="250">
          <template #default="{ row }">
            <div class="menu-name-cell">
              <el-icon v-if="row.icon" class="menu-icon">
                <component :is="row.icon" />
              </el-icon>
              <span>{{ row.menuTitle || row.menuName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="menuName" label="路由名称" width="150" />
        <el-table-column prop="path" label="路由路径" width="180" show-overflow-tooltip />
        <el-table-column prop="component" label="组件路径" width="200" show-overflow-tooltip />
        <el-table-column prop="permission" label="权限标识" width="180" show-overflow-tooltip />
        <el-table-column prop="menuType" label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.menuType)">
              {{ getTypeText(row.menuType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80">
          <template #default="{ row }">
            <div class="sort-cell">
              <span>{{ row.sortOrder }}</span>
              <div class="sort-actions">
                <el-button
                  size="small"
                  text
                  @click="handleSort(row, 'up')"
                  v-permission="'system:menu:edit'"
                >
                  <el-icon><ArrowUp /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  text
                  @click="handleSort(row, 'down')"
                  v-permission="'system:menu:edit'"
                >
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              v-permission="'system:menu:edit'"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-permission="'system:menu:edit'"
            >
              编辑
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleAddChild(row)"
              v-permission="'system:menu:add'"
              v-if="row.menuType !== 3"
            >
              新增
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              v-permission="'system:menu:delete'"
              :disabled="row.isSystem === 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 菜单表单弹窗 -->
    <MenuFormDialog
      v-model:visible="formDialogVisible"
      :form-data="currentMenu"
      :is-edit="isEdit"
      :parent-menu="parentMenu"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Operation, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { getMenuListApi, deleteMenuApi, enableMenuApi, disableMenuApi, updateMenuSortApi } from '@/api/system/menu'
import type { MenuInfo, MenuQueryParams } from '@/api/system/menu'
import MenuFormDialog from './components/MenuFormDialog.vue'

// 查询表单
const queryForm = reactive<MenuQueryParams>({
  menuName: '',
  menuType: undefined,
  status: undefined
})

// 表格数据
const tableData = ref<MenuInfo[]>([])
const loading = ref(false)
const tableRef = ref()
const isExpandAll = ref(false)

// 弹窗控制
const formDialogVisible = ref(false)
const currentMenu = ref<Partial<MenuInfo>>({})
const parentMenu = ref<Partial<MenuInfo>>({})
const isEdit = ref(false)

// 获取类型标签类型
const getTypeTagType = (type: number) => {
  const typeMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '目录',
    2: '菜单',
    3: '按钮'
  }
  return typeMap[type] || '未知'
}

// 搜索
const handleSearch = () => {
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, {
    menuName: '',
    menuType: undefined,
    status: undefined
  })
  handleSearch()
}

// 展开/收起全部
const handleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
  if (tableRef.value) {
    if (isExpandAll.value) {
      tableRef.value.expandAll()
    } else {
      tableRef.value.collapseAll()
    }
  }
}

// 新增
const handleAdd = () => {
  currentMenu.value = {}
  parentMenu.value = {}
  isEdit.value = false
  formDialogVisible.value = true
}

// 编辑
const handleEdit = (row: MenuInfo) => {
  currentMenu.value = { ...row }
  parentMenu.value = {}
  isEdit.value = true
  formDialogVisible.value = true
}

// 新增子菜单
const handleAddChild = (row: MenuInfo) => {
  currentMenu.value = {}
  parentMenu.value = { ...row }
  isEdit.value = false
  formDialogVisible.value = true
}

// 状态切换
const handleStatusChange = async (row: MenuInfo) => {
  try {
    if (row.status === 1) {
      await enableMenuApi(row.id)
      ElMessage.success('菜单启用成功')
    } else {
      await disableMenuApi(row.id)
      ElMessage.success('菜单禁用成功')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error('状态切换失败')
  }
}

// 排序
const handleSort = async (row: MenuInfo, direction: 'up' | 'down') => {
  try {
    const newSortOrder = direction === 'up' ? row.sortOrder! - 1 : row.sortOrder! + 1
    await updateMenuSortApi(row.id, newSortOrder)
    ElMessage.success('排序更新成功')
    fetchData()
  } catch (error) {
    ElMessage.error('排序更新失败')
  }
}

// 删除
const handleDelete = async (row: MenuInfo) => {
  if (row.isSystem === 1) {
    ElMessage.warning('系统内置菜单不能删除')
    return
  }

  if (row.children && row.children.length > 0) {
    ElMessage.warning('请先删除子菜单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 "${row.menuTitle || row.menuName}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteMenuApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  formDialogVisible.value = false
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getMenuListApi(queryForm)
    tableData.value = response.data
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.menu-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-form {
    margin-bottom: 20px;
  }

  .menu-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .menu-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .sort-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .sort-actions {
      display: flex;
      flex-direction: column;
      gap: 2px;
      margin-left: 8px;

      .el-button {
        padding: 2px;
        min-height: auto;

        .el-icon {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
