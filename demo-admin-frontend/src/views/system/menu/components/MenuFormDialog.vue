<!--
  菜单表单弹窗组件
  <AUTHOR>
-->

<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentId">
            <el-tree-select
              v-model="form.parentId"
              :data="menuTreeOptions"
              :props="treeProps"
              placeholder="请选择上级菜单"
              check-strictly
              :render-after-expand="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="menuType">
            <el-radio-group v-model="form.menuType" @change="handleTypeChange">
              <el-radio :label="1">目录</el-radio>
              <el-radio :label="2">菜单</el-radio>
              <el-radio :label="3">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单名称" prop="menuName">
            <el-input
              v-model="form.menuName"
              placeholder="请输入菜单名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示名称" prop="menuTitle">
            <el-input
              v-model="form.menuTitle"
              placeholder="请输入显示名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.menuType !== 3">
        <el-col :span="12">
          <el-form-item label="路由路径" prop="path">
            <el-input
              v-model="form.path"
              placeholder="请输入路由路径"
              maxlength="200"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组件路径" prop="component" v-if="form.menuType === 2">
            <el-input
              v-model="form.component"
              placeholder="请输入组件路径"
              maxlength="200"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单图标" prop="icon" v-if="form.menuType !== 3">
            <IconSelector v-model="form.icon" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权限标识" prop="permission">
            <el-input
              v-model="form.permission"
              placeholder="请输入权限标识"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number
              v-model="form.sortOrder"
              :min="0"
              :max="999"
              placeholder="请输入排序"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否显示" prop="visible" v-if="form.menuType !== 3">
            <el-radio-group v-model="form.visible">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="状态" prop="status" v-if="isEdit">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { getMenuTreeApi, createMenuApi, updateMenuApi } from '@/api/system/menu'
import type { MenuInfo, MenuCreateRequest, MenuUpdateRequest } from '@/api/system/menu'
import IconSelector from './IconSelector.vue'

// Props
interface Props {
  visible: boolean
  formData: Partial<MenuInfo>
  parentMenu: Partial<MenuInfo>
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: () => ({}),
  parentMenu: () => ({}),
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const menuTreeOptions = ref<MenuInfo[]>([])

// 弹窗标题
const dialogTitle = computed(() => {
  if (props.isEdit) {
    return '编辑菜单'
  }
  if (props.parentMenu.id) {
    return `新增子菜单 - ${props.parentMenu.menuTitle || props.parentMenu.menuName}`
  }
  return '新增菜单'
})

// 表单数据
const form = reactive<MenuCreateRequest & { status?: number }>({
  parentId: 0,
  menuName: '',
  menuTitle: '',
  path: '',
  component: '',
  icon: '',
  menuType: 1,
  sortOrder: 0,
  visible: 1,
  status: 1,
  permission: '',
  remark: ''
})

// 树形选择器配置
const treeProps = {
  children: 'children',
  label: 'menuTitle',
  value: 'id'
}

// 表单验证规则
const rules: FormRules = {
  parentId: [
    { required: true, message: '请选择上级菜单', trigger: 'change' }
  ],
  menuName: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { min: 2, max: 50, message: '菜单名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  menuTitle: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  menuType: [
    { required: true, message: '请选择菜单类型', trigger: 'change' }
  ],
  path: [
    { required: true, message: '请输入路由路径', trigger: 'blur' },
    { max: 200, message: '路由路径不能超过200个字符', trigger: 'blur' }
  ],
  component: [
    { required: true, message: '请输入组件路径', trigger: 'blur' },
    { max: 200, message: '组件路径不能超过200个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 999, message: '排序值在 0 到 999 之间', trigger: 'blur' }
  ],
  permission: [
    { max: 100, message: '权限标识不能超过100个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' }
  ]
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        loadMenuTree()
        resetForm()
        if (props.isEdit && props.formData) {
          Object.assign(form, props.formData)
        } else if (props.parentMenu.id) {
          form.parentId = props.parentMenu.id
        }
      })
    }
  },
  { immediate: true }
)

// 监听内部弹窗状态
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载菜单树
const loadMenuTree = async () => {
  try {
    const response = await getMenuTreeApi()
    // 添加根节点
    menuTreeOptions.value = [
      {
        id: 0,
        menuName: '根目录',
        menuTitle: '根目录',
        children: response.data
      } as MenuInfo
    ]
  } catch (error) {
    ElMessage.error('加载菜单树失败')
  }
}

// 菜单类型变化
const handleTypeChange = (type: number) => {
  // 清空相关字段
  if (type === 3) {
    // 按钮类型
    form.path = ''
    form.component = ''
    form.icon = ''
    form.visible = 1
  } else if (type === 1) {
    // 目录类型
    form.component = ''
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    parentId: 0,
    menuName: '',
    menuTitle: '',
    path: '',
    component: '',
    icon: '',
    menuType: 1,
    sortOrder: 0,
    visible: 1,
    status: 1,
    permission: '',
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (props.isEdit) {
      // 编辑菜单
      const updateData: MenuUpdateRequest = {
        parentId: form.parentId,
        menuName: form.menuName,
        menuTitle: form.menuTitle,
        path: form.path,
        component: form.component,
        icon: form.icon,
        menuType: form.menuType,
        sortOrder: form.sortOrder,
        status: form.status,
        visible: form.visible,
        permission: form.permission,
        remark: form.remark
      }
      await updateMenuApi(props.formData.id as number, updateData)
      ElMessage.success('菜单更新成功')
    } else {
      // 新增菜单
      const createData: MenuCreateRequest = {
        parentId: form.parentId,
        menuName: form.menuName,
        menuTitle: form.menuTitle,
        path: form.path,
        component: form.component,
        icon: form.icon,
        menuType: form.menuType,
        sortOrder: form.sortOrder,
        visible: form.visible,
        permission: form.permission,
        remark: form.remark
      }
      await createMenuApi(createData)
      ElMessage.success('菜单创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
