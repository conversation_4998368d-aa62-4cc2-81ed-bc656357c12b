<!--
  图标选择器组件
  <AUTHOR>
-->

<template>
  <div class="icon-selector">
    <el-input
      v-model="selectedIcon"
      placeholder="请选择图标"
      readonly
      @click="showDialog = true"
    >
      <template #prefix>
        <el-icon v-if="selectedIcon" class="selected-icon">
          <component :is="selectedIcon" />
        </el-icon>
      </template>
      <template #suffix>
        <el-icon class="cursor-pointer" @click="showDialog = true">
          <Search />
        </el-icon>
      </template>
    </el-input>

    <el-dialog
      title="选择图标"
      v-model="showDialog"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="icon-dialog">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索图标"
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <div class="icon-categories">
          <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
            <el-tab-pane label="常用图标" name="common">
              <div class="icon-grid">
                <div
                  v-for="icon in filteredCommonIcons"
                  :key="icon"
                  :class="['icon-item', { active: selectedIcon === icon }]"
                  @click="selectIcon(icon)"
                >
                  <el-icon class="icon">
                    <component :is="icon" />
                  </el-icon>
                  <span class="icon-name">{{ icon }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="系统图标" name="system">
              <div class="icon-grid">
                <div
                  v-for="icon in filteredSystemIcons"
                  :key="icon"
                  :class="['icon-item', { active: selectedIcon === icon }]"
                  @click="selectIcon(icon)"
                >
                  <el-icon class="icon">
                    <component :is="icon" />
                  </el-icon>
                  <span class="icon-name">{{ icon }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="业务图标" name="business">
              <div class="icon-grid">
                <div
                  v-for="icon in filteredBusinessIcons"
                  :key="icon"
                  :class="['icon-item', { active: selectedIcon === icon }]"
                  @click="selectIcon(icon)"
                >
                  <el-icon class="icon">
                    <component :is="icon" />
                  </el-icon>
                  <span class="icon-name">{{ icon }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button @click="clearIcon">清空</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

// 响应式数据
const showDialog = ref(false)
const selectedIcon = ref('')
const searchKeyword = ref('')
const activeCategory = ref('common')

// 常用图标
const commonIcons = [
  'House', 'Menu', 'Setting', 'User', 'UserFilled', 'Lock', 'Unlock',
  'View', 'Hide', 'Search', 'Plus', 'Minus', 'Edit', 'Delete',
  'Check', 'Close', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
  'Upload', 'Download', 'Refresh', 'More', 'MoreFilled'
]

// 系统图标
const systemIcons = [
  'Monitor', 'Cpu', 'MemoryCard', 'HardDrive', 'Connection', 'Wifi',
  'Key', 'Lock', 'Unlock', 'Shield', 'Warning', 'InfoFilled',
  'SuccessFilled', 'CircleCloseFilled', 'QuestionFilled', 'Bell',
  'Message', 'ChatDotRound', 'Phone', 'Cellphone', 'Headset'
]

// 业务图标
const businessIcons = [
  'Management', 'Operation', 'DataAnalysis', 'PieChart', 'Histogram',
  'TrendCharts', 'DataLine', 'DataBoard', 'Document', 'DocumentAdd',
  'DocumentDelete', 'DocumentCopy', 'Folder', 'FolderAdd', 'FolderDelete',
  'Files', 'Tickets', 'List', 'Grid', 'Calendar', 'Clock', 'Timer'
]

// 过滤后的图标列表
const filteredCommonIcons = computed(() => {
  if (!searchKeyword.value) return commonIcons
  return commonIcons.filter(icon => 
    icon.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredSystemIcons = computed(() => {
  if (!searchKeyword.value) return systemIcons
  return systemIcons.filter(icon => 
    icon.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredBusinessIcons = computed(() => {
  if (!searchKeyword.value) return businessIcons
  return businessIcons.filter(icon => 
    icon.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedIcon.value = newVal || ''
  },
  { immediate: true }
)

// 分类切换
const handleCategoryChange = () => {
  searchKeyword.value = ''
}

// 选择图标
const selectIcon = (icon: string) => {
  selectedIcon.value = icon
}

// 清空图标
const clearIcon = () => {
  selectedIcon.value = ''
  emit('update:modelValue', '')
  showDialog.value = false
}

// 确认选择
const confirmSelect = () => {
  emit('update:modelValue', selectedIcon.value)
  showDialog.value = false
}
</script>

<style lang="scss" scoped>
.icon-selector {
  .selected-icon {
    color: #409eff;
  }

  .cursor-pointer {
    cursor: pointer;
  }
}

.icon-dialog {
  .search-input {
    margin-bottom: 20px;
  }

  .icon-categories {
    .icon-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 10px;
      max-height: 400px;
      overflow-y: auto;
      padding: 10px 0;

      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px 10px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        &.active {
          border-color: #409eff;
          background-color: #409eff;
          color: white;

          .icon {
            color: white;
          }
        }

        .icon {
          font-size: 24px;
          margin-bottom: 8px;
          color: #606266;
        }

        .icon-name {
          font-size: 12px;
          text-align: center;
          word-break: break-all;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
