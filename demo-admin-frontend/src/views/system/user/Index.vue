<!--
  用户管理页面
  <AUTHOR>
-->

<template>
  <div class="user-management">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div class="header-actions">
            <el-button
              type="primary"
              @click="handleAdd"
              v-permission="SYSTEM_PERMISSIONS.USER.ADD"
            >
              <el-icon><Plus /></el-icon>
              新增用户
            </el-button>
            <el-button
              type="success"
              @click="handleExport"
              :loading="exportLoading"
              v-permission="SYSTEM_PERMISSIONS.USER.EXPORT"
            >
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="queryForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="queryForm.realName"
            placeholder="请输入真实姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input
            v-model="queryForm.email"
            placeholder="请输入邮箱"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="realName" label="真实姓名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            <span v-if="row.gender === 1">男</span>
            <span v-else-if="row.gender === 0">女</span>
            <span v-else>未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="160" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
              v-permission="SYSTEM_PERMISSIONS.USER.EDIT"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleResetPassword(row)"
              v-permission="SYSTEM_PERMISSIONS.USER.RESET_PASSWORD"
            >
              重置密码
            </el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
              v-permission="SYSTEM_PERMISSIONS.USER.UPDATE_STATUS"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              v-permission="SYSTEM_PERMISSIONS.USER.DELETE"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRows.length > 0">
        <el-alert
          :title="`已选择 ${selectedRows.length} 项`"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <el-button
              type="danger"
              size="small"
              @click="handleBatchDelete"
              v-permission="SYSTEM_PERMISSIONS.USER.DELETE"
            >
              批量删除
            </el-button>
          </template>
        </el-alert>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="formData.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="formData.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="请确认密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="formData.gender" placeholder="请选择性别">
                <el-option label="男" :value="1" />
                <el-option label="女" :value="0" />
                <el-option label="未知" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="formData.nickname" placeholder="请输入昵称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 空列，保持布局对称 -->
          </el-col>
        </el-row>

        <el-form-item label="角色分配" prop="roleIds">
          <el-select
            v-model="formData.roleIds"
            multiple
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.id"
              :label="role.roleName"
              :value="role.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search, Refresh, Download } from '@element-plus/icons-vue'
import {
  getUserPageApi,
  createUserApi,
  updateUserApi,
  deleteUserApi,
  batchDeleteUsersApi,
  enableUserApi,
  disableUserApi,
  resetUserPasswordApi,
  exportUsersApi,
  checkUsernameApi,
  checkEmailApi,
  checkPhoneApi,
  type UserInfo,
  type UserCreateRequest,
  type UserUpdateRequest,
  type UserQueryParams
} from '@/api/system/user'
import { getEnabledRolesApi, type RoleInfo } from '@/api/system/role'
import { SYSTEM_PERMISSIONS } from '@/constants/permissions'

// 查询表单
const queryForm = reactive<UserQueryParams>({
  username: '',
  realName: '',
  email: '',
  status: undefined
})

// 表格数据
const tableData = ref<UserInfo[]>([])
const loading = ref(false)
const selectedRows = ref<UserInfo[]>([])

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 导出加载状态
const exportLoading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

// 角色选项
const roleOptions = ref<RoleInfo[]>([])

// 表单数据
const formData = reactive<UserCreateRequest & Partial<UserUpdateRequest> & { confirmPassword?: string }>({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  nickname: '',
  email: '',
  phone: '',
  gender: 2,
  status: 1,
  roleIds: [],
  remark: ''
})

// 对话框标题
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新增用户')

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && (!isEdit.value || value !== formData.username)) {
          checkUsernameApi(value, isEdit.value ? (formData as UserUpdateRequest).id : undefined).then(response => {
            if (response.data) {
              callback(new Error('用户名已存在'))
            } else {
              callback()
            }
          }).catch(() => {
            callback()
          })
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 50, message: '真实姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && (!isEdit.value || value !== formData.email)) {
          checkEmailApi(value, isEdit.value ? (formData as UserUpdateRequest).id : undefined).then(response => {
            if (response.data) {
              callback(new Error('邮箱已存在'))
            } else {
              callback()
            }
          }).catch(() => {
            callback()
          })
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && (!isEdit.value || value !== formData.phone)) {
          checkPhoneApi(value, isEdit.value ? (formData as UserUpdateRequest).id : undefined).then(response => {
            if (response.data) {
              callback(new Error('手机号已存在'))
            } else {
              callback()
            }
          }).catch(() => {
            callback()
          })
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, {
    username: '',
    realName: '',
    email: '',
    status: undefined
  })
  handleSearch()
}

// 表格选择变化
const handleSelectionChange = (selection: UserInfo[]) => {
  selectedRows.value = selection
}

// 新增用户
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
  loadRoleOptions()
}

// 编辑用户
const handleEdit = async (row: UserInfo) => {
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, {
    id: row.id,
    username: row.username,
    realName: row.realName,
    nickname: row.nickname,
    email: row.email,
    phone: row.phone,
    gender: row.gender,
    status: row.status,
    roleIds: row.roles?.map(role => role.id) || [],
    remark: row.remark
  })

  dialogVisible.value = true
  loadRoleOptions()
}

// 删除用户
const handleDelete = async (row: UserInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteUserApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个用户吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    await batchDeleteUsersApi(ids)
    ElMessage.success('批量删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 切换用户状态
const handleToggleStatus = async (row: UserInfo) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户 "${row.username}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (row.status === 1) {
      await disableUserApi(row.id)
    } else {
      await enableUserApi(row.id)
    }

    ElMessage.success(`${action}成功`)
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 重置密码
const handleResetPassword = async (row: UserInfo) => {
  try {
    const { value: newPassword } = await ElMessageBox.prompt(
      '请输入新密码',
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: (value) => {
          if (!value) {
            return '密码不能为空'
          }
          if (value.length < 6 || value.length > 20) {
            return '密码长度在 6 到 20 个字符'
          }
          return true
        }
      }
    )

    await resetUserPasswordApi(row.id, { newPassword })
    ElMessage.success('密码重置成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('密码重置失败')
    }
  }
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const blob = await exportUsersApi(queryForm)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户数据_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  fetchData()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      // 编辑用户
      const { password, confirmPassword, ...updateData } = formData
      await updateUserApi((formData as UserUpdateRequest).id, updateData as UserUpdateRequest)
      ElMessage.success('用户更新成功')
    } else {
      // 新增用户
      await createUserApi(formData as UserCreateRequest)
      ElMessage.success('用户创建成功')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '用户更新失败' : '用户创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    password: '',
    confirmPassword: '',
    realName: '',
    nickname: '',
    email: '',
    phone: '',
    gender: 2,
    status: 1,
    roleIds: [],
    remark: ''
  })

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 加载角色选项
const loadRoleOptions = async () => {
  try {
    const response = await getEnabledRolesApi()
    roleOptions.value = response.data
  } catch (error) {
    ElMessage.error('加载角色选项失败')
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...queryForm,
      pageNum: pagination.current,
      pageSize: pagination.size
    }

    const response = await getUserPageApi(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.user-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .search-form {
    margin-bottom: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .batch-actions {
    margin-top: 15px;

    .el-alert {
      :deep(.el-alert__content) {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  .pagination {
    margin-top: 20px;
    justify-content: center;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
