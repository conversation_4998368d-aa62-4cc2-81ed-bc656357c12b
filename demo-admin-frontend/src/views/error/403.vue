<!--
  403权限错误页面
  <AUTHOR>
-->

<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <el-icon :size="120" color="#f56c6c">
          <WarnTriangleFilled />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h1 class="error-code">403</h1>
        <h2 class="error-title">无权限访问</h2>
        <p class="error-description">
          抱歉，您没有权限访问此页面，请联系管理员
        </p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            返回首页
          </el-button>
          <el-button @click="goBack">
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { WarnTriangleFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-image {
  margin-bottom: 30px;
}

.error-info {
  .error-code {
    font-size: 72px;
    font-weight: 700;
    color: #f56c6c;
    margin: 0 0 20px 0;
    line-height: 1;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 16px 0;
  }
  
  .error-description {
    font-size: 16px;
    color: #606266;
    margin: 0 0 40px 0;
    line-height: 1.5;
  }
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

// 响应式设计
@media (max-width: 480px) {
  .error-info .error-code {
    font-size: 60px;
  }
  
  .error-info .error-title {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
}
</style>
