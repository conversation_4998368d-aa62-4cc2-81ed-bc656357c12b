<template>
  <div class="log-container">
    <el-card class="log-card">
      <template #header>
        <div class="card-header">
          <span>系统日志管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="handleExport" :loading="exportLoading">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button type="warning" @click="handleClean">
              <el-icon><Delete /></el-icon>
              清理
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入关键词"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="日志类型">
            <el-select v-model="queryParams.logType" placeholder="请选择日志类型" clearable style="width: 120px">
              <el-option label="业务日志" value="BIZ_LOG" />
              <el-option label="异常日志" value="EXCEPTION_LOG" />
              <el-option label="性能日志" value="PERFORMANCE_LOG" />
            </el-select>
          </el-form-item>
          <el-form-item label="用户名">
            <el-input
              v-model="queryParams.username"
              placeholder="请输入用户名"
              clearable
              style="width: 120px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="模块">
            <el-select v-model="queryParams.moduleName" placeholder="请选择模块" clearable style="width: 120px">
              <el-option label="用户管理" value="用户管理" />
              <el-option label="角色管理" value="角色管理" />
              <el-option label="菜单管理" value="菜单管理" />
              <el-option label="系统配置" value="系统配置" />
              <el-option label="个人中心" value="个人中心" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作状态">
            <el-select v-model="queryParams.success" placeholder="请选择状态" clearable style="width: 100px">
              <el-option label="成功" :value="true" />
              <el-option label="失败" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon total">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.totalLogCount || 0 }}</div>
                  <div class="stat-label">总日志数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon success">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.successLogCount || 0 }}</div>
                  <div class="stat-label">成功日志</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon error">
                  <el-icon><CircleClose /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.exceptionLogCount || 0 }}</div>
                  <div class="stat-label">异常日志</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.slowQueryCount || 0 }}</div>
                  <div class="stat-label">慢查询</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 日志列表 -->
      <div class="log-table">
        <el-table
          v-loading="loading"
          :data="logList"
          row-key="id"
          :default-sort="{ prop: 'createTime', order: 'descending' }"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="logType" label="日志类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getLogTypeTag(row.logType)">
                {{ getLogTypeLabel(row.logType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="username" label="用户名" width="100" />
          <el-table-column prop="description" label="操作描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="moduleName" label="模块" width="100" />
          <el-table-column prop="operationType" label="操作类型" width="100" />
          <el-table-column prop="requestUri" label="请求URI" min-width="150" show-overflow-tooltip />
          <el-table-column prop="method" label="方法" width="80" />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="executionTime" label="执行时间" width="100" sortable="custom">
            <template #default="{ row }">
              <span :class="{ 'slow-query': row.executionTime && row.executionTime > 2000 }">
                {{ row.executionTime }}ms
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="success" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.success ? 'success' : 'danger'">
                {{ row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="80">
            <template #default="{ row }">
              <el-tag :type="getLogLevelTag(row.level)">
                {{ row.level }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">
                查看
              </el-button>
              <el-button link type="warning" @click="handleViewDetails(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      title="日志详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedLog" class="log-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="日志类型">
            <el-tag :type="getLogTypeTag(selectedLog.logType)">
              {{ getLogTypeLabel(selectedLog.logType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedLog.username }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedLog.userId }}</el-descriptions-item>
          <el-descriptions-item label="操作描述">{{ selectedLog.description }}</el-descriptions-item>
          <el-descriptions-item label="模块">{{ selectedLog.moduleName }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">{{ selectedLog.operationType }}</el-descriptions-item>
          <el-descriptions-item label="请求URI">{{ selectedLog.requestUri }}</el-descriptions-item>
          <el-descriptions-item label="请求方法">{{ selectedLog.method }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedLog.ip }}</el-descriptions-item>
          <el-descriptions-item label="执行时间">
            <span :class="{ 'slow-query': selectedLog.executionTime && selectedLog.executionTime > 2000 }">
              {{ selectedLog.executionTime }}ms
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedLog.success ? 'success' : 'danger'">
              {{ selectedLog.success ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLogLevelTag(selectedLog.level)">
              {{ selectedLog.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="异常类型">{{ selectedLog.exceptionType }}</el-descriptions-item>
          <el-descriptions-item label="浏览器">{{ selectedLog.browser }}</el-descriptions-item>
          <el-descriptions-item label="操作系统">{{ selectedLog.os }}</el-descriptions-item>
          <el-descriptions-item label="线程名称">{{ selectedLog.threadName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedLog.createTime) }}</el-descriptions-item>
          </el-descriptions>

        <div v-if="selectedLog.errorMsg" class="error-section">
          <h4>错误信息</h4>
          <el-input
            v-model="selectedLog.errorMsg"
            type="textarea"
            :rows="4"
            readonly
          />
        </div>

        <div v-if="selectedLog.requestParams" class="params-section">
          <h4>请求参数</h4>
          <el-input
            v-model="selectedLog.requestParams"
            type="textarea"
            :rows="4"
            readonly
          />
        </div>

        <div v-if="selectedLog.response" class="response-section">
          <h4>响应结果</h4>
          <el-input
            v-model="selectedLog.response"
            type="textarea"
            :rows="4"
            readonly
          />
        </div>
      </div>
    </el-dialog>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="日志查看"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedLog" class="log-view">
        <pre>{{ formatLogView(selectedLog) }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Search, Download, Delete, 
  Document, CircleCheck, CircleClose, Warning 
} from '@element-plus/icons-vue'
import { logApi, type LogQueryRequest, type LogResponse, type LogStatisticsResponse } from '@/api/log'
import { useAuthStore } from '@/stores/auth'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const logList = ref<LogResponse[]>([])
const total = ref(0)
const statistics = ref<LogStatisticsResponse>({} as LogStatisticsResponse)
const selectedLog = ref<LogResponse | null>(null)
const detailsDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dateRange = ref<[string, string] | null>(null)

// 查询参数
const queryParams = reactive<LogQueryRequest>({
  pageNum: 1,
  pageSize: 20,
  sortField: 'createTime',
  sortOrder: 'desc'
})

// WebSocket连接
let ws: WebSocket | null = null

// 认证store
const authStore = useAuthStore()

// 方法
const handleQuery = () => {
  queryParams.pageNum = 1
  getLogList()
  getLogStatistics()
}

const resetQuery = () => {
  Object.assign(queryParams, {
    keyword: undefined,
    logType: undefined,
    username: undefined,
    moduleName: undefined,
    success: undefined,
    pageNum: 1
  })
  dateRange.value = null
  handleQuery()
}

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getLogList()
}

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val
  getLogList()
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.sortField = prop
  queryParams.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  getLogList()
}

const handleRefresh = () => {
  getLogList()
  getLogStatistics()
  ElMessage.success('刷新成功')
}

const handleExport = async () => {
  try {
    exportLoading.value = true
    
    // 构建导出查询参数
    const exportParams: LogQueryRequest = { ...queryParams }
    
    // 处理日期范围
    if (dateRange.value) {
      exportParams.startTime = dateRange.value[0]
      exportParams.endTime = dateRange.value[1]
    }
    
    const blob = await logApi.exportLogs(exportParams)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `日志数据_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleClean = () => {
  ElMessageBox.confirm(
    '确定要清理过期日志吗？此操作不可恢复！',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const beforeDate = new Date()
    beforeDate.setDate(beforeDate.getDate() - 30)
    logApi.cleanLogs(beforeDate.toISOString()).then(() => {
      ElMessage.success('清理成功')
      handleRefresh()
    })
  })
}

const handleView = (row: LogResponse) => {
  selectedLog.value = row
  viewDialogVisible.value = true
}

const handleViewDetails = (row: LogResponse) => {
  selectedLog.value = row
  detailsDialogVisible.value = true
}

// 获取日志列表
const getLogList = async () => {
  loading.value = true
  try {
    if (dateRange.value) {
      queryParams.startTime = dateRange.value[0]
      queryParams.endTime = dateRange.value[1]
    } else {
      queryParams.startTime = undefined
      queryParams.endTime = undefined
    }

    const res = await logApi.getLogPage(queryParams)
    logList.value = res.data.records
    total.value = res.data.total
  } catch (error) {
    console.error('获取日志列表失败:', error)
    ElMessage.error('获取日志列表失败')
  } finally {
    loading.value = false
  }
}

// 获取日志统计
const getLogStatistics = async () => {
  try {
    const params = { ...queryParams }
    delete params.pageNum
    delete params.pageSize

    if (dateRange.value) {
      params.startTime = dateRange.value[0]
      params.endTime = dateRange.value[1]
    } else {
      params.startTime = undefined
      params.endTime = undefined
    }

    const res = await logApi.getLogStatistics(params)
    statistics.value = res.data
  } catch (error) {
    console.error('获取日志统计失败:', error)
  }
}

// 工具函数
const getLogTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'BIZ_LOG': 'primary',
    'EXCEPTION_LOG': 'danger',
    'PERFORMANCE_LOG': 'warning'
  }
  return tagMap[type] || 'info'
}

const getLogTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'BIZ_LOG': '业务日志',
    'EXCEPTION_LOG': '异常日志',
    'PERFORMANCE_LOG': '性能日志'
  }
  return labelMap[type] || type
}

const getLogLevelTag = (level: string) => {
  const tagMap: Record<string, string> = {
    'INFO': 'info',
    'WARN': 'warning',
    'ERROR': 'danger',
    'DEBUG': 'info'
  }
  return tagMap[level] || 'info'
}

const formatDateTime = (datetime: string) => {
  return new Date(datetime).toLocaleString()
}

const formatLogView = (log: LogResponse) => {
  return `【${log.logType}】${log.description}
用户: ${log.username}
模块: ${log.moduleName}
操作: ${log.operationType}
时间: ${formatDateTime(log.createTime)}
IP: ${log.ip}
执行时间: ${log.executionTime}ms
状态: ${log.success ? '成功' : '失败'}
${log.errorMsg ? `错误: ${log.errorMsg}` : ''}`
}

// WebSocket相关
const initWebSocket = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const wsUrl = `${protocol}//${window.location.host}/api/ws/log`
  
  ws = new WebSocket(wsUrl)
  
  ws.onopen = () => {
    console.log('WebSocket连接成功')
    // 发送认证信息
    if (authStore.token) {
      ws.send(JSON.stringify({
        type: 'auth',
        token: authStore.token
      }))
    }
  }
  
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'log') {
        // 实时日志推送
        const newLog = data.data
        logList.value.unshift(newLog)
        if (logList.value.length > queryParams.pageSize) {
          logList.value.pop()
        }
        // 更新统计
        getLogStatistics()
      }
    } catch (error) {
      console.error('WebSocket消息处理失败:', error)
    }
  }
  
  ws.onclose = () => {
    console.log('WebSocket连接关闭')
    // 5秒后重连
    setTimeout(initWebSocket, 5000)
  }
  
  ws.onerror = (error) => {
    console.error('WebSocket连接错误:', error)
  }
}

// 生命周期
onMounted(() => {
  handleQuery()
  initWebSocket()
})

onUnmounted(() => {
  if (ws) {
    ws.close()
  }
})
</script>

<style scoped>
.log-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-area {
  margin-bottom: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.stat-icon.success {
  background: linear-gradient(45deg, #56ab2f, #a8e6cf);
}

.stat-icon.error {
  background: linear-gradient(45deg, #ff416c, #ff4b2b);
}

.stat-icon.warning {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.log-table {
  margin-top: 20px;
}

.slow-query {
  color: #f56c6c;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.log-details {
  max-height: 600px;
  overflow-y: auto;
}

.error-section,
.params-section,
.response-section {
  margin-top: 20px;
}

.error-section h4,
.params-section h4,
.response-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: bold;
}

.log-view {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  color: #606266;
}
</style>