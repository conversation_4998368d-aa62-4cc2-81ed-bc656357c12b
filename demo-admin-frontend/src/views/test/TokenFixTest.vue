<template>
  <div class="test-container">
    <h2>JWT Token 修复测试</h2>
    
    <div class="test-section">
      <h3>1. Token 清理测试</h3>
      <div class="test-item">
        <label>原始Token (带空格):</label>
        <input v-model="originalToken" placeholder="输入带空格的token进行测试" />
      </div>
      <div class="test-item">
        <label>清理后Token:</label>
        <span class="result">{{ cleanedToken }}</span>
      </div>
      <el-button @click="testCleanToken" type="primary">测试清理</el-button>
    </div>

    <div class="test-section">
      <h3>2. 当前状态</h3>
      <div class="status-item">
        <label>是否已登录:</label>
        <span :class="{'success': authStore.isAuthenticated, 'error': !authStore.isAuthenticated}">
          {{ authStore.isAuthenticated ? '是' : '否' }}
        </span>
      </div>
      <div class="status-item" v-if="authStore.isAuthenticated">
        <label>当前Token:</label>
        <span class="token-preview">{{ authStore.token?.substring(0, 50) }}...</span>
      </div>
      <div class="status-item" v-if="authStore.isAuthenticated">
        <label>Token包含空格:</label>
        <span :class="{'error': hasWhitespace, 'success': !hasWhitespace}">
          {{ hasWhitespace ? '是' : '否' }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h3>3. API 测试</h3>
      <el-button @click="testUserApi" type="primary" :loading="testing">
        测试用户列表API
      </el-button>
      <div class="test-result" v-if="testResult">
        <h4>测试结果:</h4>
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>4. 快速操作</h3>
      <div class="actions">
        <el-button @click="cleanCurrentToken" type="warning">清理当前Token空格</el-button>
        <el-button @click="logout" type="danger">退出登录</el-button>
        <el-button @click="goToDebug" type="info">前往详细诊断页面</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/request'
import router from '@/router'

const authStore = useAuthStore()

const originalToken = ref('')
const testing = ref(false)
const testResult = ref<any>(null)

const cleanedToken = computed(() => originalToken.value ? originalToken.value.trim() : '')
const hasWhitespace = computed(() => authStore.token ? /\s/.test(authStore.token) : false)

const testCleanToken = () => {
  if (!originalToken.value) {
    ElMessage.warning('请输入token进行测试')
    return
  }
  
  const before = originalToken.value
  const after = before.trim()
  
  ElMessage.success(`清理完成！\n长度: ${before.length} -> ${after.length}`)
}

const cleanCurrentToken = () => {
  if (!authStore.token) {
    ElMessage.warning('当前没有token')
    return
  }
  
  const cleaned = authStore.token.trim()
  authStore.token = cleaned
  
  // 更新存储
  const tokenFromStorage = localStorage.getItem('demo_admin_token') || sessionStorage.getItem('demo_admin_token')
  if (tokenFromStorage) {
    if (localStorage.getItem('demo_admin_token')) {
      localStorage.setItem('demo_admin_token', cleaned)
    } else {
      sessionStorage.setItem('demo_admin_token', cleaned)
    }
  }
  
  ElMessage.success('当前token空格已清理')
}

const testUserApi = async () => {
  testing.value = true
  try {
    const response = await http.get('/system/user/page', {
      params: {
        current: 1,
        size: 5
      }
    })
    testResult.value = {
      success: true,
      data: response.data,
      code: response.code,
      message: response.message
    }
    ElMessage.success('API测试成功')
  } catch (error: any) {
    testResult.value = {
      success: false,
      error: error.message,
      response: error.response?.data
    }
    ElMessage.error('API测试失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const logout = () => {
  authStore.logout()
  ElMessage.success('已退出登录')
}

const goToDebug = () => {
  router.push('/debug/token')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.test-item, .status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.test-item label, .status-item label {
  font-weight: bold;
  min-width: 150px;
}

.test-item input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.result, .token-preview {
  font-family: monospace;
  background: #e0e0e0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.success {
  color: #67c23a;
  font-weight: bold;
}

.error {
  color: #f56c6c;
  font-weight: bold;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-result {
  margin-top: 15px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.test-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
}
</style>