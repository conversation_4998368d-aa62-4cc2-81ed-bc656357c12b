<template>
  <div class="token-debug-container">
    <h2>Token 诊断工具</h2>
    
    <div class="debug-section">
      <h3>当前Token状态</h3>
      <div class="token-info">
        <div class="info-item">
          <label>Token存在:</label>
          <span :class="{'success': hasToken, 'error': !hasToken}">{{ hasToken ? '是' : '否' }}</span>
        </div>
        <div class="info-item" v-if="hasToken">
          <label>Token长度:</label>
          <span>{{ tokenLength }}</span>
        </div>
        <div class="info-item" v-if="hasToken">
          <label>包含空格:</label>
          <span :class="{'error': hasWhitespace, 'success': !hasWhitespace}">{{ hasWhitespace ? '是' : '否' }}</span>
        </div>
        <div class="info-item" v-if="hasToken">
          <label>Token前20字符:</label>
          <span class="token-preview">{{ tokenStart }}</span>
        </div>
        <div class="info-item" v-if="hasToken">
          <label>Token后20字符:</label>
          <span class="token-preview">{{ tokenEnd }}</span>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>操作</h3>
      <div class="actions">
        <el-button type="primary" @click="cleanToken">清理Token空格</el-button>
        <el-button type="danger" @click="clearToken">清除Token</el-button>
        <el-button @click="refresh">刷新</el-button>
      </div>
    </div>

    <div class="debug-section">
      <h3>完整Token</h3>
      <div class="token-full" v-if="hasToken">
        <pre>{{ currentToken }}</pre>
      </div>
      <div v-else class="no-token">
        没有找到Token
      </div>
    </div>

    <div class="debug-section">
      <h3>测试API</h3>
      <div class="api-test">
        <el-button type="primary" @click="testApi">测试用户列表API</el-button>
        <div class="test-result" v-if="apiResult">
          <h4>API测试结果:</h4>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/request'

const authStore = useAuthStore()

const currentToken = ref<string>('')
const apiResult = ref<any>(null)

// 计算属性
const hasToken = computed(() => !!currentToken.value)
const tokenLength = computed(() => currentToken.value.length)
const hasWhitespace = computed(() => /\s/.test(currentToken.value))
const tokenStart = computed(() => currentToken.value ? currentToken.value.substring(0, 20) : '')
const tokenEnd = computed(() => currentToken.value ? currentToken.value.substring(currentToken.value.length - 20) : '')

// 获取当前token
const getCurrentToken = () => {
  currentToken.value = authStore.token || ''
}

// 清理token空格
const cleanToken = () => {
  if (currentToken.value) {
    const cleanedToken = currentToken.value.trim()
    authStore.token = cleanedToken
    
    // 更新localStorage
    const tokenFromStorage = localStorage.getItem('demo_admin_token') || sessionStorage.getItem('demo_admin_token')
    if (tokenFromStorage) {
      if (localStorage.getItem('demo_admin_token')) {
        localStorage.setItem('demo_admin_token', cleanedToken)
      } else {
        sessionStorage.setItem('demo_admin_token', cleanedToken)
      }
    }
    
    currentToken.value = cleanedToken
    ElMessage.success('Token空格已清理')
  }
}

// 清除token
const clearToken = () => {
  authStore.logout()
  currentToken.value = ''
  ElMessage.success('Token已清除')
}

// 测试API
const testApi = async () => {
  try {
    const response = await http.get('/system/user/page', {
      params: {
        current: 1,
        size: 10
      }
    })
    apiResult.value = response
    ElMessage.success('API测试成功')
  } catch (error) {
    console.error('API测试失败:', error)
    apiResult.value = error
    ElMessage.error('API测试失败')
  }
}

// 刷新
const refresh = () => {
  getCurrentToken()
  apiResult.value = null
}

onMounted(() => {
  getCurrentToken()
})
</script>

<style scoped>
.token-debug-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.debug-section {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.debug-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.token-info {
  display: grid;
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item label {
  font-weight: bold;
  min-width: 120px;
}

.success {
  color: #67c23a;
  font-weight: bold;
}

.error {
  color: #f56c6c;
  font-weight: bold;
}

.token-preview {
  font-family: monospace;
  background: #e0e0e0;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.token-full {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.token-full pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
}

.no-token {
  color: #999;
  font-style: italic;
}

.api-test {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.test-result {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.test-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
}
</style>