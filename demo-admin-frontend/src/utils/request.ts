/**
 * HTTP请求封装
 * <AUTHOR>
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import type { ApiResponse } from '@/types/common'

// Token刷新状态管理
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: any) => void
  reject: (reason?: any) => void
}> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 创建用于文件下载的axios实例（绕过响应拦截器）
const downloadRequest: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 下载请求的拦截器（只添加认证token）
downloadRequest.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()

    // 添加认证token，并清理空格
    if (authStore.token) {
      config.headers = config.headers || {}
      // 清理token前后的空格
      const cleanToken = authStore.token.trim()
      config.headers.Authorization = `Bearer ${cleanToken}`
    }

    return config
  },
  (error) => {
    console.error('下载请求错误:', error)
    return Promise.reject(error)
  }
)

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const authStore = useAuthStore()

    // 添加认证token，并清理空格
    if (authStore.token) {
      config.headers = config.headers || {}
      // 清理token前后的空格
      const cleanToken = authStore.token.trim()
      config.headers.Authorization = `Bearer ${cleanToken}`
    }

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { code, message, data } = response.data

    // 请求成功
    if (code === 200) {
      return response.data
    }

    // 业务错误
    ElMessage.error(message || '请求失败')
    return Promise.reject(new Error(message || '请求失败'))
  },
  async (error) => {
    console.error('响应错误:', error)

    const originalRequest = error.config

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Token过期，尝试刷新
          if (!originalRequest._retry) {
            if (isRefreshing) {
              // 如果正在刷新，将请求加入队列
              return new Promise((resolve, reject) => {
                failedQueue.push({ resolve, reject })
              }).then(token => {
                originalRequest.headers.Authorization = `Bearer ${token}`
                return request(originalRequest)
              }).catch(err => {
                return Promise.reject(err)
              })
            }

            originalRequest._retry = true
            isRefreshing = true

            try {
              const authStore = useAuthStore()
              const newToken = await authStore.refreshAccessToken()
              processQueue(null, newToken)
              originalRequest.headers.Authorization = `Bearer ${newToken}`
              return request(originalRequest)
            } catch (refreshError) {
              processQueue(refreshError, null)
              // 刷新失败，跳转到登录页
              ElMessageBox.confirm(
                '登录状态已过期，请重新登录',
                '系统提示',
                {
                  confirmButtonText: '重新登录',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              ).then(() => {
                const authStore = useAuthStore()
                authStore.logout()
                router.push('/login')
              }).catch(() => {
                // 用户取消
              })
              return Promise.reject(refreshError)
            } finally {
              isRefreshing = false
            }
          }
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error(error.message || '请求失败')
    }

    return Promise.reject(error)
  }
)

// 封装请求方法
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.get(url, config)
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, data, config)
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.put(url, data, config)
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.delete(url, config)
  },

  upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    })
  },

  download<T = Blob>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return downloadRequest.get(url, {
      ...config,
      responseType: 'blob'
    }).then(response => response.data)
  }
}

export default request
