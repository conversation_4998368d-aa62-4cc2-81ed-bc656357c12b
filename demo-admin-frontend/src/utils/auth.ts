/**
 * 认证相关工具函数
 * <AUTHOR>
 */

const TOKEN_KEY = 'demo_admin_token'
const REFRESH_TOKEN_KEY = 'demo_admin_refresh_token'
const USER_INFO_KEY = 'demo_admin_user_info'

/**
 * 获取Token
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY)
}

/**
 * 设置Token
 */
export function setToken(token: string, rememberMe: boolean = false): void {
  if (rememberMe) {
    localStorage.setItem(TOKEN_KEY, token)
  } else {
    sessionStorage.setItem(TOKEN_KEY, token)
  }
}

/**
 * 移除Token
 */
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
}

/**
 * 获取刷新Token
 */
export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY) || sessionStorage.getItem(REFRESH_TOKEN_KEY)
}

/**
 * 设置刷新Token
 */
export function setRefreshToken(refreshToken: string, rememberMe: boolean = false): void {
  if (rememberMe) {
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
  } else {
    sessionStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
  }
}

/**
 * 移除刷新Token
 */
export function removeRefreshToken(): void {
  localStorage.removeItem(REFRESH_TOKEN_KEY)
  sessionStorage.removeItem(REFRESH_TOKEN_KEY)
}

/**
 * 获取用户信息
 */
export function getUserInfo(): any {
  const userInfo = localStorage.getItem(USER_INFO_KEY) || sessionStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo: any, rememberMe: boolean = false): void {
  if (rememberMe) {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
  } else {
    sessionStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
  }
}

/**
 * 移除用户信息
 */
export function removeUserInfo(): void {
  localStorage.removeItem(USER_INFO_KEY)
  sessionStorage.removeItem(USER_INFO_KEY)
}

/**
 * 清除所有认证信息
 */
export function clearAuth(): void {
  removeToken()
  removeRefreshToken()
  removeUserInfo()
}

/**
 * 检查是否已登录
 */
export function isAuthenticated(): boolean {
  return !!getToken()
}

/**
 * 检查是否有指定权限
 */
export function hasPermission(permission: string): boolean {
  const userInfo = getUserInfo()
  if (!userInfo || !userInfo.permissions) {
    return false
  }
  return userInfo.permissions.includes(permission)
}

/**
 * 检查是否有指定角色
 */
export function hasRole(role: string): boolean {
  const userInfo = getUserInfo()
  if (!userInfo || !userInfo.roles) {
    return false
  }
  return userInfo.roles.includes(role)
}

/**
 * 检查是否有任一权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查是否有任一角色
 */
export function hasAnyRole(roles: string[]): boolean {
  return roles.some(role => hasRole(role))
}
