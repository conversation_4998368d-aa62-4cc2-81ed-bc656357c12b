/**
 * 错误处理工具
 * <AUTHOR>
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  BUSINESS = 'BUSINESS',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  code?: string | number
  message: string
  details?: any
}

/**
 * 错误处理类
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param error 错误对象
   */
  static handleApiError(error: any): void {
    console.error('API Error:', error)

    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          this.showError({
            type: ErrorType.VALIDATION,
            code: status,
            message: data?.message || '请求参数错误'
          })
          break
        case 401:
          this.showError({
            type: ErrorType.PERMISSION,
            code: status,
            message: '登录已过期，请重新登录'
          })
          // 可以在这里触发登出逻辑
          break
        case 403:
          this.showError({
            type: ErrorType.PERMISSION,
            code: status,
            message: '没有权限访问该资源'
          })
          break
        case 404:
          this.showError({
            type: ErrorType.NETWORK,
            code: status,
            message: '请求的资源不存在'
          })
          break
        case 500:
          this.showError({
            type: ErrorType.NETWORK,
            code: status,
            message: '服务器内部错误'
          })
          break
        default:
          this.showError({
            type: ErrorType.NETWORK,
            code: status,
            message: data?.message || '网络请求失败'
          })
      }
    } else if (error.request) {
      // 网络错误
      this.showError({
        type: ErrorType.NETWORK,
        message: '网络连接失败，请检查网络设置'
      })
    } else {
      // 其他错误
      this.showError({
        type: ErrorType.UNKNOWN,
        message: error.message || '未知错误'
      })
    }
  }

  /**
   * 处理业务错误
   * @param error 业务错误
   */
  static handleBusinessError(error: any): void {
    this.showError({
      type: ErrorType.BUSINESS,
      code: error.code,
      message: error.message || '业务处理失败'
    })
  }

  /**
   * 处理表单验证错误
   * @param errors 验证错误
   */
  static handleValidationError(errors: Record<string, string[]>): void {
    const firstError = Object.values(errors)[0]?.[0]
    if (firstError) {
      this.showError({
        type: ErrorType.VALIDATION,
        message: firstError
      })
    }
  }

  /**
   * 显示错误信息
   * @param errorInfo 错误信息
   */
  static showError(errorInfo: ErrorInfo): void {
    const { type, message } = errorInfo

    switch (type) {
      case ErrorType.VALIDATION:
        ElMessage.warning(message)
        break
      case ErrorType.PERMISSION:
        ElMessage.error(message)
        break
      case ErrorType.NETWORK:
        ElNotification.error({
          title: '网络错误',
          message,
          duration: 5000
        })
        break
      case ErrorType.BUSINESS:
        ElMessage.error(message)
        break
      default:
        ElMessage.error(message)
    }
  }

  /**
   * 显示成功信息
   * @param message 成功信息
   */
  static showSuccess(message: string): void {
    ElMessage.success(message)
  }

  /**
   * 显示警告信息
   * @param message 警告信息
   */
  static showWarning(message: string): void {
    ElMessage.warning(message)
  }

  /**
   * 显示通知
   * @param options 通知选项
   */
  static showNotification(options: {
    title: string
    message: string
    type?: 'success' | 'warning' | 'info' | 'error'
    duration?: number
  }): void {
    const { title, message, type = 'info', duration = 4500 } = options
    
    ElNotification({
      title,
      message,
      type,
      duration
    })
  }
}

/**
 * 全局错误处理函数
 */
export function setupGlobalErrorHandler(): void {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    ErrorHandler.handleApiError(event.reason)
    event.preventDefault()
  })

  // 捕获全局JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    ErrorHandler.showError({
      type: ErrorType.UNKNOWN,
      message: '页面发生错误，请刷新重试'
    })
  })
}

// 导出常用方法
export const {
  handleApiError,
  handleBusinessError,
  handleValidationError,
  showError,
  showSuccess,
  showWarning,
  showNotification
} = ErrorHandler
