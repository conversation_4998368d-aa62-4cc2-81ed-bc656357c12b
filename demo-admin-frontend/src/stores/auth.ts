/**
 * 认证状态管理
 * <AUTHOR>
 */

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { http } from '@/utils/request'
import {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  removeRefreshToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  clearAuth
} from '@/utils/auth'
import type { LoginForm, UserInfo } from '@/types/common'

interface LoginResponse {
  token: string
  tokenType: string
  expiresIn: number
  userId: number
  username: string
  realName: string
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
  permissions: string[]
  roles: string[]
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const refreshToken = ref<string>(getRefreshToken() || '')
  const userInfo = ref<UserInfo | null>(getUserInfo())
  const permissions = ref<string[]>((userInfo.value && userInfo.value.permissions) ? userInfo.value.permissions : [])
  const roles = ref<string[]>((userInfo.value && userInfo.value.roles) ? userInfo.value.roles : [])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const username = computed(() => userInfo.value?.username || '')
  const realName = computed(() => userInfo.value?.realName || '')
  const avatar = computed(() => userInfo.value?.avatar || '')

  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      const response = await http.post<LoginResponse>('/auth/login', loginForm)
      const loginData = response.data

      // 构建用户信息对象
      const user: UserInfo = {
        id: loginData.userId,
        username: loginData.username,
        realName: loginData.realName,
        nickname: loginData.nickname,
        email: loginData.email,
        phone: loginData.phone,
        avatar: loginData.avatar,
        status: 1, // 默认状态为启用
        roles: loginData.roles || [],
        permissions: loginData.permissions || []
      }

      // 保存认证信息
      token.value = loginData.token
      refreshToken.value = '' // 后端暂时没有返回refreshToken
      userInfo.value = user
      permissions.value = loginData.permissions || []
      roles.value = loginData.roles || []

      // 持久化存储
      setToken(loginData.token, loginForm.rememberMe)
      setRefreshToken('', loginForm.rememberMe) // 暂时为空
      setUserInfo(user, loginForm.rememberMe)

      ElMessage.success('登录成功')

      // 跳转到首页
      router.push('/')

      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      await http.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除状态
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      permissions.value = []
      roles.value = []

      // 清除存储
      clearAuth()

      // 跳转到登录页
      router.push('/login')
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await http.get<UserInfo>('/auth/me')
      const user = response.data

      userInfo.value = user
      permissions.value = user.permissions || []
      roles.value = user.roles || []

      setUserInfo(user)

      return user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 刷新Token
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新Token')
      }

      const response = await http.post<{ token: string; refreshToken: string; expiresIn: number }>('/auth/refresh', {
        refreshToken: refreshToken.value
      })

      const { token: newToken, refreshToken: newRefreshToken } = response.data

      token.value = newToken
      refreshToken.value = newRefreshToken

      setToken(newToken)
      setRefreshToken(newRefreshToken)

      return newToken
    } catch (error) {
      console.error('刷新Token失败:', error)
      logout()
      throw error
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  // 检查任一权限
  const hasAnyPermission = (perms: string[]): boolean => {
    return perms.some(permission => hasPermission(permission))
  }

  // 检查任一角色
  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(role => hasRole(role))
  }

  return {
    // 状态
    token,
    refreshToken,
    userInfo,
    permissions,
    roles,

    // 计算属性
    isAuthenticated,
    username,
    realName,
    avatar,

    // 方法
    login,
    logout,
    fetchUserInfo,
    refreshAccessToken,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole
  }
})
