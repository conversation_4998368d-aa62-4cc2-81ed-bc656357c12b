/**
 * 应用状态管理
 * <AUTHOR>
 */

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref<boolean>(false)
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const theme = ref<'light' | 'dark'>('light')
  const language = ref<string>('zh-CN')
  const loading = ref<boolean>(false)
  const pageLoading = ref<boolean>(false)

  // 计算属性
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  // 设置设备类型
  const setDevice = (deviceType: 'desktop' | 'tablet' | 'mobile') => {
    device.value = deviceType
    
    // 移动端自动收起侧边栏
    if (deviceType === 'mobile') {
      sidebarCollapsed.value = true
    }
  }

  // 设置主题
  const setTheme = (themeType: 'light' | 'dark') => {
    theme.value = themeType
    document.documentElement.setAttribute('data-theme', themeType)
  }

  // 设置语言
  const setLanguage = (lang: string) => {
    language.value = lang
  }

  // 设置全局加载状态
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  // 设置页面加载状态
  const setPageLoading = (isLoading: boolean) => {
    pageLoading.value = isLoading
  }

  // 初始化应用设置
  const initApp = () => {
    // 从localStorage恢复设置
    const savedCollapsed = localStorage.getItem('sidebar_collapsed')
    if (savedCollapsed !== null) {
      sidebarCollapsed.value = JSON.parse(savedCollapsed)
    }

    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      setTheme(savedTheme as 'light' | 'dark')
    }

    const savedLanguage = localStorage.getItem('language')
    if (savedLanguage) {
      language.value = savedLanguage
    }

    // 检测设备类型
    const checkDevice = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDevice('mobile')
      } else if (width < 1024) {
        setDevice('tablet')
      } else {
        setDevice('desktop')
      }
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
  }

  // 保存设置到localStorage
  const saveSettings = () => {
    localStorage.setItem('sidebar_collapsed', JSON.stringify(sidebarCollapsed.value))
    localStorage.setItem('theme', theme.value)
    localStorage.setItem('language', language.value)
  }

  // 监听状态变化并保存
  const watchAndSave = () => {
    // 这里可以使用watch来监听状态变化并自动保存
    // 为了简化，我们在每次状态改变时手动调用saveSettings
  }

  return {
    // 状态
    sidebarCollapsed,
    device,
    theme,
    language,
    loading,
    pageLoading,

    // 计算属性
    isMobile,
    isTablet,
    isDesktop,

    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setDevice,
    setTheme,
    setLanguage,
    setLoading,
    setPageLoading,
    initApp,
    saveSettings
  }
})
