<!--
  默认布局组件
  <AUTHOR>
-->

<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div
      class="layout-sidebar"
      :class="{ 'is-collapsed': appStore.sidebarCollapsed }"
    >
      <div class="sidebar-logo">
        <img src="/favicon.ico" alt="Logo" class="logo-image">
        <span v-show="!appStore.sidebarCollapsed" class="logo-text">
          Demo Admin
        </span>
      </div>

      <el-scrollbar class="sidebar-menu-wrapper">
        <el-menu
          :default-active="activeMenu"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409eff"
          router
        >
          <sidebar-item
            v-for="route in menuRoutes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 主内容区域 -->
    <div class="layout-main">
      <!-- 顶部导航栏 -->
      <div class="layout-header">
        <div class="header-left">
          <el-button
            type="text"
            size="large"
            @click="appStore.toggleSidebar"
          >
            <el-icon>
              <Fold v-if="!appStore.sidebarCollapsed" />
              <Expand v-else />
            </el-icon>
          </el-button>

          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbList"
              :key="item.path"
              :to="item.path"
            >
              {{ item.meta?.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息下拉菜单 -->
          <el-dropdown @command="handleCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="authStore.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.realName || authStore.username }}</span>
              <el-icon class="arrow-down"><ArrowDown /></el-icon>
            </div>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  个人设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="layout-content">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  Fold,
  Expand,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import SidebarItem from '@/components/Layout/SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 菜单路由（根据权限过滤）
const menuRoutes = computed(() => {
  return router.getRoutes().filter(route => {
    // 过滤隐藏的路由
    if (route.meta?.hidden) return false

    // 只显示有子路由的路由
    if (!route.children || route.children.length === 0) return false

    // 检查路由权限
    if (route.meta?.permissions && route.meta.permissions.length > 0) {
      if (!authStore.hasAnyPermission(route.meta.permissions)) return false
    }

    // 检查路由角色
    if (route.meta?.roles && route.meta.roles.length > 0) {
      if (!authStore.hasAnyRole(route.meta.roles)) return false
    }

    // 检查是否有可访问的子路由
    const visibleChildren = route.children.filter(child => {
      if (child.meta?.hidden) return false

      // 检查子路由权限
      if (child.meta?.permissions && child.meta.permissions.length > 0) {
        return authStore.hasAnyPermission(child.meta.permissions)
      }

      // 检查子路由角色
      if (child.meta?.roles && child.meta.roles.length > 0) {
        return authStore.hasAnyRole(child.meta.roles)
      }

      return true
    })

    // 如果没有可访问的子路由，则不显示该菜单
    return visibleChildren.length > 0
  })
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched
})

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile/index')
      break
    case 'settings':
      router.push('/profile/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await authStore.logout()
      } catch (error) {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

.layout-sidebar {
  width: $sidebar-width;
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;

  &.is-collapsed {
    width: $sidebar-collapsed-width;
  }

  .sidebar-logo {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: $header-height;
    background-color: #2b3a4b;

    .logo-image {
      width: 32px;
      height: 32px;
    }

    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      white-space: nowrap;
    }
  }

  .sidebar-menu-wrapper {
    height: calc(100vh - #{$header-height});
  }
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: $header-height;
  padding: 0 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .header-left {
    display: flex;
    align-items: center;

    .breadcrumb {
      margin-left: 20px;
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      .username {
        margin: 0 8px;
        font-size: 14px;
        color: #303133;
      }

      .arrow-down {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.layout-content {
  flex: 1;
  padding: 20px;
  background-color: #f2f3f5;
  overflow-y: auto;
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;

    &:not(.is-collapsed) {
      width: $sidebar-width;
    }
  }

  .layout-main {
    margin-left: 0;
  }

  .layout-header .header-left .breadcrumb {
    display: none;
  }
}
</style>
