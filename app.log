[INFO] Scanning for projects...
[INFO] 
[INFO] --------------------< com.example.admin:demo-admin >--------------------
[INFO] Building demo-admin 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.3.6:run (default-cli) > test-compile @ demo-admin >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ demo-admin ---
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] Copying 18 resources from src\main\resources to target\classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ demo-admin ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ demo-admin ---
[INFO] skip non existing resourceDirectory D:\github\demo-admin\src\test\resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ demo-admin ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] <<< spring-boot:3.3.6:run (default-cli) < test-compile @ demo-admin <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.3.6:run (default-cli) @ demo-admin ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.3.6)

2025-08-01 14:45:25.603 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-01 14:45:25.633 [main] INFO  com.example.admin.DemoAdminApplication - Starting DemoAdminApplication using Java 21.0.2 with PID 21420 (D:\github\demo-admin\target\classes started by fumouren in D:\github\demo-admin)
2025-08-01 14:45:25.634 [main] DEBUG com.example.admin.DemoAdminApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-08-01 14:45:25.634 [main] INFO  com.example.admin.DemoAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-01 14:45:27.953 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-01 14:45:27.955 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 14:45:27.955 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.33]
2025-08-01 14:45:28.014 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /************* network interface 
Get network interface info: name:wireless_32768 (Realtek 8852CE WiFi 6E PCI-E NIC)
Initialization Sequence datacenterId:11 workerId:19
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.9 
2025-08-01 14:45:28.900 [main] DEBUG c.example.admin.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-01 14:45:28.966 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 14:45:29.003 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
2025-08-01 14:45:29.026 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-01 14:45:29.262 [main] INFO  com.zaxxer.hikari.HikariDataSource - DemoAdminHikariCP - Starting...
2025-08-01 14:45:29.505 [main] INFO  com.zaxxer.hikari.pool.HikariPool - DemoAdminHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e111aeb
2025-08-01 14:45:29.507 [main] INFO  com.zaxxer.hikari.HikariDataSource - DemoAdminHikariCP - Start completed.
2025-08-01 14:45:29.530 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01 14:45:29.531 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-08-01 14:45:29.737 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01 14:45:30.466 [main] INFO  c.e.admin.service.impl.ConfigCacheServiceImpl - 初始化系统配置缓存
2025-08-01 14:45:30.467 [main] INFO  c.e.admin.service.impl.ConfigCacheServiceImpl - 刷新所有配置缓存
2025-08-01 14:45:30.467 [main] INFO  c.e.admin.service.impl.ConfigCacheServiceImpl - 清空所有配置缓存
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1377af49] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@932205110 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id, config_key, config_value, config_type, description, is_encrypted, is_system, status, create_time, update_time, create_by, update_by, deleted FROM sys_config WHERE status = 1 AND deleted = 0 ORDER BY config_key ASC
==> Parameters: 
<==    Columns: id, config_key, config_value, config_type, description, is_encrypted, is_system, status, create_time, update_time, create_by, update_by, deleted
<==        Row: 12, file.upload.allowedTypes, <<BLOB>>, STRING, �����ϴ����ļ�����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 11, file.upload.maxSize, <<BLOB>>, NUMBER, �ļ��ϴ�����С��MB��, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 13, file.upload.path, <<BLOB>>, STRING, �ļ��ϴ�·��, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 18, mail.from, <<BLOB>>, STRING, �ʼ�������, 0, 0, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 14, mail.host, <<BLOB>>, STRING, �ʼ���������ַ, 0, 0, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 17, mail.password, <<BLOB>>, STRING, �ʼ�����, 1, 0, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 15, mail.port, <<BLOB>>, NUMBER, �ʼ��������˿�, 0, 0, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 16, mail.username, <<BLOB>>, STRING, �ʼ��û���, 0, 0, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 21, profile.avatar.allowedTypes, <<BLOB>>, STRING, �����ͷ���ļ�����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 20, profile.avatar.maxSize, <<BLOB>>, NUMBER, ͷ���ļ�����С��MB��, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 23, profile.device.maxDevices, <<BLOB>>, NUMBER, �û�����豸����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 19, profile.module.enabled, <<BLOB>>, BOOLEAN, ��������ģ���Ƿ�����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 24, profile.notification.enabled, <<BLOB>>, BOOLEAN, �Ƿ����ø�������֪ͨ, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 22, profile.password.historySize, <<BLOB>>, NUMBER, ������ʷ��¼��������, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 8, security.login.failMaxCount, <<BLOB>>, NUMBER, ��¼ʧ��������, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 9, security.login.lockTime, <<BLOB>>, NUMBER, �˻�����ʱ�䣨���ӣ�, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 6, security.password.minLength, <<BLOB>>, NUMBER, ������С����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 7, security.password.requireSpecial, <<BLOB>>, BOOLEAN, �����Ƿ�Ҫ�������ַ�, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 10, security.session.timeout, <<BLOB>>, NUMBER, �Ự��ʱʱ�䣨���ӣ�, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 5, system.copyright, <<BLOB>>, STRING, ��Ȩ��Ϣ, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 3, system.description, <<BLOB>>, STRING, ϵͳ����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 4, system.logo, <<BLOB>>, STRING, ϵͳLogo, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 1, system.name, <<BLOB>>, STRING, ϵͳ����, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==        Row: 2, system.version, <<BLOB>>, STRING, ϵͳ�汾, 0, 1, 1, 2025-08-01 10:19:30, 2025-08-01 10:19:30, 1, 1, 0
<==      Total: 24
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1377af49]
2025-08-01 14:45:30.840 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: file.upload.allowedTypes = jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
2025-08-01 14:45:30.843 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: file.upload.maxSize = 10
2025-08-01 14:45:30.845 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: file.upload.path = /uploads
2025-08-01 14:45:30.846 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: mail.from = 
2025-08-01 14:45:30.848 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: mail.host = 
2025-08-01 14:45:30.849 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: mail.password = 
2025-08-01 14:45:30.851 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: mail.port = 587
2025-08-01 14:45:30.852 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: mail.username = 
2025-08-01 14:45:30.853 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.avatar.allowedTypes = jpg,jpeg,png,gif
2025-08-01 14:45:30.855 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.avatar.maxSize = 5
2025-08-01 14:45:30.857 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.device.maxDevices = 10
2025-08-01 14:45:30.859 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.module.enabled = true
2025-08-01 14:45:30.861 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.notification.enabled = true
2025-08-01 14:45:30.863 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: profile.password.historySize = 5
2025-08-01 14:45:30.864 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: security.login.failMaxCount = 5
2025-08-01 14:45:30.865 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: security.login.lockTime = 30
2025-08-01 14:45:30.866 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: security.password.minLength = 6
2025-08-01 14:45:30.868 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: security.password.requireSpecial = false
2025-08-01 14:45:30.869 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: security.session.timeout = 30
2025-08-01 14:45:30.870 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: system.copyright = © 2025 Demo Admin
2025-08-01 14:45:30.872 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: system.description = 后台管理系统
2025-08-01 14:45:30.873 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: system.logo = 
2025-08-01 14:45:30.874 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: system.name = Demo Admin
2025-08-01 14:45:30.875 [main] DEBUG c.e.admin.service.impl.ConfigCacheServiceImpl - 设置配置缓存: system.version = 1.0.0
2025-08-01 14:45:30.875 [main] INFO  c.e.admin.service.impl.ConfigCacheServiceImpl - 配置缓存刷新完成，共加载 24 个配置
2025-08-01 14:45:31.845 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 14:45:35.569 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-01 14:45:35.608 [main] INFO  com.example.admin.DemoAdminApplication - Started DemoAdminApplication in 10.588 seconds (process running for 11.035)
2025-08-01 14:46:25.528 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:46:25.556 [http-nio-8080-exec-1] DEBUG c.example.admin.security.JwtAuthenticationFilter - 请求路径: /api/debug/permissions/all, JWT令牌: 未获取
2025-08-01 14:46:25.556 [http-nio-8080-exec-1] WARN  c.example.admin.security.JwtAuthenticationFilter - JWT令牌为空或验证失败
2025-08-01 14:46:25.596 [http-nio-8080-exec-1] INFO  c.e.admin.controller.PermissionDebugController - 获取所有权限
2025-08-01 14:46:25.596 [http-nio-8080-exec-1] INFO  c.e.admin.service.impl.SysPermissionServiceImpl - 查询权限列表: null
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48810c85] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1490627736 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE deleted=0 AND (deleted = ?) ORDER BY sort_order ASC
==> Parameters: 0(Integer)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 304, profile:password:edit, �޸�����, 2, 303, null, �޸ĸ�������Ȩ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 302, profile:info:edit, �޸���Ϣ, 2, 301, null, �޸ĸ�����ϢȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 301, profile:info, ������Ϣ, 1, 300, /profile/info, ������Ϣҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 205, monitor:online:list, �����û���ѯ, 2, 204, null, �����û��б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 202, monitor:log:list, ��־��ѯ, 2, 201, null, ��־�б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 201, monitor:log, ��־����, 1, 200, /monitor/log, ��־����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 128, system:log:list, ��־��ѯ, 2, 127, null, ��־�б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 123, system:config:list, ���ò�ѯ, 2, 122, null, �����б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 118, system:menu:list, �˵���ѯ, 2, 117, null, �˵��б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 112, system:role:list, ��ɫ��ѯ, 2, 111, null, ��ɫ�б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 102, system:user:list, �û���ѯ, 2, 101, null, �û��б��ѯȨ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 113, system:role:add, ��ɫ����, 2, 111, null, ��ɫ����Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 206, monitor:online:kick, ǿ������, 2, 204, null, ǿ���û�����Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 204, monitor:online, �����û�, 1, 200, /monitor/online, �����û�ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 203, monitor:log:delete, ��־ɾ��, 2, 201, null, ��־ɾ��Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 303, profile:password, �޸�����, 1, 300, /profile/password, �޸�����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 119, system:menu:add, �˵�����, 2, 117, null, �˵�����Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 200, monitor, ϵͳ���, 1, 0, /monitor, ϵͳ���ģ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 129, system:log:view, ��־�鿴, 2, 127, null, ��־����鿴Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 124, system:config:add, ��������, 2, 122, null, ��������Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 103, system:user:add, �û�����, 2, 101, null, �û�����Ȩ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 300, profile, ��������, 1, 0, /profile, ��������ģ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 104, system:user:edit, �û��޸�, 2, 101, null, �û��޸�Ȩ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 130, system:log:export, ��־����, 2, 127, null, ��־���ݵ���Ȩ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 125, system:config:edit, �����޸�, 2, 122, null, �����޸�Ȩ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 120, system:menu:edit, �˵��޸�, 2, 117, null, �˵��޸�Ȩ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 117, system:menu, �˵�����, 1, 100, /system/menu, �˵�����ҳ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 114, system:role:edit, ��ɫ�޸�, 2, 111, null, ��ɫ�޸�Ȩ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 126, system:config:delete, ����ɾ��, 2, 122, null, ����ɾ��Ȩ��, 1, 4, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 121, system:menu:delete, �˵�ɾ��, 2, 117, null, �˵�ɾ��Ȩ��, 1, 4, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 131, system:log:clean, ��־����, 2, 127, null, ��־����Ȩ��, 1, 4, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 105, system:user:delete, �û�ɾ��, 2, 101, null, �û�ɾ��Ȩ��, 1, 4, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 115, system:role:delete, ��ɫɾ��, 2, 111, null, ��ɫɾ��Ȩ��, 1, 4, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 122, system:config, ϵͳ����, 1, 100, /system/config, ϵͳ����ҳ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 132, system:log:statistics, ��־ͳ��, 2, 127, null, ��־ͳ��Ȩ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 106, system:user:resetPassword, ��������, 2, 101, null, �����û�����Ȩ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 116, system:role:permission, ��ɫ��Ȩ, 2, 111, null, ��ɫȨ�޷���Ȩ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 107, system:user:export, �û�����, 2, 101, null, �û����ݵ���Ȩ��, 1, 6, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 108, system:user:import, �û�����, 2, 101, null, �û����ݵ���Ȩ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 109, system:user:updateStatus, ����״̬, 2, 101, null, �û�״̬����Ȩ��, 1, 8, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==        Row: 110, system:user:role, �����ɫ, 2, 101, null, �û���ɫ����Ȩ��, 1, 9, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 45
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48810c85]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41197dfc] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1849240398 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 100(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41197dfc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79b33c2a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1814331644 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 303(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 303, profile:password, �޸�����, 1, 300, /profile/password, �޸�����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79b33c2a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c941398] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1277569100 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 301(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 301, profile:info, ������Ϣ, 1, 300, /profile/info, ������Ϣҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c941398]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7560a6f5] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2045010706 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 300(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 300, profile, ��������, 1, 0, /profile, ��������ģ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7560a6f5]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b4d5d23] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@468322737 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 204(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 204, monitor:online, �����û�, 1, 200, /monitor/online, �����û�ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b4d5d23]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5e613e] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@788239795 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 201(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 201, monitor:log, ��־����, 1, 200, /monitor/log, ��־����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f5e613e]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@44d332be] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@416869515 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 200(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 200, monitor, ϵͳ���, 1, 0, /monitor, ϵͳ���ģ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@44d332be]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32815a6b] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1872418887 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 127(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32815a6b]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fabd27e] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@130123234 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 122(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 122, system:config, ϵͳ����, 1, 100, /system/config, ϵͳ����ҳ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fabd27e]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@404b3a77] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@554838050 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 117(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 117, system:menu, �˵�����, 1, 100, /system/menu, �˵�����ҳ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@404b3a77]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15854a41] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@201605636 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 111(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15854a41]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c3b9c9a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@324213790 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c3b9c9a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38cf94f9] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1214255158 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 111(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38cf94f9]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35070f49] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1907043660 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 204(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 204, monitor:online, �����û�, 1, 200, /monitor/online, �����û�ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35070f49]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38ed8d01] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@891138066 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 200(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 200, monitor, ϵͳ���, 1, 0, /monitor, ϵͳ���ģ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@38ed8d01]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37cbb6fb] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1662693510 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 201(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 201, monitor:log, ��־����, 1, 200, /monitor/log, ��־����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37cbb6fb]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7420db6] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1834229707 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 300(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 300, profile, ��������, 1, 0, /profile, ��������ģ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7420db6]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@186e7f66] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@257355360 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 117(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 117, system:menu, �˵�����, 1, 100, /system/menu, �˵�����ҳ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@186e7f66]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79153113] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@317453336 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 127(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@79153113]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@601d3bb1] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@818938632 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 122(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 122, system:config, ϵͳ����, 1, 100, /system/config, ϵͳ����ҳ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@601d3bb1]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66d5082a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1872525298 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 100(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66d5082a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@112de702] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1601644885 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@112de702]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bceb33e] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1436894386 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bceb33e]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27079f85] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2039389976 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 127(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@27079f85]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@425d4e31] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1497176648 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 122(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 122, system:config, ϵͳ����, 1, 100, /system/config, ϵͳ����ҳ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@425d4e31]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9d5d53] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1956232247 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 117(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 117, system:menu, �˵�����, 1, 100, /system/menu, �˵�����ҳ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5c9d5d53]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2025e05e] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@384059084 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 100(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2025e05e]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522260c6] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1569349068 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 111(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@522260c6]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f0e6bb9] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@778769102 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 122(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 122, system:config, ϵͳ����, 1, 100, /system/config, ϵͳ����ҳ��, 1, 5, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f0e6bb9]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4730c460] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1684333994 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 117(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 117, system:menu, �˵�����, 1, 100, /system/menu, �˵�����ҳ��, 1, 3, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4730c460]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e5c3dc] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1178010756 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 127(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e5c3dc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a21494] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@284216159 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39a21494]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@448c467] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@761755342 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 111(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@448c467]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a306172] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2014962095 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 100(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3a306172]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ab1252b] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@867430142 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 127(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 127, system:log, ��־����, 1, 100, /system/log, ��־����ҳ��, 1, 7, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ab1252b]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b1af941] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@402726772 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b1af941]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3353f832] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2135937000 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 111(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 111, system:role, ��ɫ����, 1, 100, /system/role, ��ɫ����ҳ��, 1, 2, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3353f832]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@158233cc] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@960289574 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@158233cc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bc5ba78] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2136865439 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 100(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 100, system, ϵͳ����, 1, 0, /system, ϵͳ����ģ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7bc5ba78]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@73a22a80] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@635586806 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@73a22a80]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a5e58e2] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1221234947 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a5e58e2]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14f6f057] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1278681955 wrapping com.mysql.cj.jdbc.ConnectionImpl@6e111aeb] will not be managed by Spring
==>  Preparing: SELECT id,permission_code,permission_name,permission_type,parent_id,path,description,status,sort_order,is_system,remark,create_time,update_time,create_by,update_by,deleted,version FROM sys_permission WHERE id=? AND deleted=0
==> Parameters: 101(Long)
<==    Columns: id, permission_code, permission_name, permission_type, parent_id, path, description, status, sort_order, is_system, remark, create_time, update_time, create_by, update_by, deleted, version
<==        Row: 101, system:user, �û�����, 1, 100, /system/user, �û�����ҳ��, 1, 1, 1, ϵͳ����Ȩ��, 2025-08-01 13:31:45, 2025-08-01 13:31:45, 1, 1, 0, 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14f6f057]
2025-08-01 14:46:25.823 [http-nio-8080-exec-1] ERROR com.example.admin.aspect.ExceptionLogAspect - 异常日志 - 层级: SERVICE, 请求: GET /api/debug/permissions/all, 异常: NullPointerException, 错误: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
java.lang.NullPointerException: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
	at com.example.admin.service.impl.SysPermissionServiceImpl.getPermissionList(SysPermissionServiceImpl.java:244)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundService(PerformanceAspect.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.service.impl.SysPermissionServiceImpl$$SpringCGLIB$$0.getPermissionList(<generated>)
	at com.example.admin.controller.PermissionDebugController.getAllPermissions(PermissionDebugController.java:56)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundController(PerformanceAspect.java:44)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.controller.PermissionDebugController$$SpringCGLIB$$0.getAllPermissions(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.admin.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-01 14:46:25.825 [http-nio-8080-exec-1] INFO  com.example.admin.aspect.ExceptionLogAspect - 发送到异常监控系统: {logType=EXCEPTION_LOG, method=GET, ip=0:0:0:0:0:0:0:1, userAgent=curl/8.7.1, exceptionClass=NullPointerException, requestUri=/api/debug/permissions/all, stackTrace=com.example.admin.service.impl.SysPermissionServiceImpl.getPermissionList(SysPermissionServiceImpl.java:244)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
com.example.admin.aspect.PerformanceAspect.aroundService(PerformanceAspect.java:52)
, layer=SERVICE, exceptionMessage=Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null, timestamp=2025-08-01T14:46:25.823217900}
2025-08-01 14:46:25.826 [http-nio-8080-exec-1] ERROR com.example.admin.aspect.ExceptionLogAspect - 异常告警 - 层级: SERVICE, 级别: CRITICAL, 异常: NullPointerException, 错误: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
2025-08-01 14:46:25.826 [http-nio-8080-exec-1] INFO  com.example.admin.aspect.ExceptionLogAspect - 发送异常告警: {alertType=EXCEPTION_ALERT, alertLevel=CRITICAL, exceptionClass=NullPointerException, exceptionMessage=Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null, layer=SERVICE, timestamp=2025-08-01T14:46:25.825836100}
2025-08-01 14:46:25.826 [http-nio-8080-exec-1] ERROR com.example.admin.aspect.ExceptionLogAspect - 异常日志 - 层级: CONTROLLER, 请求: GET /api/debug/permissions/all, 异常: NullPointerException, 错误: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
java.lang.NullPointerException: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
	at com.example.admin.service.impl.SysPermissionServiceImpl.getPermissionList(SysPermissionServiceImpl.java:244)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundService(PerformanceAspect.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.service.impl.SysPermissionServiceImpl$$SpringCGLIB$$0.getPermissionList(<generated>)
	at com.example.admin.controller.PermissionDebugController.getAllPermissions(PermissionDebugController.java:56)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundController(PerformanceAspect.java:44)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.controller.PermissionDebugController$$SpringCGLIB$$0.getAllPermissions(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.admin.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-01 14:46:25.827 [http-nio-8080-exec-1] INFO  com.example.admin.aspect.ExceptionLogAspect - 发送到异常监控系统: {logType=EXCEPTION_LOG, method=GET, ip=0:0:0:0:0:0:0:1, userAgent=curl/8.7.1, exceptionClass=NullPointerException, requestUri=/api/debug/permissions/all, stackTrace=com.example.admin.service.impl.SysPermissionServiceImpl.getPermissionList(SysPermissionServiceImpl.java:244)
java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
java.base/java.lang.reflect.Method.invoke(Method.java:580)
org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
com.example.admin.aspect.PerformanceAspect.aroundService(PerformanceAspect.java:52)
, layer=CONTROLLER, exceptionMessage=Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null, timestamp=2025-08-01T14:46:25.826365700}
2025-08-01 14:46:25.827 [http-nio-8080-exec-1] ERROR com.example.admin.aspect.ExceptionLogAspect - 异常告警 - 层级: CONTROLLER, 级别: CRITICAL, 异常: NullPointerException, 错误: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
2025-08-01 14:46:25.827 [http-nio-8080-exec-1] INFO  com.example.admin.aspect.ExceptionLogAspect - 发送异常告警: {alertType=EXCEPTION_ALERT, alertLevel=CRITICAL, exceptionClass=NullPointerException, exceptionMessage=Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null, layer=CONTROLLER, timestamp=2025-08-01T14:46:25.827943700}
2025-08-01 14:46:25.829 [http-nio-8080-exec-1] ERROR c.e.admin.common.exception.GlobalExceptionHandler - 运行时异常: 
java.lang.NullPointerException: Cannot invoke "com.example.admin.dto.request.PermissionQueryRequest.getTreeStructure()" because "request" is null
	at com.example.admin.service.impl.SysPermissionServiceImpl.getPermissionList(SysPermissionServiceImpl.java:244)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundService(PerformanceAspect.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.service.impl.SysPermissionServiceImpl$$SpringCGLIB$$0.getPermissionList(<generated>)
	at com.example.admin.controller.PermissionDebugController.getAllPermissions(PermissionDebugController.java:56)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.admin.aspect.PerformanceAspect.monitorPerformance(PerformanceAspect.java:70)
	at com.example.admin.aspect.PerformanceAspect.aroundController(PerformanceAspect.java:44)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.example.admin.controller.PermissionDebugController$$SpringCGLIB$$0.getAllPermissions(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.example.admin.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:25 min
[INFO] Finished at: 2025-08-01T14:46:47+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.3.6:run (default-cli) on project demo-admin: Process terminated with exit code: 1 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
