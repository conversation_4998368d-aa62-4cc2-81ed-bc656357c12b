package com.example.admin;

import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码测试类
 * 用于验证BCrypt密码编码和验证
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class PasswordTest {

    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Test
    public void testPasswordEncoding() {
        String rawPassword = "123456";
        String encodedPassword = passwordEncoder.encode(rawPassword);
        
        System.out.println("原始密码: " + rawPassword);
        System.out.println("编码后密码: " + encodedPassword);
        
        // 验证密码
        boolean matches = passwordEncoder.matches(rawPassword, encodedPassword);
        System.out.println("密码验证结果: " + matches);
    }

    @Test
    public void testExistingPassword() {
        String rawPassword = "123456";
        String existingEncodedPassword = "$2a$10$7JB720yubVSOfvVWbazBuOWShWzhlicrXMmcvxoXosFRUPuHd/AhK";
        
        System.out.println("原始密码: " + rawPassword);
        System.out.println("数据库中的编码密码: " + existingEncodedPassword);
        
        // 验证现有的编码密码是否正确
        boolean matches = passwordEncoder.matches(rawPassword, existingEncodedPassword);
        System.out.println("现有密码验证结果: " + matches);
    }

    @Test
    public void generateNewPassword() {
        String rawPassword = "123456";
        String newEncodedPassword = passwordEncoder.encode(rawPassword);
        
        System.out.println("新生成的BCrypt密码: " + newEncodedPassword);
        
        // 验证新生成的密码
        boolean matches = passwordEncoder.matches(rawPassword, newEncodedPassword);
        System.out.println("新密码验证结果: " + matches);
    }
}
