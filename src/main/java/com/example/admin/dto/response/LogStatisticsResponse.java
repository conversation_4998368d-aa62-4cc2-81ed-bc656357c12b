package com.example.admin.dto.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 日志统计响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
public class LogStatisticsResponse {
    
    /**
     * 总日志数量
     */
    private Long totalLogCount;
    
    /**
     * 异常日志数量
     */
    private Long exceptionLogCount;
    
    /**
     * 性能日志数量
     */
    private Long performanceLogCount;
    
    /**
     * 成功日志数量
     */
    private Long successLogCount;
    
    /**
     * 失败日志数量
     */
    private Long failedLogCount;
    
    /**
     * 慢查询数量
     */
    private Long slowQueryCount;
    
    /**
     * 平均执行时间 (毫秒)
     */
    private Double avgExecutionTime;
    
    /**
     * 最小执行时间 (毫秒)
     */
    private Long minExecutionTime;
    
    /**
     * 最大执行时间 (毫秒)
     */
    private Long maxExecutionTime;
    
    /**
     * 错误率 (%)
     */
    private Double errorRate;
    
    /**
     * 成功率 (%)
     */
    private Double successRate;
    
    /**
     * 日志类型统计
     */
    private List<Map<String, Object>> logTypeStatistics;
    
    /**
     * 用户统计
     */
    private List<Map<String, Object>> userStatistics;
    
    /**
     * 模块统计
     */
    private List<Map<String, Object>> moduleStatistics;
    
    /**
     * 操作统计
     */
    private List<Map<String, Object>> operationStatistics;
    
    /**
     * 性能指标
     */
    private Map<String, Object> performanceMetrics;
    
    /**
     * 日期趋势数据
     */
    private List<Map<String, Object>> dateTrend;
    
    /**
     * 模块分布数据
     */
    private List<Map<String, Object>> moduleDistribution;
    
    /**
     * 用户活跃度数据
     */
    private List<Map<String, Object>> userActivity;
    
    /**
     * 操作频率数据
     */
    private List<Map<String, Object>> operationFrequency;
    
    /**
     * 性能趋势数据
     */
    private List<Map<String, Object>> performanceTrend;
    
}