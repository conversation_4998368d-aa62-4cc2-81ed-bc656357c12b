package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 个人信息响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class ProfileResponse {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别（0：女，1：男，2：未知）
     */
    private Integer gender;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate birthday;

    /**
     * 所在地
     */
    private String location;

    /**
     * 个人网站
     */
    private String website;

    /**
     * GitHub地址
     */
    private String github;

    /**
     * Twitter地址
     */
    private String twitter;

    /**
     * LinkedIn地址
     */
    private String linkedin;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 语言偏好
     */
    private String language;

    /**
     * 主题偏好
     */
    private String theme;

    /**
     * 邮件通知设置
     */
    private Integer emailNotifications;

    /**
     * 短信通知设置
     */
    private Integer smsNotifications;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 最后登录设备
     */
    private String lastLoginDevice;

    /**
     * 登录次数
     */
    private Integer loginCount;

    /**
     * 密码最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime passwordUpdateTime;

    /**
     * 用户偏好设置
     */
    private Map<String, Object> preferences;

    /**
     * 用户设备列表
     */
    private List<DeviceResponse> devices;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
