package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class PermissionResponse {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 权限编码
     */
    private String permissionCode;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限类型（1：菜单，2：按钮，3：接口）
     */
    private Integer permissionType;

    /**
     * 权限类型描述
     */
    private String permissionTypeDesc;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 父权限名称
     */
    private String parentName;

    /**
     * 权限路径
     */
    private String path;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否系统内置权限（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 子权限列表（树形结构时使用）
     */
    private List<PermissionResponse> children;

    /**
     * 是否有子权限
     */
    private Boolean hasChildren;

    /**
     * 权限层级
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
