package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class RoleResponse {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 数据权限范围（1：全部数据，2：本部门及以下数据，3：本部门数据，4：仅本人数据，5：自定义数据）
     */
    private Integer dataScope;

    /**
     * 数据权限范围描述
     */
    private String dataScopeDesc;

    /**
     * 是否系统内置角色（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 权限列表
     */
    private List<PermissionInfo> permissions;

    /**
     * 用户数量
     */
    private Integer userCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 权限信息内部类
     */
    @Data
    public static class PermissionInfo {
        /**
         * 权限ID
         */
        private Long id;

        /**
         * 权限编码
         */
        private String code;

        /**
         * 权限名称
         */
        private String name;

        /**
         * 权限类型（1：菜单，2：按钮，3：接口）
         */
        private Integer type;

        /**
         * 权限类型描述
         */
        private String typeDesc;

        /**
         * 权限描述
         */
        private String description;
    }
}
