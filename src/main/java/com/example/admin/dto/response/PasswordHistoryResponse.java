package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 密码修改历史响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class PasswordHistoryResponse {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 修改类型
     */
    private String changeType;

    /**
     * 修改类型描述
     */
    private String changeTypeDesc;

    /**
     * 修改原因
     */
    private String changeReason;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}