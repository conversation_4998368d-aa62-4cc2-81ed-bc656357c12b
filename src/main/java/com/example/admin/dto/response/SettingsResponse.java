package com.example.admin.dto.response;

import lombok.Data;

/**
 * 个人设置响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class SettingsResponse {

    /**
     * 主题设置
     */
    private String theme;

    /**
     * 语言设置
     */
    private String language;

    /**
     * 时区设置
     */
    private String timezone;

    /**
     * 日期格式
     */
    private String dateFormat;

    /**
     * 时间格式（12/24）
     */
    private String timeFormat;

    /**
     * 菜单是否折叠（0：展开，1：折叠）
     */
    private Integer menuCollapsed;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 是否启用通知（0：禁用，1：启用）
     */
    private Integer notificationEnabled;

    /**
     * 邮件通知（0：禁用，1：启用）
     */
    private Integer emailNotification;

    /**
     * 短信通知（0：禁用，1：启用）
     */
    private Integer smsNotification;
}
