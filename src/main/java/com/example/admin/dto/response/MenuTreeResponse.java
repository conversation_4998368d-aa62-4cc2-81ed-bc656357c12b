package com.example.admin.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 菜单树形响应DTO（用于前端路由生成）
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class MenuTreeResponse {

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单标题
     */
    private String title;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 路由参数
     */
    private String query;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 是否外链（0：否，1：是）
     */
    private Integer isFrame;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    private Integer isCache;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    private Integer visible;

    /**
     * 菜单类型（1：目录，2：菜单，3：按钮）
     */
    private Integer menuType;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 子菜单列表
     */
    private List<MenuTreeResponse> children;

    /**
     * 路由元信息
     */
    private Meta meta;

    /**
     * 路由元信息内部类
     */
    @Data
    public static class Meta {
        /**
         * 菜单标题
         */
        private String title;

        /**
         * 菜单图标
         */
        private String icon;

        /**
         * 是否隐藏
         */
        private Boolean hidden;

        /**
         * 是否缓存
         */
        private Boolean keepAlive;

        /**
         * 是否外链
         */
        private Boolean isFrame;

        /**
         * 权限标识
         */
        private String[] permissions;

        /**
         * 排序
         */
        private Integer sortOrder;
    }
}
