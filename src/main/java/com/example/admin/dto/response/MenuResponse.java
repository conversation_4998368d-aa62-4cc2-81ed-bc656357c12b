package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class MenuResponse {

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单标题（用于国际化）
     */
    private String menuTitle;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 父菜单名称
     */
    private String parentName;

    /**
     * 菜单类型（1：目录，2：菜单，3：按钮）
     */
    private Integer menuType;

    /**
     * 菜单类型描述
     */
    private String menuTypeDesc;

    /**
     * 路由路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 路由参数
     */
    private String query;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 是否外链（0：否，1：是）
     */
    private Integer isFrame;

    /**
     * 是否外链描述
     */
    private String isFrameDesc;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    private Integer isCache;

    /**
     * 是否缓存描述
     */
    private String isCacheDesc;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    private Integer visible;

    /**
     * 是否显示描述
     */
    private String visibleDesc;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 权限标识
     */
    private String perms;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否系统内置菜单（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 子菜单列表（树形结构时使用）
     */
    private List<MenuResponse> children;

    /**
     * 是否有子菜单
     */
    private Boolean hasChildren;

    /**
     * 菜单层级
     */
    private Integer level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
