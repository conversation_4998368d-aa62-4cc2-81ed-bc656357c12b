package com.example.admin.dto.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 仪表盘统计响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
public class DashboardResponse {

    /**
     * 用户总数
     */
    private Long userCount;

    /**
     * 角色数量
     */
    private Long roleCount;

    /**
     * 菜单数量
     */
    private Long menuCount;

    /**
     * 在线用户数量
     */
    private Long onlineUserCount;

    /**
     * 今日活跃用户数
     */
    private Long todayActiveUserCount;

    /**
     * 系统信息
     */
    private Map<String, Object> systemInfo;

    /**
     * 最近活动
     */
    private List<Map<String, Object>> recentActivities;

    /**
     * 用户增长趋势 (最近7天)
     */
    private List<Map<String, Object>> userGrowthTrend;

    /**
     * 系统负载信息
     */
    private Map<String, Object> systemLoad;

    /**
     * 统计时间
     */
    private String statisticsTime;
}