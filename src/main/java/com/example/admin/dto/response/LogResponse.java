package com.example.admin.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 日志响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
public class LogResponse {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 日志类型
     */
    private String logType;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 操作描述
     */
    private String description;
    
    /**
     * 请求URI
     */
    private String requestUri;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 执行时间 (毫秒)
     */
    private Long executionTime;
    
    /**
     * 操作状态 (true:成功, false:失败)
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 浏览器类型
     */
    private String browser;
    
    /**
     * 操作系统
     */
    private String os;
    
    /**
     * 日志级别
     */
    private String level;
    
    /**
     * 异常类型
     */
    private String exceptionType;
    
    /**
     * 线程名称
     */
    private String threadName;
    
}