package com.example.admin.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 用户导出数据DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class UserExportResponse {

    @ExcelProperty(value = "ID", index = 0)
    @ColumnWidth(10)
    private Long id;

    @ExcelProperty(value = "用户名", index = 1)
    @ColumnWidth(20)
    private String username;

    @ExcelProperty(value = "真实姓名", index = 2)
    @ColumnWidth(20)
    private String realName;

    @ExcelProperty(value = "昵称", index = 3)
    @ColumnWidth(20)
    private String nickname;

    @ExcelProperty(value = "邮箱", index = 4)
    @ColumnWidth(30)
    private String email;

    @ExcelProperty(value = "手机号", index = 5)
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty(value = "性别", index = 6)
    @ColumnWidth(10)
    private String gender;

    @ExcelProperty(value = "状态", index = 7)
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "最后登录时间", index = 8)
    @ColumnWidth(20)
    private String lastLoginTime;

    @ExcelProperty(value = "创建时间", index = 9)
    @ColumnWidth(20)
    private String createTime;

    @ExcelProperty(value = "备注", index = 10)
    @ColumnWidth(30)
    private String remark;

    /**
     * 性别转换
     */
    public void setGender(Integer gender) {
        if (gender == null) {
            this.gender = "未知";
        } else {
            switch (gender) {
                case 1:
                    this.gender = "男";
                    break;
                case 0:
                    this.gender = "女";
                    break;
                default:
                    this.gender = "未知";
            }
        }
    }

    /**
     * 状态转换
     */
    public void setStatus(Integer status) {
        if (status == null) {
            this.status = "未知";
        } else {
            switch (status) {
                case 1:
                    this.status = "启用";
                    break;
                case 0:
                    this.status = "禁用";
                    break;
                default:
                    this.status = "未知";
            }
        }
    }

    /**
     * 时间格式化
     */
    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        if (lastLoginTime != null) {
            this.lastLoginTime = lastLoginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            this.lastLoginTime = "";
        }
    }

    /**
     * 时间格式化
     */
    public void setCreateTime(LocalDateTime createTime) {
        if (createTime != null) {
            this.createTime = createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            this.createTime = "";
        }
    }
}