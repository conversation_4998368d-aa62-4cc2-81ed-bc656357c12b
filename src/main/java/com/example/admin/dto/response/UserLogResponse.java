package com.example.admin.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户日志响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class UserLogResponse {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 响应结果（SUCCESS/FAIL）
     */
    private String responseResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 操作地点
     */
    private String location;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
