package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;

/**
 * 角色权限分配请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class RolePermissionAssignRequest {

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    /**
     * 权限ID列表
     */
    @NotEmpty(message = "权限ID列表不能为空")
    private Long[] permissionIds;

    /**
     * 是否覆盖原有权限（true：覆盖，false：追加）
     */
    private Boolean override = true;
}
