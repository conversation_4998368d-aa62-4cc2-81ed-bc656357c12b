package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 权限查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class PermissionQueryRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 权限编码（模糊查询）
     */
    private String permissionCode;

    /**
     * 权限名称（模糊查询）
     */
    private String permissionName;

    /**
     * 权限类型（1：菜单，2：按钮，3：接口）
     */
    private Integer permissionType;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 是否系统内置权限（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 排序字段
     */
    private String sortField = "sortOrder";

    /**
     * 排序方向（asc：升序，desc：降序）
     */
    private String sortOrder = "asc";

    /**
     * 是否查询树形结构（true：返回树形结构，false：返回列表结构）
     */
    private Boolean treeStructure = false;
}
