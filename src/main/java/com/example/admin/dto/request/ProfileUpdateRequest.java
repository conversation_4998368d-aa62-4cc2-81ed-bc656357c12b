package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 个人信息更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class ProfileUpdateRequest {

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;

    /**
     * 生日
     */
    @Past(message = "生日必须是过去的日期")
    private LocalDate birthday;

    /**
     * 性别：0-女，1-男，2-未知
     */
    @Min(value = 0, message = "性别值无效")
    @Max(value = 2, message = "性别值无效")
    private Integer gender;

    /**
     * 所在地
     */
    @Size(max = 100, message = "所在地长度不能超过100个字符")
    private String location;

    /**
     * 个人网站
     */
    @Size(max = 255, message = "个人网站长度不能超过255个字符")
    @Pattern(regexp = "^https?://.*", message = "个人网站必须是有效的URL")
    private String website;

    /**
     * GitHub地址
     */
    @Size(max = 255, message = "GitHub地址长度不能超过255个字符")
    @Pattern(regexp = "^https?://(www\\.)?github\\.com/.*", message = "GitHub地址格式不正确")
    private String github;

    /**
     * Twitter地址
     */
    @Size(max = 255, message = "Twitter地址长度不能超过255个字符")
    @Pattern(regexp = "^https?://(www\\.)?twitter\\.com/.*", message = "Twitter地址格式不正确")
    private String twitter;

    /**
     * LinkedIn地址
     */
    @Size(max = 255, message = "LinkedIn地址长度不能超过255个字符")
    @Pattern(regexp = "^https?://(www\\.)?linkedin\\.com/.*", message = "LinkedIn地址格式不正确")
    private String linkedin;

    /**
     * 时区
     */
    @Size(max = 50, message = "时区长度不能超过50个字符")
    private String timezone;

    /**
     * 语言偏好
     */
    @Size(max = 10, message = "语言偏好长度不能超过10个字符")
    private String language;

    /**
     * 主题偏好：light-浅色，dark-深色
     */
    @Size(max = 20, message = "主题偏好长度不能超过20个字符")
    private String theme;

    /**
     * 邮件通知：0-关闭，1-开启
     */
    @Min(value = 0, message = "邮件通知设置无效")
    @Max(value = 1, message = "邮件通知设置无效")
    private Integer emailNotifications;

    /**
     * 短信通知：0-关闭，1-开启
     */
    @Min(value = 0, message = "短信通知设置无效")
    @Max(value = 1, message = "短信通知设置无效")
    private Integer smsNotifications;
}
