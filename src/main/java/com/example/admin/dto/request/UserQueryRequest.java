package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 用户查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class UserQueryRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 真实姓名（模糊查询）
     */
    private String realName;

    /**
     * 昵称（模糊查询）
     */
    private String nickname;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 手机号（模糊查询）
     */
    private String phone;

    /**
     * 性别（0：女，1：男，2：未知）
     */
    private Integer gender;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 最后登录时间开始
     */
    private LocalDateTime lastLoginTimeStart;

    /**
     * 最后登录时间结束
     */
    private LocalDateTime lastLoginTimeEnd;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向（asc：升序，desc：降序）
     */
    private String sortOrder = "desc";
}
