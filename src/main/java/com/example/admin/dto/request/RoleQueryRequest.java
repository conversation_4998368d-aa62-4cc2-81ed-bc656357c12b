package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 角色查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class RoleQueryRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 角色编码（模糊查询）
     */
    private String roleCode;

    /**
     * 角色名称（模糊查询）
     */
    private String roleName;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 数据权限范围（1：全部数据，2：本部门及以下数据，3：本部门数据，4：仅本人数据，5：自定义数据）
     */
    private Integer dataScope;

    /**
     * 是否系统内置角色（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向（asc：升序，desc：降序）
     */
    private String sortOrder = "desc";
}
