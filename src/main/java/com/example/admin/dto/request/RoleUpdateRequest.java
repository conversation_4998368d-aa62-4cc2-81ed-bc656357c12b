package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 角色更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class RoleUpdateRequest {

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long id;

    /**
     * 角色编码（更新时不允许修改）
     */
    private String roleCode;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间")
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "角色状态不能为空")
    @Min(value = 0, message = "角色状态值无效")
    @Max(value = 1, message = "角色状态值无效")
    private Integer status;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder;

    /**
     * 数据权限范围（1：全部数据，2：本部门及以下数据，3：本部门数据，4：仅本人数据，5：自定义数据）
     */
    @NotNull(message = "数据权限范围不能为空")
    @Min(value = 1, message = "数据权限范围值无效")
    @Max(value = 5, message = "数据权限范围值无效")
    private Integer dataScope;

    /**
     * 权限ID列表
     */
    private Long[] permissionIds;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
