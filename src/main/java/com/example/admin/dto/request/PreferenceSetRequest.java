package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 偏好设置请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class PreferenceSetRequest {

    /**
     * 偏好键
     */
    @NotBlank(message = "偏好键不能为空")
    @Size(max = 100, message = "偏好键长度不能超过100个字符")
    private String preferenceKey;

    /**
     * 偏好值
     */
    @NotBlank(message = "偏好值不能为空")
    private String preferenceValue;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空")
    @Pattern(regexp = "^(STRING|NUMBER|BOOLEAN|JSON)$", message = "数据类型必须是STRING、NUMBER、BOOLEAN或JSON")
    private String dataType;

    /**
     * 偏好描述
     */
    @Size(max = 255, message = "偏好描述长度不能超过255个字符")
    private String description;
}