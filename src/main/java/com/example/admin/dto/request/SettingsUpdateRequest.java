package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * 个人设置更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class SettingsUpdateRequest {

    /**
     * 主题设置
     */
    @Size(max = 20, message = "主题设置长度不能超过20个字符")
    private String theme;

    /**
     * 语言设置
     */
    @Size(max = 10, message = "语言设置长度不能超过10个字符")
    private String language;

    /**
     * 时区设置
     */
    @Size(max = 50, message = "时区设置长度不能超过50个字符")
    private String timezone;

    /**
     * 日期格式
     */
    @Size(max = 20, message = "日期格式长度不能超过20个字符")
    private String dateFormat;

    /**
     * 时间格式（12/24）
     */
    @Size(max = 10, message = "时间格式长度不能超过10个字符")
    private String timeFormat;

    /**
     * 菜单是否折叠（0：展开，1：折叠）
     */
    private Integer menuCollapsed;

    /**
     * 分页大小
     */
    @Min(value = 5, message = "分页大小不能小于5")
    @Max(value = 100, message = "分页大小不能大于100")
    private Integer pageSize;

    /**
     * 是否启用通知（0：禁用，1：启用）
     */
    private Integer notificationEnabled;

    /**
     * 邮件通知（0：禁用，1：启用）
     */
    private Integer emailNotification;

    /**
     * 短信通知（0：禁用，1：启用）
     */
    private Integer smsNotification;
}
