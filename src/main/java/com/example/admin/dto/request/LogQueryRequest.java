package com.example.admin.dto.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志查询请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
public class LogQueryRequest {
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 日志类型
     */
    private String logType;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 请求URI
     */
    private String requestUri;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 操作状态 (true:成功, false:失败)
     */
    private Boolean success;
    
    /**
     * 日志级别
     */
    private String level;
    
    /**
     * 异常类型
     */
    private String exceptionType;
    
    /**
     * 浏览器类型
     */
    private String browser;
    
    /**
     * 操作系统
     */
    private String os;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行时间最小值 (毫秒)
     */
    private Long minExecutionTime;
    
    /**
     * 执行时间最大值 (毫秒)
     */
    private Long maxExecutionTime;
    
        
    /**
     * 用户ID列表
     */
    private List<Long> userIds;
    
    /**
     * 模块名称列表
     */
    private List<String> moduleNames;
    
    /**
     * 操作类型列表
     */
    private List<String> operationTypes;
    
    /**
     * IP地址列表
     */
    private List<String> ips;
    
    /**
     * 日志级别列表
     */
    private List<String> levels;
    
    /**
     * 排序字段
     */
    private String sortField = "create_time";
    
    /**
     * 排序方向 (asc/desc)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页面大小
     */
    private Integer pageSize = 10;
    
}