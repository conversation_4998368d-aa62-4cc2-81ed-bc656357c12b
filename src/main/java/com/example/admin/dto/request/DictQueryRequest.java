package com.example.admin.dto.request;

import lombok.Data;

/**
 * 数据字典查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class DictQueryRequest {

    /**
     * 字典编码（模糊查询）
     */
    private String dictCode;

    /**
     * 字典名称（模糊查询）
     */
    private String dictName;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;
}
