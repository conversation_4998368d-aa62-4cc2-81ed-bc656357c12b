package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 菜单查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class MenuQueryRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Long size = 10L;

    /**
     * 菜单名称（模糊查询）
     */
    private String menuName;

    /**
     * 菜单标题（模糊查询）
     */
    private String menuTitle;

    /**
     * 菜单类型（1：目录，2：菜单，3：按钮）
     */
    private Integer menuType;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 路由路径（模糊查询）
     */
    private String path;

    /**
     * 组件路径（模糊查询）
     */
    private String component;

    /**
     * 是否外链（0：否，1：是）
     */
    private Integer isFrame;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    private Integer isCache;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    private Integer visible;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 权限标识（模糊查询）
     */
    private String perms;

    /**
     * 是否系统内置菜单（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 排序字段
     */
    private String sortField = "sortOrder";

    /**
     * 排序方向（asc：升序，desc：降序）
     */
    private String sortOrder = "asc";

    /**
     * 是否查询树形结构（true：返回树形结构，false：返回列表结构）
     */
    private Boolean treeStructure = false;

    /**
     * 是否只查询菜单（排除按钮）
     */
    private Boolean menuOnly = false;

    /**
     * 用户ID（查询用户可见的菜单）
     */
    private Long userId;

    /**
     * 角色ID（查询角色可见的菜单）
     */
    private Long roleId;
}
