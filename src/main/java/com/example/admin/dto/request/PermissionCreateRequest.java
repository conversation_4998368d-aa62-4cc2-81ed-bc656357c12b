package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 权限创建请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class PermissionCreateRequest {

    /**
     * 权限编码
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(min = 2, max = 100, message = "权限编码长度必须在2-100个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9:_]+$", message = "权限编码只能包含字母、数字、冒号和下划线")
    private String permissionCode;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(min = 2, max = 50, message = "权限名称长度必须在2-50个字符之间")
    private String permissionName;

    /**
     * 权限类型（1：菜单，2：按钮，3：接口）
     */
    @NotNull(message = "权限类型不能为空")
    @Min(value = 1, message = "权限类型值无效")
    @Max(value = 3, message = "权限类型值无效")
    private Integer permissionType;

    /**
     * 父权限ID
     */
    private Long parentId = 0L;

    /**
     * 权限路径
     */
    @Size(max = 200, message = "权限路径长度不能超过200个字符")
    private String path;

    /**
     * 权限描述
     */
    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String description;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "权限状态不能为空")
    @Min(value = 0, message = "权限状态值无效")
    @Max(value = 1, message = "权限状态值无效")
    private Integer status = 1;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
