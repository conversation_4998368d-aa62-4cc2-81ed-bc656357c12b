package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 系统配置更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class ConfigUpdateRequest {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    private Long id;

    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    @Pattern(regexp = "^[a-zA-Z0-9._-]+$", message = "配置键只能包含字母、数字、点、下划线和横线")
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置类型（STRING、NUMBER、BOOLEAN、JSON）
     */
    @NotBlank(message = "配置类型不能为空")
    @Pattern(regexp = "^(STRING|NUMBER|BOOLEAN|JSON)$", message = "配置类型只能是STRING、NUMBER、BOOLEAN、JSON")
    private String configType;

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;

    /**
     * 是否加密（0：否，1：是）
     */
    @Min(value = 0, message = "是否加密值无效")
    @Max(value = 1, message = "是否加密值无效")
    private Integer isEncrypted;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值无效")
    @Max(value = 1, message = "状态值无效")
    private Integer status;
}
