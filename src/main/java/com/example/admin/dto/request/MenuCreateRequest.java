package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 菜单创建请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class MenuCreateRequest {

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 2, max = 50, message = "菜单名称长度必须在2-50个字符之间")
    private String menuName;

    /**
     * 菜单标题（用于国际化）
     */
    @Size(max = 50, message = "菜单标题长度不能超过50个字符")
    private String menuTitle;

    /**
     * 父菜单ID
     */
    private Long parentId = 0L;

    /**
     * 菜单类型（1：目录，2：菜单，3：按钮）
     */
    @NotNull(message = "菜单类型不能为空")
    @Min(value = 1, message = "菜单类型值无效")
    @Max(value = 3, message = "菜单类型值无效")
    private Integer menuType;

    /**
     * 路由路径
     */
    @Size(max = 200, message = "路由路径长度不能超过200个字符")
    private String path;

    /**
     * 组件路径
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String component;

    /**
     * 路由参数
     */
    @Size(max = 200, message = "路由参数长度不能超过200个字符")
    private String query;

    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String icon;

    /**
     * 是否外链（0：否，1：是）
     */
    @Min(value = 0, message = "是否外链值无效")
    @Max(value = 1, message = "是否外链值无效")
    private Integer isFrame = 0;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    @Min(value = 0, message = "是否缓存值无效")
    @Max(value = 1, message = "是否缓存值无效")
    private Integer isCache = 0;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    @NotNull(message = "是否显示不能为空")
    @Min(value = 0, message = "是否显示值无效")
    @Max(value = 1, message = "是否显示值无效")
    private Integer visible = 1;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "菜单状态不能为空")
    @Min(value = 0, message = "菜单状态值无效")
    @Max(value = 1, message = "菜单状态值无效")
    private Integer status = 1;

    /**
     * 权限标识
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String perms;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
