package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户日志查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class UserLogQueryRequest {

    /**
     * 操作类型
     */
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String operationType;

    /**
     * 日志类型
     */
    @Size(max = 50, message = "日志类型长度不能超过50个字符")
    private String logType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小不能小于1")
    private Integer pageSize = 10;
}
