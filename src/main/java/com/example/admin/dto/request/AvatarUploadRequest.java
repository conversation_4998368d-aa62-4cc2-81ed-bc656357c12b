package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 头像上传请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class AvatarUploadRequest {

    /**
     * 头像文件Base64编码
     */
    @NotBlank(message = "头像文件不能为空")
    @Size(max = 5242880, message = "头像文件大小不能超过5MB")
    private String avatarBase64;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    @Pattern(regexp = "^(image/jpeg|image/jpg|image/png|image/gif)$", message = "仅支持jpg、jpeg、png、gif格式的图片")
    private String fileType;

    /**
     * 文件名
     */
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    private String fileName;
}