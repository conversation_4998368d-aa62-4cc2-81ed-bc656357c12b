package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 数据字典创建请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class DictCreateRequest {

    /**
     * 字典编码
     */
    @NotBlank(message = "字典编码不能为空")
    @Size(max = 50, message = "字典编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "字典编码只能包含字母、数字和下划线")
    private String dictCode;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Size(max = 100, message = "字典名称长度不能超过100个字符")
    private String dictName;

    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值无效")
    @Max(value = 1, message = "状态值无效")
    private Integer status = 1;
}
