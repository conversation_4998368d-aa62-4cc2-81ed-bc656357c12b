package com.example.admin.dto.request;

import lombok.Data;

/**
 * 系统配置查询请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class ConfigQueryRequest {

    /**
     * 配置键（模糊查询）
     */
    private String configKey;

    /**
     * 配置类型
     */
    private String configType;

    /**
     * 是否系统配置（0：否，1：是）
     */
    private Integer isSystem;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Integer status;

    /**
     * 页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;
}
