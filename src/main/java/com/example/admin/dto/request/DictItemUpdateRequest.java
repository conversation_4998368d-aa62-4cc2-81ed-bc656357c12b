package com.example.admin.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 字典项更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class DictItemUpdateRequest {

    /**
     * 字典项ID
     */
    @NotNull(message = "字典项ID不能为空")
    private Long id;

    /**
     * 字典ID
     */
    @NotNull(message = "字典ID不能为空")
    private Long dictId;

    /**
     * 字典项编码
     */
    @NotBlank(message = "字典项编码不能为空")
    @Size(max = 50, message = "字典项编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "字典项编码只能包含字母、数字和下划线")
    private String itemCode;

    /**
     * 字典项名称
     */
    @NotBlank(message = "字典项名称不能为空")
    @Size(max = 100, message = "字典项名称长度不能超过100个字符")
    private String itemName;

    /**
     * 字典项值
     */
    @Size(max = 200, message = "字典项值长度不能超过200个字符")
    private String itemValue;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder;

    /**
     * 状态（0：禁用，1：启用）
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值无效")
    @Max(value = 1, message = "状态值无效")
    private Integer status;
}
