package com.example.admin.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传工具类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Component
public class FileUploadUtils {

    @Value("${demo-admin.file.upload-path:/uploads/}")
    private String uploadPath;

    @Value("${demo-admin.file.max-size:10MB}")
    private String maxSize;

    @Value("#{'${demo-admin.file.allowed-types:jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx}'.split(',')}")
    private List<String> allowedTypes;

    /**
     * 上传文件
     *
     * @param file 文件
     * @param subDir 子目录
     * @return 文件相对路径
     */
    public String uploadFile(MultipartFile file, String subDir) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String fileExtension = FileUtil.extName(originalFilename).toLowerCase();
        if (!allowedTypes.contains(fileExtension)) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        }

        // 验证文件大小
        long maxSizeBytes = parseSize(maxSize);
        if (file.getSize() > maxSizeBytes) {
            throw new IllegalArgumentException("文件大小超过限制: " + maxSize);
        }

        // 生成文件名
        String fileName = generateFileName(originalFilename);

        // 构建文件路径
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String relativePath = subDir + "/" + datePath + "/" + fileName;
        String absolutePath = uploadPath + relativePath;

        // 创建目录
        File targetFile = new File(absolutePath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new IOException("创建目录失败: " + parentDir.getAbsolutePath());
            }
        }

        // 保存文件
        file.transferTo(targetFile);

        log.info("文件上传成功: {} -> {}", originalFilename, relativePath);
        return relativePath;
    }

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 头像相对路径
     */
    public String uploadAvatar(MultipartFile file) throws IOException {
        // 验证是否为图片
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IllegalArgumentException("只能上传图片文件");
        }

        // 限制头像大小为5MB
        if (file.getSize() > 5 * 1024 * 1024) {
            throw new IllegalArgumentException("头像文件大小不能超过5MB");
        }

        return uploadFile(file, "avatars");
    }

    /**
     * 上传头像（Base64格式）
     *
     * @param imageData 图片数据
     * @param fileName 文件名
     * @return 头像相对路径
     */
    public String uploadAvatarBase64(byte[] imageData, String fileName) throws IOException {
        if (imageData == null || imageData.length == 0) {
            throw new IllegalArgumentException("图片数据不能为空");
        }

        // 验证文件大小
        long maxSizeBytes = 5 * 1024 * 1024; // 5MB
        if (imageData.length > maxSizeBytes) {
            throw new IllegalArgumentException("头像文件大小不能超过5MB");
        }

        // 验证文件名
        if (StrUtil.isBlank(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String fileExtension = FileUtil.extName(fileName).toLowerCase();
        if (!Arrays.asList("jpg", "jpeg", "png", "gif").contains(fileExtension)) {
            throw new IllegalArgumentException("只支持jpg、jpeg、png、gif格式的图片");
        }

        // 生成文件名
        String newFileName = generateFileName(fileName);

        // 构建文件路径
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String relativePath = "avatars/" + datePath + "/" + newFileName;
        String absolutePath = uploadPath + relativePath;

        // 创建目录
        File targetFile = new File(absolutePath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new IOException("创建目录失败: " + parentDir.getAbsolutePath());
            }
        }

        // 保存文件
        FileUtil.writeBytes(imageData, targetFile);

        log.info("头像上传成功(Base64): {} -> {}", fileName, relativePath);
        return relativePath;
    }

    /**
     * 删除文件
     *
     * @param relativePath 文件相对路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String relativePath) {
        if (StrUtil.isBlank(relativePath)) {
            return false;
        }

        String absolutePath = uploadPath + relativePath;
        File file = new File(absolutePath);
        
        if (file.exists() && file.isFile()) {
            boolean deleted = file.delete();
            if (deleted) {
                log.info("文件删除成功: {}", relativePath);
            } else {
                log.warn("文件删除失败: {}", relativePath);
            }
            return deleted;
        }

        return false;
    }

    /**
     * 获取文件的完整URL
     *
     * @param relativePath 文件相对路径
     * @return 文件URL
     */
    public String getFileUrl(String relativePath) {
        if (StrUtil.isBlank(relativePath)) {
            return null;
        }
        
        // 这里可以根据实际情况返回完整的URL
        // 例如：http://localhost:8080/api/files/ + relativePath
        return "/api/files/" + relativePath;
    }

    /**
     * 生成唯一文件名
     *
     * @param originalFilename 原始文件名
     * @return 新文件名
     */
    private String generateFileName(String originalFilename) {
        String fileExtension = FileUtil.extName(originalFilename);
        String uuid = IdUtil.simpleUUID();
        return uuid + "." + fileExtension;
    }

    /**
     * 解析文件大小字符串
     *
     * @param sizeStr 大小字符串，如 "10MB", "1GB"
     * @return 字节数
     */
    private long parseSize(String sizeStr) {
        if (StrUtil.isBlank(sizeStr)) {
            return 10 * 1024 * 1024; // 默认10MB
        }

        sizeStr = sizeStr.toUpperCase().trim();
        
        if (sizeStr.endsWith("KB")) {
            return Long.parseLong(sizeStr.substring(0, sizeStr.length() - 2)) * 1024;
        } else if (sizeStr.endsWith("MB")) {
            return Long.parseLong(sizeStr.substring(0, sizeStr.length() - 2)) * 1024 * 1024;
        } else if (sizeStr.endsWith("GB")) {
            return Long.parseLong(sizeStr.substring(0, sizeStr.length() - 2)) * 1024 * 1024 * 1024;
        } else {
            // 默认按字节处理
            return Long.parseLong(sizeStr);
        }
    }
}
