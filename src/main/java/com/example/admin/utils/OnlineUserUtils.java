package com.example.admin.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 在线用户管理工具类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class OnlineUserUtils {

    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String ONLINE_USERS_KEY = "admin:online:users";
    private static final String USER_LAST_ACTIVITY_KEY = "admin:user:last:activity:";
    private static final long ONLINE_TIMEOUT_MINUTES = 30; // 30分钟无活动则下线

    public OnlineUserUtils(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 用户登录时记录在线状态
     */
    public void userLogin(Long userId, String username) {
        try {
            String userKey = userId + ":" + username;
            
            // 添加到在线用户集合
            redisTemplate.opsForSet().add(ONLINE_USERS_KEY, userKey);
            
            // 更新最后活动时间
            updateUserActivity(userId);
            
            // 设置过期时间
            redisTemplate.expire(ONLINE_USERS_KEY, ONLINE_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            
            log.info("用户上线: {}", username);
        } catch (Exception e) {
            log.error("记录用户在线状态失败: {}", username, e);
        }
    }

    /**
     * 用户登出时移除在线状态
     */
    public void userLogout(Long userId, String username) {
        try {
            String userKey = userId + ":" + username;
            
            // 从在线用户集合中移除
            redisTemplate.opsForSet().remove(ONLINE_USERS_KEY, userKey);
            
            // 删除最后活动时间记录
            redisTemplate.delete(USER_LAST_ACTIVITY_KEY + userId);
            
            log.info("用户下线: {}", username);
        } catch (Exception e) {
            log.error("移除用户在线状态失败: {}", username, e);
        }
    }

    /**
     * 更新用户活动时间
     */
    public void updateUserActivity(Long userId) {
        try {
            String activityKey = USER_LAST_ACTIVITY_KEY + userId;
            redisTemplate.opsForValue().set(activityKey, System.currentTimeMillis(), 
                ONLINE_TIMEOUT_MINUTES, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("更新用户活动时间失败: {}", userId, e);
        }
    }

    /**
     * 获取在线用户数量
     */
    public long getOnlineUserCount() {
        try {
            // 清理过期的在线用户
            cleanupExpiredUsers();
            
            Set<Object> onlineUsers = redisTemplate.opsForSet().members(ONLINE_USERS_KEY);
            return onlineUsers != null ? onlineUsers.size() : 0L;
        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return 0L;
        }
    }

    /**
     * 获取在线用户列表
     */
    public Set<Object> getOnlineUsers() {
        try {
            cleanupExpiredUsers();
            return redisTemplate.opsForSet().members(ONLINE_USERS_KEY);
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
            return Set.of();
        }
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        try {
            String userKey = USER_LAST_ACTIVITY_KEY + userId;
            Long lastActivity = (Long) redisTemplate.opsForValue().get(userKey);
            if (lastActivity == null) {
                return false;
            }
            
            // 检查是否超时
            long currentTime = System.currentTimeMillis();
            long timeoutMillis = ONLINE_TIMEOUT_MINUTES * 60 * 1000L;
            
            return (currentTime - lastActivity) < timeoutMillis;
        } catch (Exception e) {
            log.error("检查用户在线状态失败: {}", userId, e);
            return false;
        }
    }

    /**
     * 清理过期的在线用户
     */
    private void cleanupExpiredUsers() {
        try {
            Set<Object> onlineUsers = redisTemplate.opsForSet().members(ONLINE_USERS_KEY);
            if (onlineUsers == null || onlineUsers.isEmpty()) {
                return;
            }
            
            long currentTime = System.currentTimeMillis();
            long timeoutMillis = ONLINE_TIMEOUT_MINUTES * 60 * 1000L;
            
            for (Object userObj : onlineUsers) {
                String userKey = userObj.toString();
                String[] parts = userKey.split(":");
                if (parts.length >= 1) {
                    Long userId = Long.valueOf(parts[0]);
                    String activityKey = USER_LAST_ACTIVITY_KEY + userId;
                    Long lastActivity = (Long) redisTemplate.opsForValue().get(activityKey);
                    
                    if (lastActivity == null || (currentTime - lastActivity) >= timeoutMillis) {
                        // 用户已超时，从在线集合中移除
                        redisTemplate.opsForSet().remove(ONLINE_USERS_KEY, userKey);
                        redisTemplate.delete(activityKey);
                        log.debug("清理过期在线用户: {}", userKey);
                    }
                }
            }
        } catch (Exception e) {
            log.error("清理过期在线用户失败", e);
        }
    }

    /**
     * 强制用户下线
     */
    public boolean forceUserOffline(Long userId, String username) {
        try {
            String userKey = userId + ":" + username;
            
            // 从在线用户集合中移除
            redisTemplate.opsForSet().remove(ONLINE_USERS_KEY, userKey);
            
            // 删除最后活动时间记录
            redisTemplate.delete(USER_LAST_ACTIVITY_KEY + userId);
            
            log.info("强制用户下线: {}", username);
            return true;
        } catch (Exception e) {
            log.error("强制用户下线失败: {}", username, e);
            return false;
        }
    }

    /**
     * 获取用户最后活动时间
     */
    public Long getUserLastActivity(Long userId) {
        try {
            String activityKey = USER_LAST_ACTIVITY_KEY + userId;
            return (Long) redisTemplate.opsForValue().get(activityKey);
        } catch (Exception e) {
            log.error("获取用户最后活动时间失败: {}", userId, e);
            return null;
        }
    }
}