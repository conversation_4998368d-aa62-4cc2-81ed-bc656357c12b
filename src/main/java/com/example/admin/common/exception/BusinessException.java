package com.example.admin.common.exception;

import com.example.admin.common.constant.CommonConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String message;

    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super(message);
        this.code = CommonConstant.ERROR_CODE;
        this.message = message;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = CommonConstant.ERROR_CODE;
        this.message = message;
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    /**
     * 创建业务异常
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建业务异常（带错误码）
     */
    public static BusinessException of(Integer code, String message) {
        return new BusinessException(code, message);
    }

    /**
     * 创建未授权异常
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(CommonConstant.UNAUTHORIZED_CODE, message);
    }

    /**
     * 创建禁止访问异常
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(CommonConstant.FORBIDDEN_CODE, message);
    }

    /**
     * 创建资源未找到异常
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(CommonConstant.NOT_FOUND_CODE, message);
    }

    /**
     * 创建参数错误异常
     */
    public static BusinessException badRequest(String message) {
        return new BusinessException(CommonConstant.BAD_REQUEST_CODE, message);
    }
}
