package com.example.admin.common.constant;

/**
 * 通用常量类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class CommonConstant {

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR_CODE = 500;

    /**
     * 未授权状态码
     */
    public static final int UNAUTHORIZED_CODE = 401;

    /**
     * 禁止访问状态码
     */
    public static final int FORBIDDEN_CODE = 403;

    /**
     * 资源未找到状态码
     */
    public static final int NOT_FOUND_CODE = 404;

    /**
     * 参数错误状态码
     */
    public static final int BAD_REQUEST_CODE = 400;

    /**
     * 成功消息
     */
    public static final String SUCCESS_MSG = "操作成功";

    /**
     * 失败消息
     */
    public static final String ERROR_MSG = "操作失败";

    /**
     * 未授权消息
     */
    public static final String UNAUTHORIZED_MSG = "未授权访问";

    /**
     * 禁止访问消息
     */
    public static final String FORBIDDEN_MSG = "禁止访问";

    /**
     * 资源未找到消息
     */
    public static final String NOT_FOUND_MSG = "资源未找到";

    /**
     * 参数错误消息
     */
    public static final String BAD_REQUEST_MSG = "参数错误";

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 默认页面大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大页面大小
     */
    public static final int MAX_PAGE_SIZE = 100;

    /**
     * 逻辑删除 - 已删除
     */
    public static final int DELETED = 1;

    /**
     * 逻辑删除 - 未删除
     */
    public static final int NOT_DELETED = 0;

    /**
     * 状态 - 启用
     */
    public static final int STATUS_ENABLED = 1;

    /**
     * 状态 - 禁用
     */
    public static final int STATUS_DISABLED = 0;

    /**
     * 是否 - 是
     */
    public static final int YES = 1;

    /**
     * 是否 - 否
     */
    public static final int NO = 0;

    /**
     * 默认密码
     */
    public static final String DEFAULT_PASSWORD = "123456";

    /**
     * 超级管理员角色编码
     */
    public static final String SUPER_ADMIN_ROLE = "SUPER_ADMIN";

    /**
     * 管理员角色编码
     */
    public static final String ADMIN_ROLE = "ADMIN";

    /**
     * 普通用户角色编码
     */
    public static final String USER_ROLE = "USER";

    /**
     * JWT Token 前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * JWT Token Header
     */
    public static final String TOKEN_HEADER = "Authorization";

    /**
     * 验证码 Redis Key 前缀
     */
    public static final String CAPTCHA_KEY_PREFIX = "captcha:";

    /**
     * 用户登录失败次数 Redis Key 前缀
     */
    public static final String LOGIN_FAIL_KEY_PREFIX = "login_fail:";

    /**
     * 在线用户 Redis Key 前缀
     */
    public static final String ONLINE_USER_KEY_PREFIX = "online_user:";

    /**
     * 权限缓存 Redis Key 前缀
     */
    public static final String PERMISSION_KEY_PREFIX = "permission:";

    /**
     * 菜单缓存 Redis Key 前缀
     */
    public static final String MENU_KEY_PREFIX = "menu:";

    /**
     * 系统配置缓存 Redis Key 前缀
     */
    public static final String CONFIG_KEY_PREFIX = "config:";

    /**
     * 文件上传路径
     */
    public static final String UPLOAD_PATH = "/uploads/";

    /**
     * 头像上传路径
     */
    public static final String AVATAR_PATH = "/uploads/avatar/";

    /**
     * 默认头像
     */
    public static final String DEFAULT_AVATAR = "/static/images/default-avatar.png";

    /**
     * 日期时间格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 时间格式
     */
    public static final String TIME_FORMAT = "HH:mm:ss";
}
