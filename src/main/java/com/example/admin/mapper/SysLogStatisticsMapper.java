package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysLogStatistics;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 系统日志统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogStatisticsMapper extends BaseMapper<SysLogStatistics> {

    /**
     * 根据日期和类型查询统计数据
     */
    SysLogStatistics selectByDateAndType(@Param("statDate") LocalDate statDate,
                                         @Param("statType") String statType,
                                         @Param("moduleName") String moduleName);

    /**
     * 查询日期范围内的统计数据
     */
    List<SysLogStatistics> selectByDateRange(@Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("statType") String statType);

    /**
     * 查询最新的统计数据
     */
    List<SysLogStatistics> selectLatestStatistics(@Param("statType") String statType,
                                                 @Param("limit") Integer limit);

    /**
     * 查询模块统计排行
     */
    List<Map<String, Object>> selectModuleRanking(@Param("startDate") LocalDate startDate,
                                                @Param("endDate") LocalDate endDate,
                                                @Param("statType") String statType,
                                                @Param("limit") Integer limit);

    /**
     * 查询用户活跃度排行
     */
    List<Map<String, Object>> selectUserActivityRanking(@Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate,
                                                        @Param("limit") Integer limit);

    /**
     * 查询操作频率排行
     */
    List<Map<String, Object>> selectOperationFrequencyRanking(@Param("startDate") LocalDate startDate,
                                                              @Param("endDate") LocalDate endDate,
                                                              @Param("limit") Integer limit);

    /**
     * 查询错误率排行
     */
    List<Map<String, Object>> selectErrorRateRanking(@Param("startDate") LocalDate startDate,
                                                   @Param("endDate") LocalDate endDate,
                                                   @Param("limit") Integer limit);

    /**
     * 查询响应时间排行
     */
    List<Map<String, Object>> selectResponseTimeRanking(@Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate,
                                                       @Param("limit") Integer limit);

    /**
     * 查询趋势数据
     */
    List<Map<String, Object>> selectTrendData(@Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("moduleName") String moduleName,
                                           @Param("statType") String statType);

    /**
     * 查询汇总数据
     */
    Map<String, Object> selectSummaryData(@Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate,
                                         @Param("statType") String statType);

    /**
     * 删除过期统计数据
     */
    int deleteExpiredStatistics(@Param("expirationDate") LocalDate expirationDate);

    /**
     * 检查统计数据是否存在
     */
    boolean checkStatisticsExists(@Param("statDate") LocalDate statDate,
                                 @Param("statType") String statType,
                                 @Param("moduleName") String moduleName);

}