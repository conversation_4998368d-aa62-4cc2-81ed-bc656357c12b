package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.entity.SysUserLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户个人操作日志Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysUserLogMapper extends BaseMapper<SysUserLog> {

    /**
     * 根据用户ID分页获取操作日志
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志分页列表
     */
    Page<SysUserLog> selectPageByUserId(Page<SysUserLog> page, 
                                       @Param("userId") Long userId,
                                       @Param("operationType") String operationType,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID获取操作日志列表
     *
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 日志列表
     */
    List<SysUserLog> selectByUserId(@Param("userId") Long userId,
                                   @Param("operationType") String operationType,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("limit") Integer limit);

    /**
     * 根据用户ID删除操作日志
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 删除指定时间之前的日志
     *
     * @param beforeTime 时间点
     * @return 影响行数
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计用户操作次数
     *
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    Long countByUserId(@Param("userId") Long userId,
                      @Param("operationType") String operationType,
                      @Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime);
}
