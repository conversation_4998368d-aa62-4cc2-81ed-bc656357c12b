package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysDict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {

    /**
     * 根据字典编码查询字典
     *
     * @param dictCode 字典编码
     * @return 数据字典
     */
    SysDict selectByDictCode(@Param("dictCode") String dictCode);

    /**
     * 查询所有启用的字典
     *
     * @return 字典列表
     */
    List<SysDict> selectEnabledDicts();

    /**
     * 根据字典名称模糊查询
     *
     * @param dictName 字典名称
     * @return 字典列表
     */
    List<SysDict> selectByDictNameLike(@Param("dictName") String dictName);
}
