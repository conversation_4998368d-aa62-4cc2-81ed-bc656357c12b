package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysUserProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户个人资料Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysUserProfileMapper extends BaseMapper<SysUserProfile> {

    /**
     * 根据用户ID获取用户个人资料
     *
     * @param userId 用户ID
     * @return 用户个人资料
     */
    SysUserProfile selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID删除用户个人资料
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 更新登录信息
     *
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @param loginDevice 登录设备
     * @return 影响行数
     */
    int updateLoginInfo(@Param("userId") Long userId, 
                       @Param("loginTime") String loginTime, 
                       @Param("loginIp") String loginIp, 
                       @Param("loginDevice") String loginDevice);

    /**
     * 增加登录次数
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int incrementLoginCount(@Param("userId") Long userId);
}
