package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.entity.SysUserDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户登录设备Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysUserDeviceMapper extends BaseMapper<SysUserDevice> {

    /**
     * 根据用户ID获取设备列表
     *
     * @param userId 用户ID
     * @return 设备列表
     */
    List<SysUserDevice> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID分页获取设备列表
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @return 设备分页列表
     */
    Page<SysUserDevice> selectPageByUserId(Page<SysUserDevice> page, @Param("userId") Long userId);

    /**
     * 根据用户ID和设备ID获取设备信息
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备信息
     */
    SysUserDevice selectByUserIdAndDeviceId(@Param("userId") Long userId, @Param("deviceId") String deviceId);

    /**
     * 更新设备状态为离线
     *
     * @param userId 用户ID
     * @param excludeDeviceId 排除的设备ID
     * @return 影响行数
     */
    int updateStatusToOffline(@Param("userId") Long userId, @Param("excludeDeviceId") String excludeDeviceId);

    /**
     * 清除当前设备标记
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int clearCurrentDevice(@Param("userId") Long userId);

    /**
     * 根据用户ID删除所有设备
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
