package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysLogDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统日志详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogDetailMapper extends BaseMapper<SysLogDetail> {

    /**
     * 根据日志ID查询详情列表
     */
    List<SysLogDetail> selectDetailsByLogId(@Param("logId") Long logId);

    /**
     * 批量插入日志详情
     */
    int batchInsert(@Param("details") List<SysLogDetail> details);

    /**
     * 根据日志ID批量删除详情
     */
    int deleteByLogId(@Param("logId") Long logId);

    /**
     * 根据日志ID批量删除详情
     */
    int deleteByLogIds(@Param("logIds") List<Long> logIds);

    /**
     * 查询敏感字段详情
     */
    List<SysLogDetail> selectSensitiveDetails(@Param("startTime") String startTime,
                                             @Param("endTime") String endTime);

    /**
     * 更新脱敏状态
     */
    int updateMaskedStatus(@Param("id") Long id, @Param("maskedValue") String maskedValue);

}