package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysUserPasswordHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户密码修改记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysUserPasswordHistoryMapper extends BaseMapper<SysUserPasswordHistory> {

    /**
     * 根据用户ID获取密码修改历史
     *
     * @param userId 用户ID
     * @return 密码修改历史列表
     */
    List<SysUserPasswordHistory> selectByUserId(@Param("userId") Long userId);

    /**
     * 获取用户最近的密码修改记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 密码修改历史列表
     */
    List<SysUserPasswordHistory> selectRecentByUserId(@Param("userId") Long userId, 
                                                     @Param("limit") Integer limit);

    /**
     * 检查密码是否在历史记录中
     *
     * @param userId 用户ID
     * @param password 密码（加密）
     * @return 是否存在
     */
    boolean existsPasswordInHistory(@Param("userId") Long userId, 
                                   @Param("password") String password);

    /**
     * 根据用户ID删除密码修改历史
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}