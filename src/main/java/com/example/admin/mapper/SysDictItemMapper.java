package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysDictItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字典项Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {

    /**
     * 根据字典ID查询字典项
     *
     * @param dictId 字典ID
     * @return 字典项列表
     */
    List<SysDictItem> selectByDictId(@Param("dictId") Long dictId);

    /**
     * 根据字典编码查询字典项
     *
     * @param dictCode 字典编码
     * @return 字典项列表
     */
    List<SysDictItem> selectByDictCode(@Param("dictCode") String dictCode);

    /**
     * 根据字典ID查询启用的字典项
     *
     * @param dictId 字典ID
     * @return 字典项列表
     */
    List<SysDictItem> selectEnabledByDictId(@Param("dictId") Long dictId);

    /**
     * 根据字典编码查询启用的字典项
     *
     * @param dictCode 字典编码
     * @return 字典项列表
     */
    List<SysDictItem> selectEnabledByDictCode(@Param("dictCode") String dictCode);

    /**
     * 根据字典ID和字典项编码查询
     *
     * @param dictId 字典ID
     * @param itemCode 字典项编码
     * @return 字典项
     */
    SysDictItem selectByDictIdAndItemCode(@Param("dictId") Long dictId, @Param("itemCode") String itemCode);

    /**
     * 删除字典下的所有字典项
     *
     * @param dictId 字典ID
     * @return 删除数量
     */
    int deleteByDictId(@Param("dictId") Long dictId);
}
