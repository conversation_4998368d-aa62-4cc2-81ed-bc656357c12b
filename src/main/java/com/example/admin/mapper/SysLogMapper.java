package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.dto.request.LogQueryRequest;
import com.example.admin.entity.SysLog;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogMapper extends BaseMapper<SysLog> {

    /**
     * 分页查询日志列表
     */
    IPage<SysLog> selectLogPage(Page<SysLog> page, @Param("query") LogQueryRequest query);

    /**
     * 根据条件查询日志列表
     */
    List<SysLog> selectLogList(@Param("query") LogQueryRequest query);

    /**
     * 根据时间范围查询日志数量
     */
    Long selectLogCountByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("logType") String logType);

    /**
     * 根据用户ID查询日志列表
     */
    List<SysLog> selectLogsByUserId(@Param("userId") Long userId, 
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 根据IP查询日志列表
     */
    List<SysLog> selectLogsByIp(@Param("ip") String ip,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询异常日志列表
     */
    List<SysLog> selectExceptionLogs(@Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询性能日志列表
     */
    List<SysLog> selectPerformanceLogs(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime,
                                      @Param("threshold") Long threshold);

    /**
     * 统计日志数量（按类型）
     */
    List<Map<String, Object>> statisticsLogCountByType(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计日志数量（按用户）
     */
    List<Map<String, Object>> statisticsLogCountByUser(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime,
                                                       @Param("limit") Integer limit);

    /**
     * 统计日志数量（按模块）
     */
    List<Map<String, Object>> statisticsLogCountByModule(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 统计日志数量（按操作）
     */
    List<Map<String, Object>> statisticsLogCountByOperation(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计日志数量（按日期）
     */
    List<Map<String, Object>> statisticsLogCountByDate(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计性能指标
     */
    Map<String, Object> statisticsPerformanceMetrics(@Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询慢查询日志
     */
    List<SysLog> selectSlowQueryLogs(@Param("threshold") Long threshold,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询错误日志
     */
    List<SysLog> selectErrorLogs(@Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    
    /**
     * 删除过期日志
     */
    int deleteExpiredLogs(@Param("expirationDate") LocalDateTime expirationDate);

    /**
     * 查询最近的日志
     */
    List<SysLog> selectRecentLogs(@Param("limit") Integer limit);

    /**
     * 查询用户操作轨迹
     */
    List<SysLog> selectUserOperationTrail(@Param("userId") Long userId,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询热门操作
     */
    List<Map<String, Object>> selectPopularOperations(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime,
                                                      @Param("limit") Integer limit);

}