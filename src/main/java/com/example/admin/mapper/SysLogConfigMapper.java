package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysLogConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统日志配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogConfigMapper extends BaseMapper<SysLogConfig> {

    /**
     * 根据配置代码查询配置
     */
    SysLogConfig selectByConfigCode(@Param("configCode") String configCode);

    /**
     * 根据模块名称查询配置列表
     */
    List<SysLogConfig> selectByModuleName(@Param("moduleName") String moduleName);

    /**
     * 根据配置类型查询配置列表
     */
    List<SysLogConfig> selectByConfigType(@Param("configType") String configType);

    /**
     * 查询启用的配置列表
     */
    List<SysLogConfig> selectEnabledConfigs();

    /**
     * 查询模块的日志配置
     */
    SysLogConfig selectModuleConfig(@Param("moduleName") String moduleName);

    /**
     * 查询操作类型的日志配置
     */
    List<SysLogConfig> selectOperationConfigs(@Param("operationType") String operationType);

    /**
     * 查询日志级别的配置
     */
    List<SysLogConfig> selectLevelConfigs(@Param("logLevel") String logLevel);

    /**
     * 查询告警配置
     */
    List<SysLogConfig> selectAlertConfigs();

    /**
     * 查询性能告警配置
     */
    List<SysLogConfig> selectPerformanceAlertConfigs();

    /**
     * 查询异常告警配置
     */
    List<SysLogConfig> selectExceptionAlertConfigs();

    /**
     * 更新配置启用状态
     */
    int updateConfigEnabled(@Param("id") Long id, @Param("enabled") Boolean enabled);

    
    /**
     * 查询需要实时推送的配置
     */
    List<SysLogConfig> selectRealTimePushConfigs();

    /**
     * 查询需要统计的配置
     */
    List<SysLogConfig> selectStatisticsConfigs();

}