package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysUserPreference;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户偏好设置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysUserPreferenceMapper extends BaseMapper<SysUserPreference> {

    /**
     * 根据用户ID获取所有偏好设置
     *
     * @param userId 用户ID
     * @return 偏好设置列表
     */
    List<SysUserPreference> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和偏好键获取偏好设置
     *
     * @param userId 用户ID
     * @param preferenceKey 偏好键
     * @return 偏好设置
     */
    SysUserPreference selectByUserIdAndKey(@Param("userId") Long userId, 
                                         @Param("preferenceKey") String preferenceKey);

    /**
     * 根据用户ID删除所有偏好设置
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和偏好键删除偏好设置
     *
     * @param userId 用户ID
     * @param preferenceKey 偏好键
     * @return 影响行数
     */
    int deleteByUserIdAndKey(@Param("userId") Long userId, 
                            @Param("preferenceKey") String preferenceKey);

    /**
     * 获取用户偏好设置映射
     *
     * @param userId 用户ID
     * @return 偏好设置映射
     */
    Map<String, Object> selectPreferenceMap(@Param("userId") Long userId);
}