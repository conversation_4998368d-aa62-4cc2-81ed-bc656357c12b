package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统菜单Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {
    
    /**
     * 根据用户ID查询菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询菜单列表
     * 
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据父菜单ID查询子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<SysMenu> selectChildrenByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询所有菜单的树形结构
     * 
     * @return 菜单树形结构
     */
    List<SysMenu> selectMenuTree();
    
    /**
     * 根据用户ID查询菜单树形结构
     * 
     * @param userId 用户ID
     * @return 菜单树形结构
     */
    List<SysMenu> selectMenuTreeByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询菜单树形结构
     * 
     * @param roleId 角色ID
     * @return 菜单树形结构
     */
    List<SysMenu> selectMenuTreeByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 查询用户权限标识列表
     * 
     * @param userId 用户ID
     * @return 权限标识列表
     */
    List<String> selectPermsByUserId(@Param("userId") Long userId);
    
    /**
     * 查询角色权限标识列表
     * 
     * @param roleId 角色ID
     * @return 权限标识列表
     */
    List<String> selectPermsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据菜单类型查询菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByType(@Param("menuType") Integer menuType);
    
    /**
     * 查询所有启用的菜单（用于路由生成）
     * 
     * @return 菜单列表
     */
    List<SysMenu> selectEnabledMenus();
    
    // 继承BaseMapper，获得基本的CRUD操作
    // 如需要自定义SQL，可在此添加方法并在对应的XML文件中实现
}
