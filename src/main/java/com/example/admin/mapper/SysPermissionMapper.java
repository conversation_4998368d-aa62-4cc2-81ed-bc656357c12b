package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统权限Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {
    
    /**
     * 根据角色ID查询权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID查询权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<SysPermission> selectPermissionsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据父权限ID查询子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<SysPermission> selectChildrenByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询所有权限的树形结构
     * 
     * @return 权限树形结构
     */
    List<SysPermission> selectPermissionTree();
    
    // 继承BaseMapper，获得基本的CRUD操作
    // 如需要自定义SQL，可在此添加方法并在对应的XML文件中实现
}
