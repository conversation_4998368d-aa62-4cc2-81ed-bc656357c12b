package com.example.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.admin.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 系统配置
     */
    SysConfig selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<SysConfig> selectEnabledConfigs();

    /**
     * 根据配置类型查询配置
     *
     * @param configType 配置类型
     * @return 配置列表
     */
    List<SysConfig> selectByConfigType(@Param("configType") String configType);

    /**
     * 查询系统配置
     *
     * @param isSystem 是否系统配置
     * @return 配置列表
     */
    List<SysConfig> selectBySystemFlag(@Param("isSystem") Integer isSystem);
}
