package com.example.admin.security;

import com.example.admin.utils.JwtUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final UserDetailsService userDetailsService;

    /**
     * JWT认证过滤逻辑
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param filterChain 过滤器链
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                    HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 从请求头中获取JWT令牌
            String jwt = getJwtFromRequest(request);
            log.debug("请求路径: {}, JWT令牌: {}", request.getRequestURI(), jwt != null ? "已获取" : "未获取");
            
            if (StringUtils.hasText(jwt) && jwtUtils.validateToken(jwt)) {
                // 从JWT中获取用户名
                String username = jwtUtils.getUsernameFromToken(jwt);
                log.debug("从JWT中获取用户名: {}", username);
                
                // 加载用户详情
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                log.debug("加载用户详情成功，用户名: {}, 权限数量: {}", username, userDetails.getAuthorities().size());
                
                // 验证JWT令牌与用户信息是否匹配
                if (jwtUtils.validateToken(jwt, username)) {
                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authentication = 
                            new UsernamePasswordAuthenticationToken(
                                    userDetails, 
                                    null, 
                                    userDetails.getAuthorities()
                            );
                    
                    // 设置认证详情
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    
                    // 设置Spring Security上下文
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    
                    log.info("用户 {} 认证成功，权限: {}", username, userDetails.getAuthorities());
                } else {
                    log.warn("JWT令牌验证失败，用户名: {}", username);
                }
            } else {
                log.warn("JWT令牌为空或验证失败");
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生异常: {}", e.getMessage(), e);
            // 清除认证上下文
            SecurityContextHolder.clearContext();
        }
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 从HTTP请求中获取JWT令牌
     *
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        return jwtUtils.getTokenFromHeader(authHeader);
    }

    /**
     * 判断是否应该跳过过滤
     * 对于某些特定路径（如登录接口），可以跳过JWT验证
     *
     * @param request HTTP请求
     * @return 是否跳过
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        
        // 跳过认证的路径
        String[] skipPaths = {
                "/auth/login",
                "/auth/register", 
                "/public/",
                "/swagger-ui/",
                "/v3/api-docs/",
                "/actuator/",
                "/dashboard/"
        };
        
        for (String skipPath : skipPaths) {
            if (path.startsWith(skipPath)) {
                return true;
            }
        }
        
        return false;
    }
}
