package com.example.admin.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 异常日志切面
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Aspect
@Component
@Slf4j
public class ExceptionLogAspect {

    /**
     * 定义切入点：Controller层的所有方法
     */
    @AfterThrowing(pointcut = "execution(* com.example.admin.controller.*.*(..))", throwing = "ex")
    public void afterControllerThrowing(Throwable ex) {
        logException("CONTROLLER", ex);
    }

    /**
     * 定义切入点：Service层的所有方法
     */
    @AfterThrowing(pointcut = "execution(* com.example.admin.service.*.*(..))", throwing = "ex")
    public void afterServiceThrowing(Throwable ex) {
        logException("SERVICE", ex);
    }

    /**
     * 定义切入点：Mapper层的所有方法
     */
    @AfterThrowing(pointcut = "execution(* com.example.admin.mapper.*.*(..))", throwing = "ex")
    public void afterMapperThrowing(Throwable ex) {
        logException("MAPPER", ex);
    }

    /**
     * 记录异常日志
     */
    private void logException(String layer, Throwable ex) {
        try {
            // 获取当前请求信息
            HttpServletRequest request = getCurrentRequest();
            String requestUri = request != null ? request.getRequestURI() : "";
            String method = request != null ? request.getMethod() : "";
            String ip = request != null ? request.getRemoteAddr() : "";
            String userAgent = request != null ? request.getHeader("User-Agent") : "";
            
            // 获取异常信息
            String exceptionClass = ex.getClass().getSimpleName();
            String exceptionMessage = ex.getMessage();
            StackTraceElement[] stackTrace = ex.getStackTrace();
            
            // 构建异常日志信息
            Map<String, Object> exceptionInfo = new HashMap<>();
            exceptionInfo.put("timestamp", LocalDateTime.now());
            exceptionInfo.put("layer", layer);
            exceptionInfo.put("requestUri", requestUri);
            exceptionInfo.put("method", method);
            exceptionInfo.put("ip", ip);
            exceptionInfo.put("userAgent", userAgent);
            exceptionInfo.put("exceptionClass", exceptionClass);
            exceptionInfo.put("exceptionMessage", exceptionMessage);
            exceptionInfo.put("stackTrace", getStackTraceString(stackTrace));
            exceptionInfo.put("logType", "EXCEPTION_LOG");
            
            // 记录错误日志
            log.error("异常日志 - 层级: {}, 请求: {} {}, 异常: {}, 错误: {}", 
                    layer, method, requestUri, exceptionClass, exceptionMessage, ex);
            
            // 发送到异常监控系统
            sendToExceptionSystem(exceptionInfo);
            
            // 异常告警检查
            checkExceptionAlert(exceptionClass, exceptionMessage, layer);
            
        } catch (Exception e) {
            log.error("记录异常日志失败", e);
        }
    }

    /**
     * 获取当前请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取堆栈跟踪字符串
     */
    private String getStackTraceString(StackTraceElement[] stackTrace) {
        if (stackTrace == null || stackTrace.length == 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(10, stackTrace.length); i++) {
            sb.append(stackTrace[i].toString()).append("\n");
        }
        
        return sb.toString();
    }

    /**
     * 发送到异常监控系统
     */
    private void sendToExceptionSystem(Map<String, Object> exceptionInfo) {
        try {
            // 这里可以将异常信息发送到Sentry等异常监控系统
            log.info("发送到异常监控系统: {}", exceptionInfo);
            
        } catch (Exception e) {
            log.error("发送异常信息失败", e);
        }
    }

    /**
     * 异常告警检查
     */
    private void checkExceptionAlert(String exceptionClass, String exceptionMessage, String layer) {
        try {
            // 定义需要告警的异常类型
            boolean needAlert = false;
            String alertLevel = "INFO";
            
            // 检查异常类型
            if (exceptionClass.contains("NullPointerException") || 
                exceptionClass.contains("OutOfMemoryError") ||
                exceptionClass.contains("SQLException") ||
                exceptionClass.contains("ConnectException")) {
                needAlert = true;
                alertLevel = "CRITICAL";
            } else if (exceptionClass.contains("BusinessException") ||
                      exceptionClass.contains("IllegalArgumentException")) {
                needAlert = true;
                alertLevel = "WARNING";
            }
            
            // 检查异常消息关键词
            if (exceptionMessage != null) {
                if (exceptionMessage.contains("Connection refused") ||
                    exceptionMessage.contains("Timeout") ||
                    exceptionMessage.contains("Database") ||
                    exceptionMessage.contains("Authentication failed")) {
                    needAlert = true;
                    alertLevel = "CRITICAL";
                }
            }
            
            // 发送告警
            if (needAlert) {
                sendExceptionAlert(exceptionClass, exceptionMessage, layer, alertLevel);
            }
            
        } catch (Exception e) {
            log.error("异常告警检查失败", e);
        }
    }

    /**
     * 发送异常告警
     */
    private void sendExceptionAlert(String exceptionClass, String exceptionMessage, String layer, String level) {
        try {
            Map<String, Object> alertInfo = new HashMap<>();
            alertInfo.put("timestamp", LocalDateTime.now());
            alertInfo.put("exceptionClass", exceptionClass);
            alertInfo.put("exceptionMessage", exceptionMessage);
            alertInfo.put("layer", layer);
            alertInfo.put("alertLevel", level);
            alertInfo.put("alertType", "EXCEPTION_ALERT");
            
            log.error("异常告警 - 层级: {}, 级别: {}, 异常: {}, 错误: {}", 
                    layer, level, exceptionClass, exceptionMessage);
            
            // 这里可以集成告警系统，如邮件、短信、钉钉等
            log.info("发送异常告警: {}", alertInfo);
            
        } catch (Exception e) {
            log.error("发送异常告警失败", e);
        }
    }
    
}