package com.example.admin.aspect;

import com.example.admin.annotation.BizLog;
import com.example.admin.entity.SysLog;
import com.example.admin.service.SysLogService;
import com.example.admin.service.WebSocketLogService;
import com.example.admin.utils.JwtUtils;
import com.example.admin.utils.RequestUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务操作日志切面
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class BizLogAspect {

    private final ObjectMapper objectMapper;
    private final JwtUtils jwtUtils;
    private final SysLogService sysLogService;
    private final WebSocketLogService webSocketLogService;

    /**
     * 定义切入点：标记了@BizLog注解的方法
     */
    @Pointcut("@annotation(com.example.admin.annotation.BizLog)")
    public void bizLogPointcut() {
    }

    /**
     * 环绕通知：记录业务操作日志
     */
    @Around("bizLogPointcut()")
    public Object aroundBizLog(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        HttpServletRequest request = getCurrentRequest();
        String requestUri = request != null ? request.getRequestURI() : "";
        String method = request != null ? request.getMethod() : "";
        String ip = RequestUtils.getClientIp(request);
        String userAgent = request != null ? request.getHeader("User-Agent") : "";
        
        // 获取当前用户
        String username = getCurrentUsername();
        
        // 获取方法信息
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String description = getBizLogDescription(joinPoint);
        
        // 获取BizLog注解
        BizLog bizLog = getBizLogAnnotation(joinPoint);
        
        // 获取请求参数
        String requestParams = "";
        if (bizLog == null || bizLog.recordRequest()) {
            Object[] args = joinPoint.getArgs();
            requestParams = getRequestParams(args);
        }
        
        // 记录开始日志
        log.info("业务操作开始 - 用户: {}, 操作: {}, 方法: {}.{}", username, description, className, methodName);
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录成功日志
            String response = "";
            if (bizLog == null || bizLog.recordResponse()) {
                response = objectMapper.writeValueAsString(result);
            }
            log.info("业务操作成功 - 用户: {}, 操作: {}, 执行时间: {}ms", 
                    username, description, executionTime);
            
            // 构建日志信息
            Map<String, Object> logInfo = buildLogInfo(username, description, className, methodName, 
                    requestUri, method, ip, userAgent, requestParams, response, 
                    executionTime, true, null);
            
            // 发送到日志系统
            sendToLogSystem(logInfo, bizLog);
            
            return result;
            
        } catch (Exception e) {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录异常日志
            String errorMsg = e.getMessage();
            log.error("业务操作失败 - 用户: {}, 操作: {}, 执行时间: {}ms, 错误: {}", 
                    username, description, executionTime, errorMsg, e);
            
            // 构建日志信息
            Map<String, Object> logInfo = buildLogInfo(username, description, className, methodName, 
                    requestUri, method, ip, userAgent, requestParams, null, 
                    executionTime, false, errorMsg);
            
            // 发送到日志系统
            sendToLogSystem(logInfo, bizLog);
            
            // 重新抛出异常
            throw e;
        }
    }

    /**
     * 获取当前请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUsername() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                String token = request.getHeader("Authorization");
                if (token != null && token.startsWith("Bearer ")) {
                    token = token.substring(7);
                    return jwtUtils.getUsernameFromToken(token);
                }
            }
            return "anonymous";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取业务日志描述
     */
    private String getBizLogDescription(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            BizLog bizLog = method.getAnnotation(BizLog.class);
            
            if (bizLog != null && !bizLog.value().isEmpty()) {
                return bizLog.value();
            }
            
            return method.getName();
        } catch (Exception e) {
            return "未知操作";
        }
    }
    
    /**
     * 获取BizLog注解
     */
    private BizLog getBizLogAnnotation(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            return method.getAnnotation(BizLog.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取请求参数
     */
    private String getRequestParams(Object[] args) {
        try {
            if (args == null || args.length == 0) {
                return "";
            }
            
            Map<String, Object> params = new HashMap<>();
            for (int i = 0; i < args.length; i++) {
                params.put("param" + i, args[i]);
            }
            
            return objectMapper.writeValueAsString(params);
        } catch (Exception e) {
            return "参数解析失败";
        }
    }

    /**
     * 构建日志信息
     */
    private Map<String, Object> buildLogInfo(String username, String description, String className, String methodName,
                                           String requestUri, String method, String ip, String userAgent,
                                           String requestParams, String response, long executionTime,
                                           boolean success, String errorMsg) {
        Map<String, Object> logInfo = new HashMap<>();
        logInfo.put("timestamp", LocalDateTime.now());
        logInfo.put("username", username);
        logInfo.put("description", description);
        logInfo.put("className", className);
        logInfo.put("methodName", methodName);
        logInfo.put("requestUri", requestUri);
        logInfo.put("method", method);
        logInfo.put("ip", ip);
        logInfo.put("userAgent", userAgent);
        logInfo.put("requestParams", requestParams);
        logInfo.put("response", response);
        logInfo.put("executionTime", executionTime);
        logInfo.put("success", success);
        logInfo.put("errorMsg", errorMsg);
        logInfo.put("logType", "BIZ_LOG");
        return logInfo;
    }

    /**
     * 发送到日志系统
     */
    private void sendToLogSystem(Map<String, Object> logInfo, BizLog bizLog) {
        try {
            // 构建SysLog实体
            SysLog sysLog = new SysLog();
            sysLog.setLogType((String) logInfo.get("logType"));
            sysLog.setUsername((String) logInfo.get("username"));
            sysLog.setDescription((String) logInfo.get("description"));
            sysLog.setClassName((String) logInfo.get("className"));
            sysLog.setMethodName((String) logInfo.get("methodName"));
            sysLog.setRequestUri((String) logInfo.get("requestUri"));
            sysLog.setMethod((String) logInfo.get("method"));
            sysLog.setIp((String) logInfo.get("ip"));
            sysLog.setUserAgent((String) logInfo.get("userAgent"));
            sysLog.setRequestParams((String) logInfo.get("requestParams"));
            sysLog.setResponse((String) logInfo.get("response"));
            sysLog.setExecutionTime((Long) logInfo.get("executionTime"));
            sysLog.setSuccess((Boolean) logInfo.get("success"));
            sysLog.setErrorMsg((String) logInfo.get("errorMsg"));
            sysLog.setLevel("INFO");
            
            // 根据注解配置设置模块名和操作类型
            if (bizLog != null && !bizLog.value().isEmpty()) {
                sysLog.setModuleName(extractModuleNameFromBizLog(bizLog.value()));
                sysLog.setOperationType(bizLog.type());
            } else {
                sysLog.setModuleName(extractModuleName((String) logInfo.get("className")));
                sysLog.setOperationType(extractOperationType((String) logInfo.get("description")));
            }
            
            // 保存到数据库
            sysLogService.createBizLog(
                sysLog.getUsername(),
                sysLog.getDescription(),
                sysLog.getClassName(),
                sysLog.getMethodName(),
                sysLog.getRequestUri(),
                sysLog.getMethod(),
                sysLog.getIp(),
                sysLog.getUserAgent(),
                sysLog.getRequestParams(),
                sysLog.getResponse(),
                sysLog.getExecutionTime(),
                sysLog.getSuccess(),
                sysLog.getErrorMsg()
            );
            
            // 实时推送日志
            webSocketLogService.pushRealTimeLog(sysLog);
            
            log.info("日志保存和推送成功: {}", sysLog.getDescription());
            
        } catch (Exception e) {
            log.error("保存日志到数据库失败", e);
        }
    }
    
    /**
     * 从BizLog注解描述提取模块名
     */
    private String extractModuleNameFromBizLog(String description) {
        if (description == null) return "未知模块";
        
        String desc = description.toLowerCase();
        if (desc.contains("用户")) return "用户管理";
        if (desc.contains("角色")) return "角色管理";
        if (desc.contains("菜单")) return "菜单管理";
        if (desc.contains("配置")) return "系统配置";
        if (desc.contains("个人") || desc.contains("资料")) return "个人中心";
        if (desc.contains("日志")) return "日志管理";
        if (desc.contains("监控")) return "系统监控";
        
        return "其他模块";
    }
    
    /**
     * 从类名提取模块名
     */
    private String extractModuleName(String className) {
        if (className == null) return "未知模块";
        
        if (className.contains("User")) return "用户管理";
        if (className.contains("Role")) return "角色管理";
        if (className.contains("Menu")) return "菜单管理";
        if (className.contains("Config")) return "系统配置";
        if (className.contains("Profile")) return "个人中心";
        if (className.contains("Log")) return "日志管理";
        
        return "其他模块";
    }
    
    /**
     * 从操作描述提取操作类型
     */
    private String extractOperationType(String description) {
        if (description == null) return "未知操作";
        
        String desc = description.toLowerCase();
        if (desc.contains("创建") || desc.contains("新增") || desc.contains("添加") || desc.contains("save") || desc.contains("create") || desc.contains("add")) {
            return "新增";
        }
        if (desc.contains("更新") || desc.contains("修改") || desc.contains("编辑") || desc.contains("update") || desc.contains("edit") || desc.contains("modify")) {
            return "修改";
        }
        if (desc.contains("删除") || desc.contains("移除") || desc.contains("delete") || desc.contains("remove")) {
            return "删除";
        }
        if (desc.contains("查询") || desc.contains("获取") || desc.contains("get") || desc.contains("find") || desc.contains("query") || desc.contains("select")) {
            return "查询";
        }
        if (desc.contains("登录") || desc.contains("login")) {
            return "登录";
        }
        if (desc.contains("退出") || desc.contains("logout")) {
            return "退出";
        }
        
        return "其他操作";
    }
    
}