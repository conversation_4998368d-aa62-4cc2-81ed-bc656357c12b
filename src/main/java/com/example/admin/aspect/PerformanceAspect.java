package com.example.admin.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控切面
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Aspect
@Component
@Slf4j
public class PerformanceAspect {

    /**
     * 定义切入点：Controller层的所有方法
     */
    @Pointcut("execution(* com.example.admin.controller.*.*(..))")
    public void controllerPointcut() {
    }

    /**
     * 定义切入点：Service层的所有方法
     */
    @Pointcut("execution(* com.example.admin.service.*.*(..))")
    public void servicePointcut() {
    }

    /**
     * 环绕通知：监控Controller层性能
     */
    @Around("controllerPointcut()")
    public Object aroundController(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, "CONTROLLER");
    }

    /**
     * 环绕通知：监控Service层性能
     */
    @Around("servicePointcut()")
    public Object aroundService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, "SERVICE");
    }

    /**
     * 性能监控核心方法
     */
    private Object monitorPerformance(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取方法信息
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        // 记录开始时间
        log.debug("性能监控开始 - 层级: {}, 类: {}, 方法: {}", layer, className, methodName);
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 性能日志记录
            logPerformance(className, methodName, layer, executionTime, true, null);
            
            // 性能告警检查
            checkPerformanceAlert(className, methodName, layer, executionTime);
            
            return result;
            
        } catch (Exception e) {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录异常性能日志
            logPerformance(className, methodName, layer, executionTime, false, e.getMessage());
            
            // 重新抛出异常
            throw e;
        }
    }

    /**
     * 记录性能日志
     */
    private void logPerformance(String className, String methodName, String layer, 
                               long executionTime, boolean success, String errorMsg) {
        try {
            Map<String, Object> performanceInfo = new HashMap<>();
            performanceInfo.put("timestamp", LocalDateTime.now());
            performanceInfo.put("className", className);
            performanceInfo.put("methodName", methodName);
            performanceInfo.put("layer", layer);
            performanceInfo.put("executionTime", executionTime);
            performanceInfo.put("success", success);
            performanceInfo.put("errorMsg", errorMsg);
            performanceInfo.put("logType", "PERFORMANCE_LOG");
            
            // 根据执行时间选择不同的日志级别
            if (executionTime > 5000) {
                log.error("性能告警 - {} {}.{} 执行时间过长: {}ms", layer, className, methodName, executionTime);
            } else if (executionTime > 2000) {
                log.warn("性能警告 - {} {}.{} 执行时间较长: {}ms", layer, className, methodName, executionTime);
            } else if (executionTime > 1000) {
                log.info("性能监控 - {} {}.{} 执行时间: {}ms", layer, className, methodName, executionTime);
            } else {
                log.debug("性能监控 - {} {}.{} 执行时间: {}ms", layer, className, methodName, executionTime);
            }
            
            // 发送到性能监控系统
            sendToPerformanceSystem(performanceInfo);
            
        } catch (Exception e) {
            log.error("记录性能日志失败", e);
        }
    }

    /**
     * 性能告警检查
     */
    private void checkPerformanceAlert(String className, String methodName, String layer, long executionTime) {
        try {
            // 定义告警阈值
            long warningThreshold = 2000; // 2秒警告
            long criticalThreshold = 5000; // 5秒严重警告
            
            if (executionTime > criticalThreshold) {
                log.error("严重性能告警 - {} {}.{} 执行时间: {}ms", layer, className, methodName, executionTime);
                // 这里可以发送邮件、短信等告警通知
                sendPerformanceAlert(className, methodName, layer, executionTime, "CRITICAL");
            } else if (executionTime > warningThreshold) {
                log.warn("性能告警 - {} {}.{} 执行时间: {}ms", layer, className, methodName, executionTime);
                sendPerformanceAlert(className, methodName, layer, executionTime, "WARNING");
            }
            
        } catch (Exception e) {
            log.error("性能告警检查失败", e);
        }
    }

    /**
     * 发送性能告警
     */
    private void sendPerformanceAlert(String className, String methodName, String layer, 
                                    long executionTime, String level) {
        try {
            Map<String, Object> alertInfo = new HashMap<>();
            alertInfo.put("timestamp", LocalDateTime.now());
            alertInfo.put("className", className);
            alertInfo.put("methodName", methodName);
            alertInfo.put("layer", layer);
            alertInfo.put("executionTime", executionTime);
            alertInfo.put("alertLevel", level);
            alertInfo.put("alertType", "PERFORMANCE_ALERT");
            
            log.info("发送性能告警: {}", alertInfo);
            // 这里可以集成告警系统，如邮件、短信、钉钉等
            
        } catch (Exception e) {
            log.error("发送性能告警失败", e);
        }
    }

    /**
     * 发送到性能监控系统
     */
    private void sendToPerformanceSystem(Map<String, Object> performanceInfo) {
        try {
            // 这里可以将性能数据发送到Prometheus、Grafana等监控系统
            log.debug("发送到性能监控系统: {}", performanceInfo);
            
        } catch (Exception e) {
            log.error("发送性能数据失败", e);
        }
    }
    
}