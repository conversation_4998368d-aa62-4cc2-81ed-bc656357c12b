package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.PermissionCreateRequest;
import com.example.admin.dto.request.PermissionQueryRequest;
import com.example.admin.dto.request.PermissionUpdateRequest;
import com.example.admin.dto.response.PermissionResponse;
import com.example.admin.service.SysPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统权限控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/permission")
@RequiredArgsConstructor
@Validated
public class SysPermissionController {

    private final SysPermissionService sysPermissionService;

    /**
     * 创建权限
     *
     * @param request 权限创建请求
     * @return 权限响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:permission:add')")
    public Result<PermissionResponse> createPermission(@Valid @RequestBody PermissionCreateRequest request) {
        log.info("创建权限请求: {}", request.getPermissionCode());
        
        PermissionResponse permissionResponse = sysPermissionService.createPermission(request);
        
        return Result.success("权限创建成功", permissionResponse);
    }

    /**
     * 更新权限
     *
     * @param request 权限更新请求
     * @return 权限响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:permission:update')")
    public Result<PermissionResponse> updatePermission(@Valid @RequestBody PermissionUpdateRequest request) {
        log.info("更新权限请求: {}", request.getId());

        PermissionResponse permissionResponse = sysPermissionService.updatePermission(request);

        return Result.success("权限更新成功", permissionResponse);
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:permission:delete')")
    public Result<Void> deletePermission(@PathVariable @NotNull Long id) {
        log.info("删除权限请求: {}", id);
        
        sysPermissionService.deletePermission(id);
        
        return Result.success("权限删除成功");
    }

    /**
     * 批量删除权限
     *
     * @param ids 权限ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:permission:delete')")
    public Result<Void> deletePermissions(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除权限请求: {}", ids);
        
        sysPermissionService.deletePermissions(ids);
        
        return Result.success("权限批量删除成功");
    }

    /**
     * 根据ID获取权限
     *
     * @param id 权限ID
     * @return 权限响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<PermissionResponse> getPermissionById(@PathVariable @NotNull Long id) {
        log.info("获取权限请求: {}", id);
        
        PermissionResponse permissionResponse = sysPermissionService.getPermissionById(id);
        
        return Result.success("获取权限成功", permissionResponse);
    }

    /**
     * 分页查询权限
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<Page<PermissionResponse>> getPermissionPage(@Valid PermissionQueryRequest request) {
        log.info("分页查询权限请求: {}", request);
        
        Page<PermissionResponse> page = sysPermissionService.getPermissionPage(request);
        
        return Result.success("查询权限成功", page);
    }

    /**
     * 获取权限列表
     *
     * @param request 查询请求
     * @return 权限列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<List<PermissionResponse>> getPermissionList(@Valid PermissionQueryRequest request) {
        log.info("查询权限列表请求: {}", request);
        
        List<PermissionResponse> permissions = sysPermissionService.getPermissionList(request);
        
        return Result.success("查询权限列表成功", permissions);
    }

    /**
     * 获取权限树形结构
     *
     * @return 权限树形结构
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<List<PermissionResponse>> getPermissionTree() {
        log.info("获取权限树形结构请求");
        
        List<PermissionResponse> permissionTree = sysPermissionService.getPermissionTree();
        
        return Result.success("获取权限树形结构成功", permissionTree);
    }

    /**
     * 启用权限
     *
     * @param id 权限ID
     * @return 操作结果
     */
    @PutMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('system:permission:update')")
    public Result<Void> enablePermission(@PathVariable @NotNull Long id) {
        log.info("启用权限请求: {}", id);

        sysPermissionService.enablePermission(id);

        return Result.success("权限启用成功");
    }

    /**
     * 禁用权限
     *
     * @param id 权限ID
     * @return 操作结果
     */
    @PutMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('system:permission:update')")
    public Result<Void> disablePermission(@PathVariable @NotNull Long id) {
        log.info("禁用权限请求: {}", id);

        sysPermissionService.disablePermission(id);

        return Result.success("权限禁用成功");
    }

    /**
     * 批量启用权限
     *
     * @param ids 权限ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/enable")
    @PreAuthorize("hasAuthority('system:permission:update')")
    public Result<Void> enablePermissions(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量启用权限请求: {}", ids);

        sysPermissionService.enablePermissions(ids);

        return Result.success("权限批量启用成功");
    }

    /**
     * 批量禁用权限
     *
     * @param ids 权限ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/disable")
    @PreAuthorize("hasAuthority('system:permission:update')")
    public Result<Void> disablePermissions(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量禁用权限请求: {}", ids);

        sysPermissionService.disablePermissions(ids);

        return Result.success("权限批量禁用成功");
    }

    /**
     * 根据角色ID获取权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @GetMapping("/role/{roleId}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<PermissionResponse>> getPermissionsByRoleId(@PathVariable @NotNull Long roleId) {
        log.info("根据角色ID获取权限列表请求: {}", roleId);

        List<PermissionResponse> permissions = sysPermissionService.getPermissionsByRoleId(roleId);

        return Result.success("获取角色权限列表成功", permissions);
    }

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('system:permission:query')")
    public Result<List<PermissionResponse>> getPermissionsByUserId(@PathVariable @NotNull Long userId) {
        log.info("根据用户ID获取权限列表请求: {}", userId);

        List<PermissionResponse> permissions = sysPermissionService.getPermissionsByUserId(userId);

        return Result.success("获取用户权限列表成功", permissions);
    }

    /**
     * 根据父权限ID获取子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @GetMapping("/children/{parentId}")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<List<PermissionResponse>> getChildrenByParentId(@PathVariable @NotNull Long parentId) {
        log.info("根据父权限ID获取子权限列表请求: {}", parentId);

        List<PermissionResponse> children = sysPermissionService.getChildrenByParentId(parentId);

        return Result.success("获取子权限列表成功", children);
    }

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @return 是否存在
     */
    @GetMapping("/check/code")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<Boolean> checkPermissionCode(@RequestParam String permissionCode) {
        log.info("检查权限编码是否存在: {}", permissionCode);

        boolean exists = sysPermissionService.existsByPermissionCode(permissionCode);

        return Result.success("检查权限编码完成", exists);
    }

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @return 是否存在
     */
    @GetMapping("/check/name")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<Boolean> checkPermissionName(@RequestParam String permissionName) {
        log.info("检查权限名称是否存在: {}", permissionName);

        boolean exists = sysPermissionService.existsByPermissionName(permissionName);

        return Result.success("检查权限名称完成", exists);
    }

    /**
     * 检查是否有子权限
     *
     * @param parentId 父权限ID
     * @return 是否有子权限
     */
    @GetMapping("/check/children/{parentId}")
    @PreAuthorize("hasAuthority('system:permission:list')")
    public Result<Boolean> hasChildren(@PathVariable @NotNull Long parentId) {
        log.info("检查是否有子权限: {}", parentId);

        boolean hasChildren = sysPermissionService.hasChildren(parentId);

        return Result.success("检查子权限完成", hasChildren);
    }

    /**
     * 导出权限数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:permission:export')")
    public Result<List<PermissionResponse>> exportPermissions(@Valid PermissionQueryRequest request) {
        log.info("导出权限数据请求: {}", request);

        List<PermissionResponse> permissions = sysPermissionService.exportPermissions(request);

        return Result.success("导出权限数据成功", permissions);
    }
}
