package com.example.admin.controller;

import com.example.admin.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 简单测试控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/api/simple")
public class SimpleTestController {

    /**
     * 最简单的测试接口
     */
    @GetMapping("/test")
    public String simpleTest() {
        log.info("简单测试接口被调用");
        return "Hello, World!";
    }

    /**
     * 返回Result的测试接口
     */
    @GetMapping("/result")
    public Result<String> resultTest() {
        log.info("Result测试接口被调用");
        return Result.success("测试成功");
    }
}