package com.example.admin.controller;

import com.example.admin.entity.SysLog;
import com.example.admin.service.WebSocketLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * WebSocket日志控制器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class WebSocketLogController {

    private final WebSocketLogService webSocketLogService;

    /**
     * 处理客户端连接请求
     */
    @MessageMapping("/logs/connect")
    public void handleConnect(@Payload Map<String, Object> message, SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            String username = (String) message.get("username");
            
            webSocketLogService.handleUserConnect(sessionId, username);
            
            log.info("WebSocket客户端连接成功: {}, 用户: {}", sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket连接失败", e);
        }
    }

    /**
     * 处理客户端断开连接请求
     */
    @MessageMapping("/logs/disconnect")
    public void handleDisconnect(@Payload Map<String, Object> message, SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            String username = (String) message.get("username");
            
            webSocketLogService.handleUserDisconnect(sessionId, username);
            
            log.info("WebSocket客户端断开连接: {}, 用户: {}", sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket断开连接失败", e);
        }
    }

    /**
     * 处理日志订阅请求
     */
    @MessageMapping("/logs/subscribe")
    public void handleSubscribe(@Payload Map<String, Object> message, SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            String username = (String) message.get("username");
            String destination = (String) message.get("destination");
            
            webSocketLogService.handleUserSubscribe(sessionId, username, destination);
            
            log.info("WebSocket客户端订阅: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket订阅失败", e);
        }
    }

    /**
     * 处理日志取消订阅请求
     */
    @MessageMapping("/logs/unsubscribe")
    public void handleUnsubscribe(@Payload Map<String, Object> message, SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            String username = (String) message.get("username");
            String destination = (String) message.get("destination");
            
            webSocketLogService.handleUserUnsubscribe(sessionId, username, destination);
            
            log.info("WebSocket客户端取消订阅: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket取消订阅失败", e);
        }
    }

    /**
     * 处理日志查询请求
     */
    @MessageMapping("/logs/query")
    @SendTo("/topic/logs/query/response")
    public Map<String, Object> handleLogQuery(@Payload Map<String, Object> message) {
        try {
            // 这里可以调用日志服务处理查询请求
            // 为了简化，返回一个示例响应
            Map<String, Object> response = Map.of(
                    "success", true,
                    "message", "日志查询请求已接收",
                    "data", message,
                    "timestamp", System.currentTimeMillis()
            );
            
            log.info("处理WebSocket日志查询请求: {}", message);
            
            return response;
            
        } catch (Exception e) {
            log.error("处理WebSocket日志查询请求失败", e);
            
            return Map.of(
                    "success", false,
                    "message", "处理日志查询请求失败: " + e.getMessage(),
                    "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * 处理日志统计请求
     */
    @MessageMapping("/logs/statistics")
    @SendTo("/topic/logs/statistics/response")
    public Map<String, Object> handleLogStatistics(@Payload Map<String, Object> message) {
        try {
            // 这里可以调用日志服务处理统计请求
            // 为了简化，返回一个示例响应
            Map<String, Object> response = Map.of(
                    "success", true,
                    "message", "日志统计请求已接收",
                    "data", message,
                    "timestamp", System.currentTimeMillis()
            );
            
            log.info("处理WebSocket日志统计请求: {}", message);
            
            return response;
            
        } catch (Exception e) {
            log.error("处理WebSocket日志统计请求失败", e);
            
            return Map.of(
                    "success", false,
                    "message", "处理日志统计请求失败: " + e.getMessage(),
                    "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * 处理心跳检测
     */
    @MessageMapping("/logs/heartbeat")
    @SendTo("/topic/logs/heartbeat/response")
    public Map<String, Object> handleHeartbeat(@Payload Map<String, Object> message) {
        try {
            String sessionId = (String) message.get("sessionId");
            String username = (String) message.get("username");
            
            Map<String, Object> response = Map.of(
                    "success", true,
                    "message", "心跳检测成功",
                    "sessionId", sessionId,
                    "username", username,
                    "timestamp", System.currentTimeMillis()
            );
            
            log.debug("处理WebSocket心跳检测: {}, 用户: {}", sessionId, username);
            
            return response;
            
        } catch (Exception e) {
            log.error("处理WebSocket心跳检测失败", e);
            
            return Map.of(
                    "success", false,
                    "message", "心跳检测失败: " + e.getMessage(),
                    "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * 推送实时日志（广播）
     */
    @MessageMapping("/logs/push")
    public void pushLog(@Payload SysLog sysLog) {
        try {
            webSocketLogService.pushRealTimeLog(sysLog);
            log.info("推送实时日志成功: {}", sysLog.getDescription());
            
        } catch (Exception e) {
            log.error("推送实时日志失败", e);
        }
    }

    /**
     * 推送告警信息
     */
    @MessageMapping("/logs/alert")
    public void pushAlert(@Payload Map<String, Object> alert) {
        try {
            String alertType = (String) alert.get("alertType");
            String alertMessage = (String) alert.get("alertMessage");
            Object alertData = alert.get("alertData");
            
            webSocketLogService.pushAlert(alertType, alertMessage, alertData);
            log.warn("推送告警信息成功: {} - {}", alertType, alertMessage);
            
        } catch (Exception e) {
            log.error("推送告警信息失败", e);
        }
    }

    /**
     * 推送性能指标
     */
    @MessageMapping("/logs/performance")
    public void pushPerformance(@Payload Map<String, Object> metrics) {
        try {
            webSocketLogService.pushPerformanceMetrics(metrics);
            log.info("推送性能指标成功");
            
        } catch (Exception e) {
            log.error("推送性能指标失败", e);
        }
    }

}