package com.example.admin.controller;

import com.example.admin.common.result.Result;
import com.example.admin.dto.request.MenuCreateRequest;
import com.example.admin.dto.request.MenuQueryRequest;
import com.example.admin.dto.request.MenuUpdateRequest;
import com.example.admin.dto.response.MenuResponse;
import com.example.admin.dto.response.MenuTreeResponse;
import com.example.admin.service.SysMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统菜单控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/menu")
@RequiredArgsConstructor
@Validated
public class SysMenuController {

    private final SysMenuService sysMenuService;

    /**
     * 创建菜单
     *
     * @param request 菜单创建请求
     * @return 菜单响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:menu:create')")
    public Result<MenuResponse> createMenu(@Valid @RequestBody MenuCreateRequest request) {
        log.info("创建菜单请求: {}", request.getMenuName());
        
        MenuResponse menuResponse = sysMenuService.createMenu(request);
        
        return Result.success("菜单创建成功", menuResponse);
    }

    /**
     * 更新菜单
     *
     * @param request 菜单更新请求
     * @return 菜单响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:menu:update')")
    public Result<MenuResponse> updateMenu(@Valid @RequestBody MenuUpdateRequest request) {
        log.info("更新菜单请求: {}", request.getId());

        MenuResponse menuResponse = sysMenuService.updateMenu(request);

        return Result.success("菜单更新成功", menuResponse);
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:menu:delete')")
    public Result<Void> deleteMenu(@PathVariable @NotNull(message = "菜单ID不能为空") Long id) {
        log.info("删除菜单请求: {}", id);
        
        sysMenuService.deleteMenu(id);
        
        return Result.success("菜单删除成功");
    }

    /**
     * 批量删除菜单
     *
     * @param ids 菜单ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:menu:delete')")
    public Result<Void> batchDeleteMenus(@RequestBody @NotEmpty(message = "菜单ID列表不能为空") List<Long> ids) {
        log.info("批量删除菜单请求: {}", ids);
        
        sysMenuService.batchDeleteMenus(ids);
        
        return Result.success("菜单批量删除成功");
    }

    /**
     * 根据ID获取菜单
     *
     * @param id 菜单ID
     * @return 菜单响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<MenuResponse> getMenuById(@PathVariable @NotNull(message = "菜单ID不能为空") Long id) {
        log.info("获取菜单请求: {}", id);
        
        MenuResponse menuResponse = sysMenuService.getMenuById(id);
        
        return Result.success("获取菜单成功", menuResponse);
    }

    /**
     * 获取菜单列表
     *
     * @param request 查询请求
     * @return 菜单列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuResponse>> getMenuList(@Valid MenuQueryRequest request) {
        log.info("查询菜单列表请求: {}", request);
        
        List<MenuResponse> menus = sysMenuService.getMenuList(request);
        
        return Result.success("查询菜单列表成功", menus);
    }

    /**
     * 获取菜单树形结构
     *
     * @return 菜单树形结构
     */
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuResponse>> getMenuTree() {
        log.info("获取菜单树形结构请求");
        
        List<MenuResponse> menuTree = sysMenuService.getMenuTree();
        
        return Result.success("获取菜单树形结构成功", menuTree);
    }

    /**
     * 根据用户ID获取菜单树形结构
     *
     * @param userId 用户ID
     * @return 菜单树形结构
     */
    @GetMapping("/tree/user/{userId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuTreeResponse>> getMenuTreeByUserId(@PathVariable @NotNull(message = "用户ID不能为空") Long userId) {
        log.info("根据用户ID获取菜单树形结构请求: {}", userId);
        
        List<MenuTreeResponse> menuTree = sysMenuService.getMenuTreeByUserId(userId);
        
        return Result.success("获取用户菜单树形结构成功", menuTree);
    }

    /**
     * 根据角色ID获取菜单树形结构
     *
     * @param roleId 角色ID
     * @return 菜单树形结构
     */
    @GetMapping("/tree/role/{roleId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuTreeResponse>> getMenuTreeByRoleId(@PathVariable @NotNull(message = "角色ID不能为空") Long roleId) {
        log.info("根据角色ID获取菜单树形结构请求: {}", roleId);
        
        List<MenuTreeResponse> menuTree = sysMenuService.getMenuTreeByRoleId(roleId);
        
        return Result.success("获取角色菜单树形结构成功", menuTree);
    }

    /**
     * 获取所有启用的菜单树形结构（用于路由生成）
     *
     * @return 菜单树形结构
     */
    @GetMapping("/tree/enabled")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuTreeResponse>> getEnabledMenuTree() {
        log.info("获取所有启用的菜单树形结构请求");
        
        List<MenuTreeResponse> menuTree = sysMenuService.getEnabledMenuTree();
        
        return Result.success("获取启用菜单树形结构成功", menuTree);
    }

    /**
     * 启用菜单
     *
     * @param id 菜单ID
     * @return 操作结果
     */
    @PutMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('system:menu:update')")
    public Result<Void> enableMenu(@PathVariable @NotNull(message = "菜单ID不能为空") Long id) {
        log.info("启用菜单请求: {}", id);
        
        sysMenuService.enableMenu(id);
        
        return Result.success("菜单启用成功");
    }

    /**
     * 禁用菜单
     *
     * @param id 菜单ID
     * @return 操作结果
     */
    @PutMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('system:menu:update')")
    public Result<Void> disableMenu(@PathVariable @NotNull(message = "菜单ID不能为空") Long id) {
        log.info("禁用菜单请求: {}", id);
        
        sysMenuService.disableMenu(id);
        
        return Result.success("菜单禁用成功");
    }

    /**
     * 批量启用菜单
     *
     * @param ids 菜单ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/enable")
    @PreAuthorize("hasAuthority('system:menu:update')")
    public Result<Void> batchEnableMenus(@RequestBody @NotEmpty(message = "菜单ID列表不能为空") List<Long> ids) {
        log.info("批量启用菜单请求: {}", ids);
        
        sysMenuService.batchEnableMenus(ids);
        
        return Result.success("菜单批量启用成功");
    }

    /**
     * 批量禁用菜单
     *
     * @param ids 菜单ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/disable")
    @PreAuthorize("hasAuthority('system:menu:update')")
    public Result<Void> batchDisableMenus(@RequestBody @NotEmpty(message = "菜单ID列表不能为空") List<Long> ids) {
        log.info("批量禁用菜单请求: {}", ids);
        
        sysMenuService.batchDisableMenus(ids);
        
        return Result.success("菜单批量禁用成功");
    }

    /**
     * 根据父菜单ID获取子菜单列表
     *
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    @GetMapping("/children/{parentId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<MenuResponse>> getChildrenByParentId(@PathVariable @NotNull(message = "父菜单ID不能为空") Long parentId) {
        log.info("根据父菜单ID获取子菜单列表请求: {}", parentId);
        
        List<MenuResponse> children = sysMenuService.getChildrenByParentId(parentId);
        
        return Result.success("获取子菜单列表成功", children);
    }

    /**
     * 检查菜单名称是否存在
     *
     * @param menuName 菜单名称
     * @param excludeId 排除的菜单ID（可选）
     * @return 检查结果
     */
    @GetMapping("/check/name")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<Boolean> checkMenuNameExists(@RequestParam String menuName,
                                               @RequestParam(required = false) Long excludeId) {
        log.info("检查菜单名称是否存在请求: {}, 排除ID: {}", menuName, excludeId);

        boolean exists = excludeId != null ?
            sysMenuService.existsByMenuName(menuName, excludeId) :
            sysMenuService.existsByMenuName(menuName);

        return Result.success("检查菜单名称完成", exists);
    }

    /**
     * 检查路由路径是否存在
     *
     * @param path 路由路径
     * @param excludeId 排除的菜单ID（可选）
     * @return 检查结果
     */
    @GetMapping("/check/path")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<Boolean> checkPathExists(@RequestParam String path,
                                           @RequestParam(required = false) Long excludeId) {
        log.info("检查路由路径是否存在请求: {}, 排除ID: {}", path, excludeId);

        boolean exists = excludeId != null ?
            sysMenuService.existsByPath(path, excludeId) :
            sysMenuService.existsByPath(path);

        return Result.success("检查路由路径完成", exists);
    }

    /**
     * 检查权限标识是否存在
     *
     * @param perms 权限标识
     * @param excludeId 排除的菜单ID（可选）
     * @return 检查结果
     */
    @GetMapping("/check/perms")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<Boolean> checkPermsExists(@RequestParam String perms,
                                            @RequestParam(required = false) Long excludeId) {
        log.info("检查权限标识是否存在请求: {}, 排除ID: {}", perms, excludeId);

        boolean exists = excludeId != null ?
            sysMenuService.existsByPerms(perms, excludeId) :
            sysMenuService.existsByPerms(perms);

        return Result.success("检查权限标识完成", exists);
    }

    /**
     * 检查是否有子菜单
     *
     * @param parentId 父菜单ID
     * @return 检查结果
     */
    @GetMapping("/check/children/{parentId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Boolean> checkHasChildren(@PathVariable @NotNull(message = "父菜单ID不能为空") Long parentId) {
        log.info("检查是否有子菜单请求: {}", parentId);

        boolean hasChildren = sysMenuService.hasChildren(parentId);

        return Result.success("检查子菜单完成", hasChildren);
    }

    /**
     * 根据用户ID获取权限标识列表
     *
     * @param userId 用户ID
     * @return 权限标识列表
     */
    @GetMapping("/perms/user/{userId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<String>> getPermsByUserId(@PathVariable @NotNull(message = "用户ID不能为空") Long userId) {
        log.info("根据用户ID获取权限标识列表请求: {}", userId);

        List<String> perms = sysMenuService.getPermsByUserId(userId);

        return Result.success("获取用户权限标识列表成功", perms);
    }

    /**
     * 根据角色ID获取权限标识列表
     *
     * @param roleId 角色ID
     * @return 权限标识列表
     */
    @GetMapping("/perms/role/{roleId}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<List<String>> getPermsByRoleId(@PathVariable @NotNull(message = "角色ID不能为空") Long roleId) {
        log.info("根据角色ID获取权限标识列表请求: {}", roleId);

        List<String> perms = sysMenuService.getPermsByRoleId(roleId);

        return Result.success("获取角色权限标识列表成功", perms);
    }
}
