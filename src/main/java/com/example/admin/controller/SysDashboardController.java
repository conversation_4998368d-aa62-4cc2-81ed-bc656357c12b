package com.example.admin.controller;

import com.example.admin.common.result.Result;
import com.example.admin.dto.response.DashboardResponse;
import com.example.admin.service.SysDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 仪表盘统计控制器
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class SysDashboardController {

    private final SysDashboardService sysDashboardService;

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/statistics")
    public Result<DashboardResponse> getDashboardStatistics() {
        try {
            log.info("获取仪表盘统计数据");
            DashboardResponse statistics = sysDashboardService.getDashboardStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            return Result.error("获取仪表盘统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户数量
     */
    @GetMapping("/online-users")
    public Result<Long> getOnlineUserCount() {
        try {
            log.info("获取在线用户数量");
            long onlineCount = sysDashboardService.getOnlineUserCount();
            return Result.success(onlineCount);
        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return Result.error("获取在线用户数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/system-info")
    public Result<Map<String, Object>> getSystemInfo() {
        try {
            log.info("获取系统信息");
            Map<String, Object> systemInfo = sysDashboardService.getSystemInfo();
            return Result.success(systemInfo);
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return Result.error("获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近活动
     */
    @GetMapping("/recent-activity")
    public Result<Map<String, Object>> getRecentActivity(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            log.info("获取最近活动，限制数量: {}", limit);
            Map<String, Object> recentActivity = sysDashboardService.getRecentActivity(limit);
            return Result.success(recentActivity);
        } catch (Exception e) {
            log.error("获取最近活动失败", e);
            return Result.error("获取最近活动失败: " + e.getMessage());
        }
    }
}