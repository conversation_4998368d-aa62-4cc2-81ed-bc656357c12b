package com.example.admin.controller;

import com.example.admin.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证诊断控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/api/auth/debug")
public class AuthDebugController {

    /**
     * 诊断JWT认证状态
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> debugAuthStatus(
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        Map<String, Object> debugInfo = new HashMap<>();
        
        // 1. 检查请求头
        debugInfo.put("authHeader", authHeader != null ? "Present" : "Missing");
        if (authHeader != null) {
            debugInfo.put("authHeaderValue", authHeader.substring(0, Math.min(authHeader.length(), 50)) + "...");
        }
        
        // 2. 检查Security Context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            debugInfo.put("authentication", "Present");
            debugInfo.put("username", authentication.getName());
            debugInfo.put("authenticated", authentication.isAuthenticated());
            debugInfo.put("authorities", authentication.getAuthorities());
            debugInfo.put("credentials", authentication.getCredentials() != null ? "Present" : "Null");
        } else {
            debugInfo.put("authentication", "Null");
        }
        
        // 3. 检查用户详情
        if (authentication != null && authentication.getPrincipal() != null) {
            Object principal = authentication.getPrincipal();
            debugInfo.put("principalType", principal.getClass().getSimpleName());
            debugInfo.put("principal", principal.toString());
        }
        
        log.info("JWT认证诊断信息: {}", debugInfo);
        
        return Result.success("JWT认证诊断信息", debugInfo);
    }
    
    /**
     * 测试公共访问（无需认证）
     */
    @GetMapping("/public")
    public Result<String> publicAccess() {
        return Result.success("公共访问测试成功");
    }
    
    /**
     * 测试认证访问（需要认证但无需特定权限）
     */
    @GetMapping("/authenticated")
    public Result<String> authenticatedAccess() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return Result.success("认证访问测试成功，当前用户: " + authentication.getName());
    }
}