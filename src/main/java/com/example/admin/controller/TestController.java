package com.example.admin.controller;

import com.example.admin.common.result.Result;
import com.example.admin.service.SysPermissionService;
import com.example.admin.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证基础框架是否正常工作
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/public/test")
@RequiredArgsConstructor
public class TestController {

    private final SysUserService sysUserService;
    private final SysPermissionService sysPermissionService;

    /**
     * Hello World 测试接口
     */
    @GetMapping("/hello")
    public Result<String> hello() {
        log.info("Hello World 接口被调用");
        return Result.success("Hello World! Demo Admin 后台管理系统启动成功！");
    }

    /**
     * 系统信息测试接口
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        log.info("系统信息接口被调用");
        
        Map<String, Object> info = new HashMap<>();
        info.put("projectName", "Demo Admin 后台管理系统");
        info.put("version", "1.0.0");
        info.put("author", "fumouren");
        info.put("currentTime", LocalDateTime.now());
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("springBootVersion", "3.5.4");
        
        return Result.success("获取系统信息成功", info);
    }

    /**
     * 异常测试接口
     */
    @GetMapping("/error")
    public Result<Void> error() {
        log.info("异常测试接口被调用");
        throw new RuntimeException("这是一个测试异常");
    }

    /**
     * 权限测试接口 - 需要用户权限
     */
    @GetMapping("/auth/user")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<String> testUserPermission() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("用户权限测试接口被调用，当前用户: {}", auth.getName());
        return Result.success("用户权限测试成功！当前用户: " + auth.getName());
    }

    /**
     * 权限测试接口 - 需要管理员权限
     */
    @GetMapping("/auth/admin")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<String> testAdminPermission() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("管理员权限测试接口被调用，当前用户: {}", auth.getName());
        return Result.success("管理员权限测试成功！当前用户: " + auth.getName());
    }

    /**
     * 获取当前用户权限信息
     */
    @GetMapping("/auth/current-user")
    public Result<Map<String, Object>> getCurrentUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        log.info("获取当前用户信息，用户名: {}", auth.getName());
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("username", auth.getName());
        userInfo.put("authorities", auth.getAuthorities());
        userInfo.put("authenticated", auth.isAuthenticated());
        
        return Result.success("获取当前用户信息成功", userInfo);
    }

    /**
     * 检查权限配置
     */
    @GetMapping("/auth/check-permissions")
    public Result<String> checkPermissions() {
        log.info("检查权限配置...");
        
        // 检查权限服务是否正常工作
        try {
            // 尝试获取权限列表
            sysPermissionService.getPermissionList(null);
            log.info("权限服务正常工作");
        } catch (Exception e) {
            log.error("权限服务异常: {}", e.getMessage(), e);
            return Result.error("权限服务异常: " + e.getMessage());
        }
        
        return Result.success("权限配置检查完成，请查看日志获取详细信息");
    }
}
