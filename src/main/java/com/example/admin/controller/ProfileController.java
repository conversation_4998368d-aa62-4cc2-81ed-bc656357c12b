package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.*;
import com.example.admin.service.ProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 个人中心控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/api/profile")
@RequiredArgsConstructor
@Validated
public class ProfileController {

    private final ProfileService profileService;

    /**
     * 获取个人信息
     *
     * @return 个人信息
     */
    @GetMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public Result<ProfileResponse> getProfile() {
        log.info("获取个人信息请求");
        
        ProfileResponse profile = profileService.getProfile();
        
        return Result.success("获取个人信息成功", profile);
    }

    /**
     * 更新个人信息
     *
     * @param request 更新请求
     * @return 更新后的个人信息
     */
    @PutMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public Result<ProfileResponse> updateProfile(@Valid @RequestBody ProfileUpdateRequest request) {
        log.info("更新个人信息请求: {}", request);
        
        ProfileResponse profile = profileService.updateProfile(request);
        
        return Result.success("个人信息更新成功", profile);
    }

    /**
     * 修改密码
     *
     * @param request 密码修改请求
     * @return 操作结果
     */
    @PutMapping("/password")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> changePassword(@Valid @RequestBody PasswordChangeRequest request) {
        log.info("修改密码请求");
        
        profileService.changePassword(request);
        
        return Result.success("密码修改成功");
    }

    /**
     * 上传头像（文件方式）
     *
     * @param file 头像文件
     * @return 头像URL
     */
    @PostMapping("/avatar")
    @PreAuthorize("isAuthenticated()")
    public Result<String> uploadAvatar(@RequestParam("file") @Valid @NotNull(message = "文件不能为空") 
                                       @org.springframework.web.bind.annotation.RequestPart MultipartFile file) {
        log.info("上传头像请求: {}", file.getOriginalFilename());
        
        String avatarUrl = profileService.uploadAvatar(file);
        
        return Result.success("头像上传成功", avatarUrl);
    }

    /**
     * 上传头像（Base64方式）
     *
     * @param request 头像上传请求
     * @return 头像URL
     */
    @PostMapping("/avatar/base64")
    @PreAuthorize("isAuthenticated()")
    public Result<String> uploadAvatarBase64(@Valid @RequestBody AvatarUploadRequest request) {
        log.info("上传头像(Base64)请求: {}", request.getFileName());
        
        String avatarUrl = profileService.uploadAvatarBase64(request);
        
        return Result.success("头像上传成功", avatarUrl);
    }

    /**
     * 获取个人设置
     *
     * @return 个人设置
     */
    @GetMapping("/settings")
    @PreAuthorize("isAuthenticated()")
    public Result<SettingsResponse> getSettings() {
        log.info("获取个人设置请求");
        
        SettingsResponse settings = profileService.getSettings();
        
        return Result.success("获取个人设置成功", settings);
    }

    /**
     * 更新个人设置
     *
     * @param request 设置更新请求
     * @return 更新后的个人设置
     */
    @PutMapping("/settings")
    @PreAuthorize("isAuthenticated()")
    public Result<SettingsResponse> updateSettings(@Valid @RequestBody SettingsUpdateRequest request) {
        log.info("更新个人设置请求: {}", request);
        
        SettingsResponse settings = profileService.updateSettings(request);
        
        return Result.success("个人设置更新成功", settings);
    }

    /**
     * 获取用户偏好设置
     *
     * @return 偏好设置映射
     */
    @GetMapping("/preferences")
    @PreAuthorize("isAuthenticated()")
    public Result<Map<String, Object>> getPreferences() {
        log.info("获取用户偏好设置请求");
        
        Long currentUserId = getCurrentUserId();
        Map<String, Object> preferences = profileService.getPreferences(currentUserId);
        
        return Result.success("获取用户偏好设置成功", preferences);
    }

    /**
     * 设置用户偏好
     *
     * @param request 偏好设置请求
     * @return 操作结果
     */
    @PostMapping("/preferences")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> setPreference(@Valid @RequestBody PreferenceSetRequest request) {
        log.info("设置用户偏好请求: {}", request);
        
        profileService.setPreference(request);
        
        return Result.success("用户偏好设置成功");
    }

    /**
     * 获取指定偏好设置
     *
     * @param preferenceKey 偏好键
     * @return 偏好值
     */
    @GetMapping("/preferences/{preferenceKey}")
    @PreAuthorize("isAuthenticated()")
    public Result<Object> getPreference(@PathVariable @NotNull String preferenceKey) {
        log.info("获取指定偏好设置请求: {}", preferenceKey);
        
        Long currentUserId = getCurrentUserId();
        Object preference = profileService.getPreference(currentUserId, preferenceKey);
        
        return Result.success("获取指定偏好设置成功", preference);
    }

    /**
     * 删除偏好设置
     *
     * @param preferenceKey 偏好键
     * @return 操作结果
     */
    @DeleteMapping("/preferences/{preferenceKey}")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> deletePreference(@PathVariable @NotNull String preferenceKey) {
        log.info("删除偏好设置请求: {}", preferenceKey);
        
        Long currentUserId = getCurrentUserId();
        profileService.deletePreference(currentUserId, preferenceKey);
        
        return Result.success("偏好设置删除成功");
    }

    /**
     * 获取登录设备列表
     *
     * @return 设备列表
     */
    @GetMapping("/devices")
    @PreAuthorize("isAuthenticated()")
    public Result<List<DeviceResponse>> getDevices() {
        log.info("获取登录设备列表请求");
        
        List<DeviceResponse> devices = profileService.getDevices();
        
        return Result.success("获取登录设备列表成功", devices);
    }

    /**
     * 删除登录设备
     *
     * @param deviceId 设备ID
     * @return 操作结果
     */
    @DeleteMapping("/devices/{deviceId}")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> removeDevice(@PathVariable @NotNull Long deviceId) {
        log.info("删除登录设备请求: {}", deviceId);
        
        profileService.removeDevice(deviceId);
        
        return Result.success("登录设备删除成功");
    }

    /**
     * 设置设备信任状态
     *
     * @param deviceId 设备ID
     * @param isTrust 是否信任
     * @return 操作结果
     */
    @PutMapping("/devices/{deviceId}/trust")
    @PreAuthorize("isAuthenticated()")
    public Result<Void> setDeviceTrust(@PathVariable @NotNull Long deviceId, 
                                       @RequestParam @NotNull Boolean isTrust) {
        log.info("设置设备信任状态请求: {} - {}", deviceId, isTrust);
        
        profileService.setDeviceTrust(deviceId, isTrust);
        
        return Result.success("设备信任状态设置成功");
    }

    /**
     * 分页获取个人操作日志
     *
     * @param request 查询请求
     * @return 日志分页列表
     */
    @GetMapping("/logs")
    @PreAuthorize("isAuthenticated()")
    public Result<Page<UserLogResponse>> getUserLogs(@Valid UserLogQueryRequest request) {
        log.info("分页获取个人操作日志请求: {}", request);
        
        Page<UserLogResponse> logs = profileService.getUserLogs(request);
        
        return Result.success("获取个人操作日志成功", logs);
    }

    /**
     * 获取密码修改历史
     *
     * @param limit 限制数量
     * @return 密码修改历史
     */
    @GetMapping("/password/history")
    @PreAuthorize("isAuthenticated()")
    public Result<List<PasswordHistoryResponse>> getPasswordHistory(
            @RequestParam(defaultValue = "10") Integer limit) {
        log.info("获取密码修改历史请求: {}", limit);
        
        List<PasswordHistoryResponse> history = profileService.getPasswordHistory(limit);
        
        return Result.success("获取密码修改历史成功", history);
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        // 从Spring Security上下文中获取当前用户ID
        // 这里简化处理，实际应该从ProfileService中获取
        return 1L; // 临时返回，实际应该从安全上下文中获取
    }
}