package com.example.admin.controller;

import com.example.admin.common.result.Result;
import com.example.admin.dto.request.LoginRequest;
import com.example.admin.dto.response.LoginResponse;
import com.example.admin.service.AuthService;
import com.example.admin.service.CaptchaService;
import com.example.admin.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final JwtUtils jwtUtils;
    private final CaptchaService captchaService;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Validated @RequestBody LoginRequest loginRequest) {
        log.info("用户登录请求: {}", loginRequest.getUsername());
        
        LoginResponse loginResponse = authService.login(loginRequest);
        
        return Result.success("登录成功", loginResponse);
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求
     * @return 响应结果
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        String token = jwtUtils.getTokenFromHeader(authHeader);
        
        if (token != null) {
            authService.logout(token);
            log.info("用户登出成功");
        }
        
        return Result.success("登出成功");
    }

    /**
     * 刷新令牌
     *
     * @param request HTTP请求
     * @return 新的令牌
     */
    @PostMapping("/refresh")
    public Result<String> refreshToken(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        String token = jwtUtils.getTokenFromHeader(authHeader);
        
        if (token == null) {
            return Result.error("令牌不能为空");
        }
        
        String newToken = authService.refreshToken(token);
        
        return Result.success(newToken, "令牌刷新成功");
    }

    /**
     * 获取当前用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/me")
    public Result<Object> getCurrentUser(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        String token = jwtUtils.getTokenFromHeader(authHeader);
        
        if (token == null || !jwtUtils.validateToken(token)) {
            return Result.error("令牌无效");
        }
        
        String username = jwtUtils.getUsernameFromToken(token);
        Long userId = jwtUtils.getUserIdFromToken(token);
        
        // 构建用户信息响应
        return Result.success("获取用户信息成功", new Object() {
            public final Long userId = AuthController.this.jwtUtils.getUserIdFromToken(token);
            public final String username = AuthController.this.jwtUtils.getUsernameFromToken(token);
        });
    }

    /**
     * 验证令牌有效性
     *
     * @param request HTTP请求
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Result<Boolean> validateToken(HttpServletRequest request) {
        String authHeader = request.getHeader(jwtUtils.getHeader());
        String token = jwtUtils.getTokenFromHeader(authHeader);

        if (token == null) {
            return Result.success("令牌为空", false);
        }

        boolean isValid = jwtUtils.validateToken(token);

        return Result.success(isValid ? "令牌有效" : "令牌无效", isValid);
    }

    /**
     * 获取验证码
     *
     * @return 验证码信息
     */
    @GetMapping("/captcha")
    public Result<Map<String, String>> getCaptcha() {
        log.info("获取验证码请求");

        Map<String, String> captchaInfo = captchaService.generateCaptcha();

        return Result.success("获取验证码成功", captchaInfo);
    }
}
