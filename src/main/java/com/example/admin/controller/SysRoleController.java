package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.RoleCreateRequest;
import com.example.admin.dto.request.RolePermissionAssignRequest;
import com.example.admin.dto.request.RoleQueryRequest;
import com.example.admin.dto.request.RoleUpdateRequest;
import com.example.admin.dto.response.RoleResponse;
import com.example.admin.service.SysRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统角色控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/role")
@RequiredArgsConstructor
@Validated
public class SysRoleController {

    private final SysRoleService sysRoleService;

    /**
     * 创建角色
     *
     * @param request 角色创建请求
     * @return 角色响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<RoleResponse> createRole(@Valid @RequestBody RoleCreateRequest request) {
        log.info("创建角色请求: {}", request.getRoleCode());
        
        RoleResponse roleResponse = sysRoleService.createRole(request);
        
        return Result.success("角色创建成功", roleResponse);
    }

    /**
     * 更新角色
     *
     * @param request 角色更新请求
     * @return 角色响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:role:update')")
    public Result<RoleResponse> updateRole(@Valid @RequestBody RoleUpdateRequest request) {
        log.info("更新角色请求: {}", request.getId());

        RoleResponse roleResponse = sysRoleService.updateRole(request);

        return Result.success("角色更新成功", roleResponse);
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:delete')")
    public Result<Void> deleteRole(@PathVariable @NotNull Long id) {
        log.info("删除角色请求: {}", id);
        
        sysRoleService.deleteRole(id);
        
        return Result.success("角色删除成功");
    }

    /**
     * 批量删除角色
     *
     * @param ids 角色ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:role:delete')")
    public Result<Void> deleteRoles(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除角色请求: {}", ids);
        
        sysRoleService.deleteRoles(ids);
        
        return Result.success("角色批量删除成功");
    }

    /**
     * 根据ID获取角色
     *
     * @param id 角色ID
     * @return 角色响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<RoleResponse> getRoleById(@PathVariable @NotNull Long id) {
        log.info("获取角色请求: {}", id);
        
        RoleResponse roleResponse = sysRoleService.getRoleById(id);
        
        return Result.success("获取角色成功", roleResponse);
    }

    /**
     * 分页查询角色
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Page<RoleResponse>> getRolePage(@Valid RoleQueryRequest request) {
        log.info("分页查询角色请求: {}", request);

        Page<RoleResponse> page = sysRoleService.getRolePage(request);

        return Result.success("查询角色成功", page);
    }

    /**
     * 获取角色列表
     *
     * @param request 查询请求
     * @return 角色列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<List<RoleResponse>> getRoleList(@Valid RoleQueryRequest request) {
        log.info("查询角色列表请求: {}", request);

        List<RoleResponse> roles = sysRoleService.getRoleList(request);

        return Result.success("查询角色列表成功", roles);
    }

    /**
     * 启用角色
     *
     * @param id 角色ID
     * @return 操作结果
     */
    @PutMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('system:role:update')")
    public Result<Void> enableRole(@PathVariable @NotNull Long id) {
        log.info("启用角色请求: {}", id);

        sysRoleService.enableRole(id);

        return Result.success("角色启用成功");
    }

    /**
     * 禁用角色
     *
     * @param id 角色ID
     * @return 操作结果
     */
    @PutMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('system:role:update')")
    public Result<Void> disableRole(@PathVariable @NotNull Long id) {
        log.info("禁用角色请求: {}", id);

        sysRoleService.disableRole(id);

        return Result.success("角色禁用成功");
    }

    /**
     * 批量启用角色
     *
     * @param ids 角色ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/enable")
    @PreAuthorize("hasAuthority('system:role:update')")
    public Result<Void> enableRoles(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量启用角色请求: {}", ids);

        sysRoleService.enableRoles(ids);

        return Result.success("角色批量启用成功");
    }

    /**
     * 批量禁用角色
     *
     * @param ids 角色ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/disable")
    @PreAuthorize("hasAuthority('system:role:update')")
    public Result<Void> disableRoles(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量禁用角色请求: {}", ids);

        sysRoleService.disableRoles(ids);

        return Result.success("角色批量禁用成功");
    }

    /**
     * 分配角色权限
     *
     * @param request 角色权限分配请求
     * @return 操作结果
     */
    @PutMapping("/permissions")
    @PreAuthorize("hasAuthority('system:role:permission')")
    public Result<Void> assignPermissions(@Valid @RequestBody RolePermissionAssignRequest request) {
        log.info("分配角色权限请求: roleId={}, permissionIds={}", request.getRoleId(), request.getPermissionIds());

        sysRoleService.assignPermissions(request);

        return Result.success("角色权限分配成功");
    }

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/{roleId}/permissions")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<List<Long>> getRolePermissionIds(@PathVariable @NotNull Long roleId) {
        log.info("获取角色权限ID列表请求: {}", roleId);

        List<Long> permissionIds = sysRoleService.getRolePermissionIds(roleId);

        return Result.success("获取角色权限ID列表成功", permissionIds);
    }

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @return 是否存在
     */
    @GetMapping("/check/code")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<Boolean> checkRoleCode(@RequestParam String roleCode) {
        log.info("检查角色编码是否存在: {}", roleCode);

        boolean exists = sysRoleService.existsByRoleCode(roleCode);

        return Result.success("检查角色编码完成", exists);
    }

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @return 是否存在
     */
    @GetMapping("/check/name")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Boolean> checkRoleName(@RequestParam String roleName) {
        log.info("检查角色名称是否存在: {}", roleName);

        boolean exists = sysRoleService.existsByRoleName(roleName);

        return Result.success("检查角色名称完成", exists);
    }

    /**
     * 导出角色数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:role:export')")
    public Result<List<RoleResponse>> exportRoles(@Valid RoleQueryRequest request) {
        log.info("导出角色数据请求: {}", request);

        List<RoleResponse> roles = sysRoleService.exportRoles(request);

        return Result.success("导出角色数据成功", roles);
    }
}
