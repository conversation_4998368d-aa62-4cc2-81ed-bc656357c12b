package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.UserQueryRequest;
import com.example.admin.dto.response.UserResponse;
import com.example.admin.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 临时用户控制器（用于测试，无权限验证）
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/api/test/user")
@RequiredArgsConstructor
public class TestUserController {

    private final SysUserService sysUserService;

    /**
     * 分页查询用户（无权限验证）
     */
    @GetMapping("/page")
    public Result<Page<UserResponse>> getUserPage(@ModelAttribute UserQueryRequest request) {
        log.info("测试分页查询用户请求: {}", request);
        
        Page<UserResponse> page = sysUserService.getUserPage(request);
        
        return Result.success("查询用户成功", page);
    }

    /**
     * 获取用户列表（无权限验证）
     */
    @GetMapping("/list")
    public Result<Page<UserResponse>> getUserList(@ModelAttribute UserQueryRequest request) {
        log.info("测试查询用户列表请求: {}", request);

        Page<UserResponse> page = sysUserService.getUserPage(request);

        return Result.success("查询用户列表成功", page);
    }

    /**
     * 根据ID获取用户（无权限验证）
     */
    @GetMapping("/{id}")
    public Result<UserResponse> getUserById(@PathVariable Long id) {
        log.info("测试获取用户请求: {}", id);
        
        UserResponse userResponse = sysUserService.getUserById(id);
        
        return Result.success("获取用户成功", userResponse);
    }
}