package com.example.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.admin.common.result.PageResult;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.LogQueryRequest;
import com.example.admin.dto.response.LogResponse;
import com.example.admin.dto.response.LogStatisticsResponse;
import com.example.admin.entity.SysLog;
import com.example.admin.service.SysLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统日志控制器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/logs")
@RequiredArgsConstructor
public class SysLogController {

    private final SysLogService sysLogService;

    /**
     * 分页查询日志列表
     */
    @GetMapping("/page")
    public Result<PageResult<LogResponse>> getLogPage(LogQueryRequest query) {
        try {
            IPage<SysLog> page = sysLogService.getLogPage(query);
            List<LogResponse> records = page.getRecords().stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            PageResult<LogResponse> pageResult = new PageResult<>(
                    page.getCurrent(),
                    page.getSize(),
                    page.getTotal(),
                    records
            );
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("分页查询日志列表失败", e);
            return Result.error("查询日志列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询日志列表
     */
    @GetMapping("/list")
    public Result<List<LogResponse>> getLogList(LogQueryRequest query) {
        try {
            List<SysLog> logs = sysLogService.getLogList(query);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("查询日志列表失败", e);
            return Result.error("查询日志列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志统计信息
     */
    @GetMapping("/statistics")
    public Result<LogStatisticsResponse> getLogStatistics(LogQueryRequest query) {
        try {
            Map<String, Object> statistics = sysLogService.getLogStatistics(query);
            Map<String, Object> chartStatistics = sysLogService.getLogChartStatistics(query);
            
            LogStatisticsResponse response = new LogStatisticsResponse();
            
            // 基础统计
            response.setTotalLogCount((Long) statistics.get("totalLogCount"));
            response.setExceptionLogCount((Long) statistics.get("exceptionLogCount"));
            response.setPerformanceLogCount((Long) statistics.get("performanceLogCount"));
            
            // 类型统计
            response.setLogTypeStatistics((List<Map<String, Object>>) statistics.get("logTypeStatistics"));
            
            // 用户统计
            response.setUserStatistics((List<Map<String, Object>>) statistics.get("userStatistics"));
            
            // 模块统计
            response.setModuleStatistics((List<Map<String, Object>>) statistics.get("moduleStatistics"));
            
            // 操作统计
            response.setOperationStatistics((List<Map<String, Object>>) statistics.get("operationStatistics"));
            
            // 性能指标
            Map<String, Object> performanceMetrics = (Map<String, Object>) statistics.get("performanceMetrics");
            if (performanceMetrics != null) {
                response.setSuccessLogCount((Long) performanceMetrics.get("successCount"));
                response.setFailedLogCount((Long) performanceMetrics.get("failedCount"));
                response.setSlowQueryCount((Long) performanceMetrics.get("slowQueryCount"));
                response.setMinExecutionTime((Long) performanceMetrics.get("minExecutionTime"));
                response.setMaxExecutionTime((Long) performanceMetrics.get("maxExecutionTime"));
                
                Object avgExecutionTime = performanceMetrics.get("avgExecutionTime");
                if (avgExecutionTime instanceof Double) {
                    response.setAvgExecutionTime((Double) avgExecutionTime);
                } else if (avgExecutionTime instanceof Long) {
                    response.setAvgExecutionTime(((Long) avgExecutionTime).doubleValue());
                }
                
                // 计算错误率和成功率
                Long totalCount = (Long) performanceMetrics.get("totalCount");
                Long failedCount = (Long) performanceMetrics.get("failedCount");
                if (totalCount > 0) {
                    response.setErrorRate((double) failedCount / totalCount * 100);
                    response.setSuccessRate(100 - response.getErrorRate());
                }
            }
            
            // 图表数据
            response.setDateTrend((List<Map<String, Object>>) chartStatistics.get("dateTrend"));
            response.setModuleDistribution((List<Map<String, Object>>) chartStatistics.get("moduleDistribution"));
            response.setUserActivity((List<Map<String, Object>>) chartStatistics.get("userActivity"));
            response.setOperationFrequency((List<Map<String, Object>>) chartStatistics.get("operationFrequency"));
            response.setPerformanceTrend((List<Map<String, Object>>) chartStatistics.get("performanceTrend"));
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取日志统计信息失败", e);
            return Result.error("获取日志统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的日志
     */
    @GetMapping("/recent")
    public Result<List<LogResponse>> getRecentLogs(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<SysLog> logs = sysLogService.getRecentLogs(limit);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取最近的日志失败", e);
            return Result.error("获取最近的日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取异常日志
     */
    @GetMapping("/exceptions")
    public Result<List<LogResponse>> getExceptionLogs(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        try {
            List<SysLog> logs = sysLogService.getExceptionLogs(startTime, endTime);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取异常日志失败", e);
            return Result.error("获取异常日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取性能日志
     */
    @GetMapping("/performance")
    public Result<List<LogResponse>> getPerformanceLogs(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime,
            @RequestParam(defaultValue = "2000") Long threshold) {
        try {
            List<SysLog> logs = sysLogService.getPerformanceLogs(startTime, endTime, threshold);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取性能日志失败", e);
            return Result.error("获取性能日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取慢查询日志
     */
    @GetMapping("/slow-queries")
    public Result<List<LogResponse>> getSlowQueryLogs(
            @RequestParam(defaultValue = "2000") Long threshold,
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        try {
            List<SysLog> logs = sysLogService.getSlowQueryLogs(threshold, startTime, endTime);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取慢查询日志失败", e);
            return Result.error("获取慢查询日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取错误日志
     */
    @GetMapping("/errors")
    public Result<List<LogResponse>> getErrorLogs(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        try {
            List<SysLog> logs = sysLogService.getErrorLogs(startTime, endTime);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取错误日志失败", e);
            return Result.error("获取错误日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户操作轨迹
     */
    @GetMapping("/user-trail/{userId}")
    public Result<List<LogResponse>> getUserOperationTrail(
            @PathVariable Long userId,
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        try {
            List<SysLog> logs = sysLogService.getUserOperationTrail(userId, startTime, endTime);
            List<LogResponse> responses = logs.stream()
                    .map(this::convertToLogResponse)
                    .toList();
            
            return Result.success(responses);
        } catch (Exception e) {
            log.error("获取用户操作轨迹失败", e);
            return Result.error("获取用户操作轨迹失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门操作
     */
    @GetMapping("/popular-operations")
    public Result<List<Map<String, Object>>> getPopularOperations(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> popularOps = sysLogService.getPopularOperations(startTime, endTime, limit);
            return Result.success(popularOps);
        } catch (Exception e) {
            log.error("获取热门操作失败", e);
            return Result.error("获取热门操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getLogConfig() {
        try {
            Map<String, Object> config = sysLogService.getLogConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取日志配置失败", e);
            return Result.error("获取日志配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新日志配置
     */
    @PutMapping("/config")
    public Result<Boolean> updateLogConfig(@RequestBody Map<String, Object> config) {
        try {
            boolean success = sysLogService.updateLogConfig(config);
            return Result.success(success);
        } catch (Exception e) {
            log.error("更新日志配置失败", e);
            return Result.error("更新日志配置失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/clean")
    public Result<Boolean> cleanLogs(@RequestParam LocalDateTime beforeDate) {
        try {
            boolean success = sysLogService.cleanLogs(beforeDate);
            return Result.success(success);
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return Result.error("清理过期日志失败: " + e.getMessage());
        }
    }

    /**
     * 转换为日志响应对象
     */
    private LogResponse convertToLogResponse(SysLog log) {
        LogResponse response = new LogResponse();
        response.setId(log.getId());
        response.setCreateTime(log.getCreateTime());
        response.setLogType(log.getLogType());
        response.setUserId(log.getUserId());
        response.setUsername(log.getUsername());
        response.setDescription(log.getDescription());
        response.setRequestUri(log.getRequestUri());
        response.setMethod(log.getMethod());
        response.setIp(log.getIp());
        response.setExecutionTime(log.getExecutionTime());
        response.setSuccess(log.getSuccess());
        response.setErrorMsg(log.getErrorMsg());
        response.setModuleName(log.getModuleName());
        response.setOperationType(log.getOperationType());
        response.setBrowser(log.getBrowser());
        response.setOs(log.getOs());
        response.setLevel(log.getLevel());
        response.setExceptionType(log.getExceptionType());
        response.setThreadName(log.getThreadName());
                return response;
    }
    
}