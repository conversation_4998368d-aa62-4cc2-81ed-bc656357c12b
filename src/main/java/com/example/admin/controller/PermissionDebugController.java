package com.example.admin.controller;

import com.example.admin.dto.request.PermissionQueryRequest;
import com.example.admin.dto.response.PermissionResponse;
import com.example.admin.service.SysPermissionService;
import com.example.admin.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限调试控制器
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@RestController
@RequestMapping("/debug")
@RequiredArgsConstructor
public class PermissionDebugController {

    private final SysPermissionService sysPermissionService;
    private final SysUserService sysUserService;

    /**
     * 获取用户权限
     */
    @GetMapping("/user/{userId}/permissions")
    public List<PermissionResponse> getUserPermissions(@PathVariable Long userId) {
        log.info("获取用户权限: {}", userId);
        List<PermissionResponse> permissions = sysPermissionService.getPermissionsByUserId(userId);
        log.info("用户 {} 的权限数量: {}", userId, permissions.size());
        permissions.forEach(permission -> log.info("权限: {}", permission.getPermissionCode()));
        return permissions;
    }

    /**
     * 检查特定权限是否存在
     */
    @GetMapping("/permission/check")
    public boolean checkPermission(@RequestParam String permissionCode) {
        log.info("检查权限是否存在: {}", permissionCode);
        boolean exists = sysPermissionService.existsByPermissionCode(permissionCode);
        log.info("权限 {} 是否存在: {}", permissionCode, exists);
        return exists;
    }

    /**
     * 获取所有权限
     */
    @GetMapping("/permissions/all")
    public List<PermissionResponse> getAllPermissions() {
        log.info("获取所有权限");
        // 创建查询请求对象，避免空指针异常
        PermissionQueryRequest request = new PermissionQueryRequest();
        request.setTreeStructure(false);
        List<PermissionResponse> permissions = sysPermissionService.getPermissionList(request);
        log.info("总权限数量: {}", permissions.size());
        return permissions;
    }

    /**
     * 简单的测试接口，不需要认证
     */
    @GetMapping("/test")
    public String test() {
        return "Debug controller is working!";
    }
}