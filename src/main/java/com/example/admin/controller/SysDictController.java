package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.DictItemResponse;
import com.example.admin.dto.response.DictResponse;
import com.example.admin.service.SysDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据字典控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/dict")
@RequiredArgsConstructor
@Validated
public class SysDictController {

    private final SysDictService sysDictService;

    /**
     * 创建数据字典
     *
     * @param request 字典创建请求
     * @return 字典响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:dict:create')")
    public Result<DictResponse> createDict(@Valid @RequestBody DictCreateRequest request) {
        log.info("创建数据字典请求: {}", request.getDictCode());
        
        DictResponse dictResponse = sysDictService.createDict(request);
        
        return Result.success("数据字典创建成功", dictResponse);
    }

    /**
     * 更新数据字典
     *
     * @param request 字典更新请求
     * @return 字典响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:dict:update')")
    public Result<DictResponse> updateDict(@Valid @RequestBody DictUpdateRequest request) {
        log.info("更新数据字典请求: {}", request.getId());
        
        DictResponse dictResponse = sysDictService.updateDict(request);
        
        return Result.success("数据字典更新成功", dictResponse);
    }

    /**
     * 删除数据字典
     *
     * @param id 字典ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:dict:delete')")
    public Result<Void> deleteDict(@PathVariable @NotNull Long id) {
        log.info("删除数据字典请求: {}", id);
        
        sysDictService.deleteDict(id);
        
        return Result.success("数据字典删除成功");
    }

    /**
     * 批量删除数据字典
     *
     * @param ids 字典ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:dict:delete')")
    public Result<Void> deleteDicts(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除数据字典请求: {}", ids);
        
        sysDictService.deleteDicts(ids);
        
        return Result.success("数据字典批量删除成功");
    }

    /**
     * 根据ID获取数据字典
     *
     * @param id 字典ID
     * @return 字典响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<DictResponse> getDict(@PathVariable @NotNull Long id) {
        log.info("获取数据字典请求: {}", id);
        
        DictResponse dictResponse = sysDictService.getDictById(id);
        
        return Result.success("获取数据字典成功", dictResponse);
    }

    /**
     * 根据字典编码获取数据字典
     *
     * @param dictCode 字典编码
     * @return 字典响应
     */
    @GetMapping("/code/{dictCode}")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<DictResponse> getDictByCode(@PathVariable String dictCode) {
        log.info("根据编码获取数据字典请求: {}", dictCode);
        
        DictResponse dictResponse = sysDictService.getDictByCode(dictCode);
        
        return Result.success("获取数据字典成功", dictResponse);
    }

    /**
     * 分页查询数据字典
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<Page<DictResponse>> pageDicts(@Valid DictQueryRequest request) {
        log.info("分页查询数据字典请求: {}", request);
        
        Page<DictResponse> page = sysDictService.pageDicts(request);
        
        return Result.success("查询数据字典成功", page);
    }

    /**
     * 获取所有启用的字典
     *
     * @return 字典列表
     */
    @GetMapping("/enabled")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<List<DictResponse>> getEnabledDicts() {
        log.info("获取所有启用字典请求");
        
        List<DictResponse> dicts = sysDictService.getEnabledDicts();
        
        return Result.success("获取启用字典成功", dicts);
    }

    // 字典项相关接口

    /**
     * 创建字典项
     *
     * @param request 字典项创建请求
     * @return 字典项响应
     */
    @PostMapping("/item")
    @PreAuthorize("hasAuthority('system:dict:create')")
    public Result<DictItemResponse> createDictItem(@Valid @RequestBody DictItemCreateRequest request) {
        log.info("创建字典项请求: {}-{}", request.getDictId(), request.getItemCode());
        
        DictItemResponse itemResponse = sysDictService.createDictItem(request);
        
        return Result.success("字典项创建成功", itemResponse);
    }

    /**
     * 更新字典项
     *
     * @param request 字典项更新请求
     * @return 字典项响应
     */
    @PutMapping("/item")
    @PreAuthorize("hasAuthority('system:dict:update')")
    public Result<DictItemResponse> updateDictItem(@Valid @RequestBody DictItemUpdateRequest request) {
        log.info("更新字典项请求: {}", request.getId());
        
        DictItemResponse itemResponse = sysDictService.updateDictItem(request);
        
        return Result.success("字典项更新成功", itemResponse);
    }

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     * @return 操作结果
     */
    @DeleteMapping("/item/{id}")
    @PreAuthorize("hasAuthority('system:dict:delete')")
    public Result<Void> deleteDictItem(@PathVariable @NotNull Long id) {
        log.info("删除字典项请求: {}", id);
        
        sysDictService.deleteDictItem(id);
        
        return Result.success("字典项删除成功");
    }

    /**
     * 批量删除字典项
     *
     * @param ids 字典项ID列表
     * @return 操作结果
     */
    @DeleteMapping("/item/batch")
    @PreAuthorize("hasAuthority('system:dict:delete')")
    public Result<Void> deleteDictItems(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除字典项请求: {}", ids);
        
        sysDictService.deleteDictItems(ids);
        
        return Result.success("字典项批量删除成功");
    }

    /**
     * 根据字典ID获取字典项
     *
     * @param dictId 字典ID
     * @return 字典项列表
     */
    @GetMapping("/{dictId}/items")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<List<DictItemResponse>> getDictItems(@PathVariable @NotNull Long dictId) {
        log.info("获取字典项请求: {}", dictId);
        
        List<DictItemResponse> items = sysDictService.getDictItemsByDictId(dictId);
        
        return Result.success("获取字典项成功", items);
    }

    /**
     * 根据字典编码获取字典项
     *
     * @param dictCode 字典编码
     * @return 字典项列表
     */
    @GetMapping("/code/{dictCode}/items")
    @PreAuthorize("hasAuthority('system:dict:view')")
    public Result<List<DictItemResponse>> getDictItemsByCode(@PathVariable String dictCode) {
        log.info("根据编码获取字典项请求: {}", dictCode);
        
        List<DictItemResponse> items = sysDictService.getDictItemsByDictCode(dictCode);
        
        return Result.success("获取字典项成功", items);
    }
}
