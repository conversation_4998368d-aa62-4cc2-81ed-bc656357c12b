package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.annotation.BizLog;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.UserCreateRequest;
import com.example.admin.dto.request.UserPasswordResetRequest;
import com.example.admin.dto.request.UserQueryRequest;
import com.example.admin.dto.request.UserUpdateRequest;
import com.example.admin.dto.response.UserExportResponse;
import com.example.admin.dto.response.UserResponse;
import com.example.admin.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 系统用户控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
@Validated
public class SysUserController {

    private final SysUserService sysUserService;

    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:user:add')")
    @BizLog(value = "创建用户", type = "CREATE")
    public Result<UserResponse> createUser(@Valid @RequestBody UserCreateRequest request) {
        log.info("创建用户请求: {}", request.getUsername());
        
        UserResponse userResponse = sysUserService.createUser(request);
        
        return Result.success("用户创建成功", userResponse);
    }

    /**
     * 更新用户
     *
     * @param request 用户更新请求
     * @return 用户响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:user:edit')")
    @BizLog(value = "更新用户", type = "UPDATE")
    public Result<UserResponse> updateUser(@Valid @RequestBody UserUpdateRequest request) {
        log.info("更新用户请求: {}", request.getId());

        UserResponse userResponse = sysUserService.updateUser(request);

        return Result.success("用户更新成功", userResponse);
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:delete')")
    @BizLog(value = "删除用户", type = "DELETE")
    public Result<Void> deleteUser(@PathVariable @NotNull Long id) {
        log.info("删除用户请求: {}", id);
        
        sysUserService.deleteUser(id);
        
        return Result.success("用户删除成功");
    }

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:user:delete')")
    public Result<Void> deleteUsers(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除用户请求: {}", ids);
        
        sysUserService.deleteUsers(ids);
        
        return Result.success("用户批量删除成功");
    }

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<UserResponse> getUserById(@PathVariable @NotNull Long id) {
        log.info("获取用户请求: {}", id);
        
        UserResponse userResponse = sysUserService.getUserById(id);
        
        return Result.success("获取用户成功", userResponse);
    }

    /**
     * 分页查询用户
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<Page<UserResponse>> getUserPage(@Valid UserQueryRequest request) {
        log.info("分页查询用户请求: {}", request);

        Page<UserResponse> page = sysUserService.getUserPage(request);

        return Result.success("查询用户成功", page);
    }

    /**
     * 获取用户列表
     *
     * @param request 查询请求
     * @return 用户列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<List<UserResponse>> getUserList(@Valid UserQueryRequest request) {
        log.info("查询用户列表请求: {}", request);

        List<UserResponse> users = sysUserService.getUserList(request);

        return Result.success("查询用户列表成功", users);
    }

    /**
     * 启用用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    @PutMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> enableUser(@PathVariable @NotNull Long id) {
        log.info("启用用户请求: {}", id);
        
        sysUserService.enableUser(id);
        
        return Result.success("用户启用成功");
    }

    /**
     * 禁用用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    @PutMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> disableUser(@PathVariable @NotNull Long id) {
        log.info("禁用用户请求: {}", id);
        
        sysUserService.disableUser(id);
        
        return Result.success("用户禁用成功");
    }

    /**
     * 批量启用用户
     *
     * @param ids 用户ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/enable")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> enableUsers(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量启用用户请求: {}", ids);
        
        sysUserService.enableUsers(ids);
        
        return Result.success("用户批量启用成功");
    }

    /**
     * 批量禁用用户
     *
     * @param ids 用户ID列表
     * @return 操作结果
     */
    @PutMapping("/batch/disable")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<Void> disableUsers(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量禁用用户请求: {}", ids);
        
        sysUserService.disableUsers(ids);
        
        return Result.success("用户批量禁用成功");
    }

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @param request 密码重置请求
     * @return 操作结果
     */
    @PutMapping("/{id}/password/reset")
    @PreAuthorize("hasAuthority('system:user:resetPassword')")
    public Result<Void> resetPassword(@PathVariable @NotNull Long id, @Valid @RequestBody UserPasswordResetRequest request) {
        log.info("重置用户密码请求: {}", id);
        
        request.setUserId(id);
        sysUserService.resetPassword(request);
        
        return Result.success("密码重置成功");
    }

    /**
     * 重置用户密码为默认密码
     *
     * @param id 用户ID
     * @return 默认密码
     */
    @PutMapping("/{id}/password/default")
    @PreAuthorize("hasAuthority('system:user:resetPassword')")
    public Result<String> resetPasswordToDefault(@PathVariable @NotNull Long id) {
        log.info("重置用户密码为默认密码请求: {}", id);
        
        String defaultPassword = sysUserService.resetPasswordToDefault(id);
        
        return Result.success("密码重置为默认密码成功", defaultPassword);
    }

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @PutMapping("/{userId}/roles")
    @PreAuthorize("hasAuthority('system:user:role')")
    @BizLog(value = "分配用户角色", type = "UPDATE")
    public Result<Void> assignRoles(@PathVariable @NotNull Long userId,
                                   @RequestBody @NotEmpty(message = "角色ID列表不能为空") List<Long> roleIds) {
        log.info("分配用户角色请求: userId={}, roleIds={}", userId, roleIds);

        // 验证角色ID列表中的每个ID都为正数
        for (Long roleId : roleIds) {
            if (roleId == null || roleId <= 0) {
                return Result.error("角色ID必须为正数");
            }
        }

        sysUserService.assignRoles(userId, roleIds);

        return Result.success("用户角色分配成功");
    }

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于编辑时验证）
     * @return 是否存在
     */
    @GetMapping("/check/username")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<Boolean> checkUsername(@RequestParam String username, 
                                        @RequestParam(required = false) Long excludeId) {
        log.info("检查用户名是否存在: {}, excludeId={}", username, excludeId);

        boolean exists = excludeId != null 
                ? sysUserService.existsByUsername(username, excludeId)
                : sysUserService.existsByUsername(username);

        return Result.success("检查用户名完成", exists);
    }

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于编辑时验证）
     * @return 是否存在
     */
    @GetMapping("/check/email")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<Boolean> checkEmail(@RequestParam String email,
                                    @RequestParam(required = false) Long excludeId) {
        log.info("检查邮箱是否存在: {}, excludeId={}", email, excludeId);

        boolean exists = excludeId != null 
                ? sysUserService.existsByEmail(email, excludeId)
                : sysUserService.existsByEmail(email);

        return Result.success("检查邮箱完成", exists);
    }

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID（用于编辑时验证）
     * @return 是否存在
     */
    @GetMapping("/check/phone")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<Boolean> checkPhone(@RequestParam String phone,
                                   @RequestParam(required = false) Long excludeId) {
        log.info("检查手机号是否存在: {}, excludeId={}", phone, excludeId);

        boolean exists = excludeId != null 
                ? sysUserService.existsByPhone(phone, excludeId)
                : sysUserService.existsByPhone(phone);

        return Result.success("检查手机号完成", exists);
    }

    /**
     * 导出用户数据
     *
     * @param request 查询请求
     * @param response HTTP响应
     */
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:user:export')")
    @BizLog(value = "导出用户数据", type = "EXPORT")
    public void exportUsers(@Valid UserQueryRequest request, HttpServletResponse response) {
        log.info("导出用户数据请求: {}", request);

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("用户数据_" + System.currentTimeMillis(), StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取导出数据
            List<UserExportResponse> users = sysUserService.exportUsersForExcel(request);

            if (users == null || users.isEmpty()) {
                log.warn("没有找到符合条件的用户数据");
                response.getWriter().write("没有找到符合条件的用户数据");
                return;
            }

            // 导出Excel
            com.alibaba.excel.EasyExcel.write(response.getOutputStream(), UserExportResponse.class)
                    .sheet("用户数据")
                    .doWrite(users);

        } catch (Exception e) {
            log.error("导出用户数据失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出用户数据失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
            throw new RuntimeException("导出用户数据失败");
        }
    }
}
