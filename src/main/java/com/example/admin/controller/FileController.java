package com.example.admin.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件访问控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/files")
public class FileController {

    @Value("${demo-admin.file.upload-path:/uploads/}")
    private String uploadPath;

    /**
     * 访问文件
     *
     * @param request HTTP请求
     * @return 文件资源
     */
    @GetMapping("/**")
    public ResponseEntity<Resource> getFile(HttpServletRequest request) {
        // 从请求路径中提取文件路径
        String requestURI = request.getRequestURI();
        String requestPath = requestURI.replace("/api/files/", "");

        try {

            if (StrUtil.isBlank(requestPath)) {
                return ResponseEntity.notFound().build();
            }

            // 构建文件绝对路径
            String absolutePath = uploadPath + requestPath;
            File file = new File(absolutePath);

            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                log.warn("文件不存在: {}", absolutePath);
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件在上传目录内
            String canonicalUploadPath = new File(uploadPath).getCanonicalPath();
            String canonicalFilePath = file.getCanonicalPath();
            if (!canonicalFilePath.startsWith(canonicalUploadPath)) {
                log.warn("非法文件访问: {}", absolutePath);
                return ResponseEntity.badRequest().build();
            }

            // 创建文件资源
            Resource resource = new FileSystemResource(file);

            // 获取文件类型
            String contentType = getContentType(file.getName());

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));

            // 如果是图片，设置为内联显示；其他文件设置为下载
            if (contentType.startsWith("image/")) {
                headers.setContentDispositionFormData("inline", 
                    URLEncoder.encode(file.getName(), StandardCharsets.UTF_8));
            } else {
                headers.setContentDispositionFormData("attachment", 
                    URLEncoder.encode(file.getName(), StandardCharsets.UTF_8));
            }

            log.info("文件访问成功: {}", requestPath);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("文件访问失败: {}", requestPath, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     *
     * @param fileName 文件名
     * @return Content-Type
     */
    private String getContentType(String fileName) {
        String extension = FileUtil.extName(fileName).toLowerCase();
        
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "txt":
                return "text/plain";
            case "html":
                return "text/html";
            case "css":
                return "text/css";
            case "js":
                return "application/javascript";
            case "json":
                return "application/json";
            case "xml":
                return "application/xml";
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "7z":
                return "application/x-7z-compressed";
            default:
                return "application/octet-stream";
        }
    }
}
