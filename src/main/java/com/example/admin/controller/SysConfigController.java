package com.example.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.result.Result;
import com.example.admin.dto.request.ConfigCreateRequest;
import com.example.admin.dto.request.ConfigQueryRequest;
import com.example.admin.dto.request.ConfigUpdateRequest;
import com.example.admin.dto.response.ConfigResponse;
import com.example.admin.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统配置控制器
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/system/config")
@RequiredArgsConstructor
@Validated
public class SysConfigController {

    private final SysConfigService sysConfigService;

    /**
     * 创建系统配置
     *
     * @param request 配置创建请求
     * @return 配置响应
     */
    @PostMapping
    @PreAuthorize("hasAuthority('system:config:create')")
    public Result<ConfigResponse> createConfig(@Valid @RequestBody ConfigCreateRequest request) {
        log.info("创建系统配置请求: {}", request.getConfigKey());
        
        ConfigResponse configResponse = sysConfigService.createConfig(request);
        
        return Result.success("系统配置创建成功", configResponse);
    }

    /**
     * 更新系统配置
     *
     * @param request 配置更新请求
     * @return 配置响应
     */
    @PutMapping
    @PreAuthorize("hasAuthority('system:config:update')")
    public Result<ConfigResponse> updateConfig(@Valid @RequestBody ConfigUpdateRequest request) {
        log.info("更新系统配置请求: {}", request.getId());
        
        ConfigResponse configResponse = sysConfigService.updateConfig(request);
        
        return Result.success("系统配置更新成功", configResponse);
    }

    /**
     * 删除系统配置
     *
     * @param id 配置ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:config:delete')")
    public Result<Void> deleteConfig(@PathVariable @NotNull Long id) {
        log.info("删除系统配置请求: {}", id);
        
        sysConfigService.deleteConfig(id);
        
        return Result.success("系统配置删除成功");
    }

    /**
     * 批量删除系统配置
     *
     * @param ids 配置ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:config:delete')")
    public Result<Void> deleteConfigs(@RequestBody @NotEmpty List<Long> ids) {
        log.info("批量删除系统配置请求: {}", ids);
        
        sysConfigService.deleteConfigs(ids);
        
        return Result.success("系统配置批量删除成功");
    }

    /**
     * 根据ID获取系统配置
     *
     * @param id 配置ID
     * @return 配置响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:config:view')")
    public Result<ConfigResponse> getConfig(@PathVariable @NotNull Long id) {
        log.info("获取系统配置请求: {}", id);
        
        ConfigResponse configResponse = sysConfigService.getConfigById(id);
        
        return Result.success("获取系统配置成功", configResponse);
    }

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    @GetMapping("/value/{configKey}")
    @PreAuthorize("hasAuthority('system:config:view')")
    public Result<String> getConfigValue(@PathVariable String configKey) {
        log.info("获取配置值请求: {}", configKey);
        
        String configValue = sysConfigService.getConfigValue(configKey);
        
        return Result.success("获取配置值成功", configValue);
    }

    /**
     * 分页查询系统配置
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:config:view')")
    public Result<Page<ConfigResponse>> pageConfigs(@Valid ConfigQueryRequest request) {
        log.info("分页查询系统配置请求: {}", request);
        
        Page<ConfigResponse> page = sysConfigService.pageConfigs(request);
        
        return Result.success("查询系统配置成功", page);
    }

    /**
     * 获取所有启用的配置
     *
     * @return 配置列表
     */
    @GetMapping("/enabled")
    @PreAuthorize("hasAuthority('system:config:view')")
    public Result<List<ConfigResponse>> getEnabledConfigs() {
        log.info("获取所有启用配置请求");
        
        List<ConfigResponse> configs = sysConfigService.getEnabledConfigs();
        
        return Result.success("获取启用配置成功", configs);
    }

    /**
     * 刷新配置缓存
     *
     * @return 操作结果
     */
    @PostMapping("/refresh")
    @PreAuthorize("hasAuthority('system:config:refresh')")
    public Result<Void> refreshCache() {
        log.info("刷新配置缓存请求");
        
        sysConfigService.refreshCache();
        
        return Result.success("配置缓存刷新成功");
    }

    /**
     * 刷新指定配置缓存
     *
     * @param configKey 配置键
     * @return 操作结果
     */
    @PostMapping("/refresh/{configKey}")
    @PreAuthorize("hasAuthority('system:config:refresh')")
    public Result<Void> refreshCache(@PathVariable String configKey) {
        log.info("刷新指定配置缓存请求: {}", configKey);
        
        sysConfigService.refreshCache(configKey);
        
        return Result.success("配置缓存刷新成功");
    }
}
