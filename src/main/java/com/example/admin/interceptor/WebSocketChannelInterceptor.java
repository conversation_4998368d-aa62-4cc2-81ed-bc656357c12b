package com.example.admin.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * WebSocket拦截器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class WebSocketChannelInterceptor implements ChannelInterceptor {

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        try {
            StompHeaderAccessor accessor = StompHeaderAccessor.wrap(message);
            StompCommand command = accessor.getCommand();
            
            if (command == null) {
                return message;
            }
            
            String sessionId = accessor.getSessionId();
            
            switch (command) {
                case CONNECT:
                    handleConnect(accessor, sessionId);
                    break;
                case DISCONNECT:
                    handleDisconnect(accessor, sessionId);
                    break;
                case SUBSCRIBE:
                    handleSubscribe(accessor, sessionId);
                    break;
                case UNSUBSCRIBE:
                    handleUnsubscribe(accessor, sessionId);
                    break;
                case SEND:
                    handleSend(accessor, sessionId);
                    break;
                default:
                    log.debug("未处理的WebSocket命令: {}", command);
                    break;
            }
            
        } catch (Exception e) {
            log.error("WebSocket拦截器处理失败", e);
        }
        
        return message;
    }

    /**
     * 处理连接命令
     */
    private void handleConnect(StompHeaderAccessor accessor, String sessionId) {
        try {
            // 获取认证信息
            String token = accessor.getFirstNativeHeader("Authorization");
            String username = accessor.getFirstNativeHeader("username");
            
            // 验证认证信息
            if (token != null && token.startsWith("Bearer ")) {
                // 这里可以验证JWT token
                // String jwtToken = token.substring(7);
                // boolean isValid = validateToken(jwtToken);
                // if (isValid) {
                //     username = getUsernameFromToken(jwtToken);
                // }
            }
            
            // 设置用户信息到会话属性
            if (username != null && !username.isEmpty()) {
                Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
                sessionAttributes.put("username", username);
                sessionAttributes.put("authenticated", true);
            } else {
                // 如果没有认证信息，设置为匿名用户
                Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
                sessionAttributes.put("username", "anonymous");
                sessionAttributes.put("authenticated", false);
            }
            
            log.info("WebSocket连接请求: {}, 用户: {}", sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket连接请求失败: {}", sessionId, e);
        }
    }

    /**
     * 处理断开连接命令
     */
    private void handleDisconnect(StompHeaderAccessor accessor, String sessionId) {
        try {
            Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket断开连接请求: {}, 用户: {}", sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket断开连接请求失败: {}", sessionId, e);
        }
    }

    /**
     * 处理订阅命令
     */
    private void handleSubscribe(StompHeaderAccessor accessor, String sessionId) {
        try {
            String destination = accessor.getDestination();
            Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            Boolean authenticated = (Boolean) sessionAttributes.get("authenticated");
            
            // 检查订阅权限
            if (!checkSubscriptionPermission(username, authenticated, destination)) {
                log.warn("WebSocket订阅权限不足: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
                // 可以抛出异常来拒绝订阅
                // throw new IllegalStateException("订阅权限不足");
            }
            
            log.info("WebSocket订阅请求: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket订阅请求失败: {}", sessionId, e);
        }
    }

    /**
     * 处理取消订阅命令
     */
    private void handleUnsubscribe(StompHeaderAccessor accessor, String sessionId) {
        try {
            String destination = accessor.getDestination();
            Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket取消订阅请求: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket取消订阅请求失败: {}", sessionId, e);
        }
    }

    /**
     * 处理发送命令
     */
    private void handleSend(StompHeaderAccessor accessor, String sessionId) {
        try {
            String destination = accessor.getDestination();
            Map<String, Object> sessionAttributes = accessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            Boolean authenticated = (Boolean) sessionAttributes.get("authenticated");
            
            // 检查发送权限
            if (!checkSendPermission(username, authenticated, destination)) {
                log.warn("WebSocket发送权限不足: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
                // 可以抛出异常来拒绝发送
                // throw new IllegalStateException("发送权限不足");
            }
            
            log.info("WebSocket发送请求: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket发送请求失败: {}", sessionId, e);
        }
    }

    /**
     * 检查订阅权限
     */
    private boolean checkSubscriptionPermission(String username, Boolean authenticated, String destination) {
        // 匿名用户只能订阅公共频道
        if (!authenticated || "anonymous".equals(username)) {
            return destination != null && (
                    destination.startsWith("/topic/logs/public/") ||
                    destination.startsWith("/topic/logs/realtime")
            );
        }
        
        // 认证用户可以订阅更多频道
        return destination != null && (
                destination.startsWith("/topic/logs/") ||
                destination.startsWith("/topic/user/" + username) ||
                destination.startsWith("/topic/logs/user/")
        );
    }

    /**
     * 检查发送权限
     */
    private boolean checkSendPermission(String username, Boolean authenticated, String destination) {
        // 匿名用户只能发送到公共频道
        if (!authenticated || "anonymous".equals(username)) {
            return destination != null && (
                    destination.startsWith("/app/logs/public/") ||
                    destination.startsWith("/app/logs/heartbeat")
            );
        }
        
        // 认证用户可以发送到更多频道
        return destination != null && (
                destination.startsWith("/app/logs/") ||
                destination.startsWith("/app/user/" + username)
        );
    }

    /**
     * 验证JWT token（简化实现）
     */
    private boolean validateToken(String token) {
        // 这里应该实现JWT token验证逻辑
        // 为了简化，暂时返回true
        return true;
    }

    /**
     * 从JWT token中获取用户名（简化实现）
     */
    private String getUsernameFromToken(String token) {
        // 这里应该实现从JWT token中获取用户名的逻辑
        // 为了简化，暂时返回一个示例用户名
        return "testuser";
    }

}