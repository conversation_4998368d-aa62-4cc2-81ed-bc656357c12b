package com.example.admin.annotation;

import java.lang.annotation.*;

/**
 * 业务操作日志注解
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BizLog {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 操作类型
     */
    String type() default "OTHER";
    
    /**
     * 是否记录请求参数
     */
    boolean recordRequest() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean recordResponse() default true;
    
    /**
     * 是否记录执行时间
     */
    boolean recordExecutionTime() default true;
    
}