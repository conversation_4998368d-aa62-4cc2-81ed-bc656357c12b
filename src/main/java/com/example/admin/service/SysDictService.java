package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.DictItemResponse;
import com.example.admin.dto.response.DictResponse;
import com.example.admin.entity.SysDict;

import java.util.List;

/**
 * 数据字典服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysDictService extends IService<SysDict> {

    /**
     * 创建数据字典
     *
     * @param request 创建请求
     * @return 字典响应
     */
    DictResponse createDict(DictCreateRequest request);

    /**
     * 更新数据字典
     *
     * @param request 更新请求
     * @return 字典响应
     */
    DictResponse updateDict(DictUpdateRequest request);

    /**
     * 根据ID删除字典
     *
     * @param id 字典ID
     */
    void deleteDict(Long id);

    /**
     * 批量删除字典
     *
     * @param ids 字典ID列表
     */
    void deleteDicts(List<Long> ids);

    /**
     * 根据ID获取字典
     *
     * @param id 字典ID
     * @return 字典响应
     */
    DictResponse getDictById(Long id);

    /**
     * 根据字典编码获取字典
     *
     * @param dictCode 字典编码
     * @return 字典响应
     */
    DictResponse getDictByCode(String dictCode);

    /**
     * 分页查询字典
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<DictResponse> pageDicts(DictQueryRequest request);

    /**
     * 获取所有启用的字典
     *
     * @return 字典列表
     */
    List<DictResponse> getEnabledDicts();

    /**
     * 检查字典编码是否存在
     *
     * @param dictCode 字典编码
     * @param excludeId 排除的字典ID
     * @return 是否存在
     */
    boolean existsDictCode(String dictCode, Long excludeId);

    // 字典项相关方法

    /**
     * 创建字典项
     *
     * @param request 创建请求
     * @return 字典项响应
     */
    DictItemResponse createDictItem(DictItemCreateRequest request);

    /**
     * 更新字典项
     *
     * @param request 更新请求
     * @return 字典项响应
     */
    DictItemResponse updateDictItem(DictItemUpdateRequest request);

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     */
    void deleteDictItem(Long id);

    /**
     * 批量删除字典项
     *
     * @param ids 字典项ID列表
     */
    void deleteDictItems(List<Long> ids);

    /**
     * 根据字典ID获取字典项
     *
     * @param dictId 字典ID
     * @return 字典项列表
     */
    List<DictItemResponse> getDictItemsByDictId(Long dictId);

    /**
     * 根据字典编码获取字典项
     *
     * @param dictCode 字典编码
     * @return 字典项列表
     */
    List<DictItemResponse> getDictItemsByDictCode(String dictCode);

    /**
     * 检查字典项编码是否存在
     *
     * @param dictId 字典ID
     * @param itemCode 字典项编码
     * @param excludeId 排除的字典项ID
     * @return 是否存在
     */
    boolean existsDictItemCode(Long dictId, String itemCode, Long excludeId);
}
