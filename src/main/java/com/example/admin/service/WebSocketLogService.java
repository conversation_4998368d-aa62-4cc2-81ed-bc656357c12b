package com.example.admin.service;

import com.example.admin.entity.SysLog;

import java.util.List;
import java.util.Map;

/**
 * WebSocket日志推送服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface WebSocketLogService {

    /**
     * 推送实时日志
     */
    void pushRealTimeLog(SysLog log);

    /**
     * 批量推送实时日志
     */
    void pushBatchRealTimeLogs(List<SysLog> logs);

    /**
     * 推送日志统计信息
     */
    void pushLogStatistics(String statisticsType, Object statistics);

    /**
     * 推送告警信息
     */
    void pushAlert(String alertType, String alertMessage, Object alertData);

    /**
     * 推送性能指标
     */
    void pushPerformanceMetrics(Object metrics);

    /**
     * 推送系统状态
     */
    void pushSystemStatus(Object status);

    /**
     * 广播消息给所有客户端
     */
    void broadcastToAll(String destination, Object message);

    /**
     * 发送消息给指定用户
     */
    void sendToUser(String username, String destination, Object message);

    /**
     * 发送消息给指定会话
     */
    void sendToSession(String sessionId, String destination, Object message);

    /**
     * 获取在线客户端数量
     */
    int getOnlineClientCount();

    /**
     * 获取在线用户列表
     */
    List<String> getOnlineUsers();

    /**
     * 检查用户是否在线
     */
    boolean isUserOnline(String username);

    /**
     * 处理用户连接
     */
    void handleUserConnect(String sessionId, String username);

    /**
     * 处理用户断开连接
     */
    void handleUserDisconnect(String sessionId, String username);

    /**
     * 处理用户订阅
     */
    void handleUserSubscribe(String sessionId, String username, String destination);

    /**
     * 处理用户取消订阅
     */
    void handleUserUnsubscribe(String sessionId, String username, String destination);

    /**
     * 广播消息给所有用户
     */
    void broadcastToAllUsers(String destination, Object message);

    /**
     * 发送消息给指定用户
     */
    void sendMessageToUser(String username, String destination, Object message);

    /**
     * 获取在线用户信息
     */
    Map<String, String> getOnlineUsersInfo();

    /**
     * 获取用户订阅信息
     */
    Map<String, List<String>> getUserSubscriptions();

}