package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.request.ConfigCreateRequest;
import com.example.admin.dto.request.ConfigQueryRequest;
import com.example.admin.dto.request.ConfigUpdateRequest;
import com.example.admin.dto.response.ConfigResponse;
import com.example.admin.entity.SysConfig;

import java.util.List;

/**
 * 系统配置服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysConfigService extends IService<SysConfig> {

    /**
     * 创建系统配置
     *
     * @param request 创建请求
     * @return 配置响应
     */
    ConfigResponse createConfig(ConfigCreateRequest request);

    /**
     * 更新系统配置
     *
     * @param request 更新请求
     * @return 配置响应
     */
    ConfigResponse updateConfig(ConfigUpdateRequest request);

    /**
     * 根据ID删除配置
     *
     * @param id 配置ID
     */
    void deleteConfig(Long id);

    /**
     * 批量删除配置
     *
     * @param ids 配置ID列表
     */
    void deleteConfigs(List<Long> ids);

    /**
     * 根据ID获取配置
     *
     * @param id 配置ID
     * @return 配置响应
     */
    ConfigResponse getConfigById(Long id);

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值（带默认值）
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 分页查询配置
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<ConfigResponse> pageConfigs(ConfigQueryRequest request);

    /**
     * 获取所有启用的配置
     *
     * @return 配置列表
     */
    List<ConfigResponse> getEnabledConfigs();

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 根据配置键刷新单个配置缓存
     *
     * @param configKey 配置键
     */
    void refreshCache(String configKey);

    /**
     * 检查配置键是否存在
     *
     * @param configKey 配置键
     * @param excludeId 排除的配置ID
     * @return 是否存在
     */
    boolean existsConfigKey(String configKey, Long excludeId);
}
