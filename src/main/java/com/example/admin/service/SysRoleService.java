package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.dto.request.RoleCreateRequest;
import com.example.admin.dto.request.RoleQueryRequest;
import com.example.admin.dto.request.RoleUpdateRequest;
import com.example.admin.dto.request.RolePermissionAssignRequest;
import com.example.admin.dto.response.RoleResponse;
import com.example.admin.entity.SysRole;

import java.util.List;

/**
 * 系统角色服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysRoleService {

    /**
     * 创建角色
     *
     * @param request 角色创建请求
     * @return 角色响应
     */
    RoleResponse createRole(RoleCreateRequest request);

    /**
     * 更新角色
     *
     * @param request 角色更新请求
     * @return 角色响应
     */
    RoleResponse updateRole(RoleUpdateRequest request);

    /**
     * 删除角色
     *
     * @param id 角色ID
     */
    void deleteRole(Long id);

    /**
     * 批量删除角色
     *
     * @param ids 角色ID列表
     */
    void deleteRoles(List<Long> ids);

    /**
     * 根据ID获取角色
     *
     * @param id 角色ID
     * @return 角色响应
     */
    RoleResponse getRoleById(Long id);

    /**
     * 根据角色编码获取角色
     *
     * @param roleCode 角色编码
     * @return 角色实体
     */
    SysRole getRoleByCode(String roleCode);

    /**
     * 分页查询角色
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<RoleResponse> getRolePage(RoleQueryRequest request);

    /**
     * 获取所有角色列表
     *
     * @param request 查询请求
     * @return 角色列表
     */
    List<RoleResponse> getRoleList(RoleQueryRequest request);

    /**
     * 启用角色
     *
     * @param id 角色ID
     */
    void enableRole(Long id);

    /**
     * 禁用角色
     *
     * @param id 角色ID
     */
    void disableRole(Long id);

    /**
     * 批量启用角色
     *
     * @param ids 角色ID列表
     */
    void enableRoles(List<Long> ids);

    /**
     * 批量禁用角色
     *
     * @param ids 角色ID列表
     */
    void disableRoles(List<Long> ids);

    /**
     * 分配角色权限
     *
     * @param request 角色权限分配请求
     */
    void assignPermissions(RolePermissionAssignRequest request);

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissionIds(Long roleId);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @return 是否存在
     */
    boolean existsByRoleCode(String roleCode);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @return 是否存在
     */
    boolean existsByRoleName(String roleName);

    /**
     * 检查角色编码是否存在（排除指定角色）
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean existsByRoleCode(String roleCode, Long excludeId);

    /**
     * 检查角色名称是否存在（排除指定角色）
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean existsByRoleName(String roleName, Long excludeId);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleResponse> getUserRoles(Long userId);

    /**
     * 导出角色数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    List<RoleResponse> exportRoles(RoleQueryRequest request);
}
