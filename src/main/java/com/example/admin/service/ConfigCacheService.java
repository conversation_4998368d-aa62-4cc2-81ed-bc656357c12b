package com.example.admin.service;

import java.util.Map;

/**
 * 配置缓存服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface ConfigCacheService {

    /**
     * 获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 获取配置值（带默认值）
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 设置配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     */
    void setConfigValue(String configKey, String configValue);

    /**
     * 删除配置缓存
     *
     * @param configKey 配置键
     */
    void removeConfig(String configKey);

    /**
     * 获取所有配置
     *
     * @return 配置映射
     */
    Map<String, String> getAllConfigs();

    /**
     * 刷新所有配置缓存
     */
    void refreshAllConfigs();

    /**
     * 刷新指定配置缓存
     *
     * @param configKey 配置键
     */
    void refreshConfig(String configKey);

    /**
     * 清空所有配置缓存
     */
    void clearAllConfigs();

    /**
     * 检查配置是否存在
     *
     * @param configKey 配置键
     * @return 是否存在
     */
    boolean hasConfig(String configKey);
}
