package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 个人中心服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface ProfileService {

    /**
     * 获取个人信息
     *
     * @return 个人信息
     */
    ProfileResponse getProfile();

    /**
     * 更新个人信息
     *
     * @param request 更新请求
     * @return 更新后的个人信息
     */
    ProfileResponse updateProfile(ProfileUpdateRequest request);

    /**
     * 修改密码
     *
     * @param request 密码修改请求
     */
    void changePassword(PasswordChangeRequest request);

    /**
     * 上传头像（文件方式）
     *
     * @param file 头像文件
     * @return 头像URL
     */
    String uploadAvatar(MultipartFile file);

    /**
     * 上传头像（Base64方式）
     *
     * @param request 头像上传请求
     * @return 头像URL
     */
    String uploadAvatarBase64(AvatarUploadRequest request);

    /**
     * 获取个人设置
     *
     * @return 个人设置
     */
    SettingsResponse getSettings();

    /**
     * 更新个人设置
     *
     * @param request 设置更新请求
     * @return 更新后的个人设置
     */
    SettingsResponse updateSettings(SettingsUpdateRequest request);

    /**
     * 获取用户偏好设置
     *
     * @param userId 用户ID
     * @return 偏好设置映射
     */
    Map<String, Object> getPreferences(Long userId);

    /**
     * 设置用户偏好
     *
     * @param request 偏好设置请求
     */
    void setPreference(PreferenceSetRequest request);

    /**
     * 获取指定偏好设置
     *
     * @param userId 用户ID
     * @param preferenceKey 偏好键
     * @return 偏好值
     */
    Object getPreference(Long userId, String preferenceKey);

    /**
     * 删除偏好设置
     *
     * @param userId 用户ID
     * @param preferenceKey 偏好键
     */
    void deletePreference(Long userId, String preferenceKey);

    /**
     * 获取登录设备列表
     *
     * @return 设备列表
     */
    List<DeviceResponse> getDevices();

    /**
     * 删除登录设备
     *
     * @param deviceId 设备ID
     */
    void removeDevice(Long deviceId);

    /**
     * 设置设备信任状态
     *
     * @param deviceId 设备ID
     * @param isTrust 是否信任
     */
    void setDeviceTrust(Long deviceId, Boolean isTrust);

    /**
     * 分页获取个人操作日志
     *
     * @param request 查询请求
     * @return 日志分页列表
     */
    Page<UserLogResponse> getUserLogs(UserLogQueryRequest request);

    /**
     * 记录用户操作日志
     *
     * @param logType 日志类型
     * @param action 操作描述
     * @param module 操作模块
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseData 响应数据
     * @param status 状态
     * @param errorMessage 错误信息
     * @param executionTime 执行时间
     */
    void recordUserLog(String logType, String action, String module, String requestUrl, 
                      String requestMethod, String requestParams, String responseData, 
                      Integer status, String errorMessage, Long executionTime);

    /**
     * 更新用户登录信息
     *
     * @param loginIp 登录IP
     * @param deviceInfo 设备信息
     */
    void updateLoginInfo(String loginIp, String deviceInfo);

    /**
     * 获取密码修改历史
     *
     * @param limit 限制数量
     * @return 密码修改历史
     */
    List<PasswordHistoryResponse> getPasswordHistory(Integer limit);
}
