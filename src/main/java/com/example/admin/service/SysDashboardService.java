package com.example.admin.service;

import com.example.admin.dto.response.DashboardResponse;

import java.util.Map;

/**
 * 仪表盘统计服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface SysDashboardService {

    /**
     * 获取仪表盘统计数据
     *
     * @return 仪表盘统计数据
     */
    DashboardResponse getDashboardStatistics();

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    long getOnlineUserCount();

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    Map<String, Object> getSystemInfo();

    /**
     * 获取最近活动
     *
     * @param limit 限制数量
     * @return 最近活动列表
     */
    Map<String, Object> getRecentActivity(Integer limit);

    /**
     * 获取用户增长趋势
     *
     * @return 用户增长趋势数据
     */
    Map<String, Object> getUserGrowthTrend();

    /**
     * 获取系统负载信息
     *
     * @return 系统负载信息
     */
    Map<String, Object> getSystemLoad();
}