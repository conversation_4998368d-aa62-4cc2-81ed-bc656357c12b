package com.example.admin.service;

import com.example.admin.dto.request.LoginRequest;
import com.example.admin.dto.response.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户登出
     *
     * @param token JWT令牌
     */
    void logout(String token);

    /**
     * 刷新令牌
     *
     * @param token 当前令牌
     * @return 新的令牌
     */
    String refreshToken(String token);
}
