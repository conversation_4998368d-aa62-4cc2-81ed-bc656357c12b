package com.example.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.dto.request.LogQueryRequest;
import com.example.admin.entity.SysLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统日志服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogService extends IService<SysLog> {

    /**
     * 分页查询日志列表
     */
    IPage<SysLog> getLogPage(LogQueryRequest query);

    /**
     * 查询日志列表
     */
    List<SysLog> getLogList(LogQueryRequest query);

    /**
     * 保存日志
     */
    boolean saveLog(SysLog log);

    /**
     * 批量保存日志
     */
    boolean batchSaveLogs(List<SysLog> logs);

    /**
     * 根据时间范围查询日志数量
     */
    Long getLogCountByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String logType);

    /**
     * 根据用户ID查询日志列表
     */
    List<SysLog> getLogsByUserId(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据IP查询日志列表
     */
    List<SysLog> getLogsByIp(String ip, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询异常日志列表
     */
    List<SysLog> getExceptionLogs(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询性能日志列表
     */
    List<SysLog> getPerformanceLogs(LocalDateTime startTime, LocalDateTime endTime, Long threshold);

    /**
     * 统计日志数量（按类型）
     */
    List<Map<String, Object>> statisticsLogCountByType(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计日志数量（按用户）
     */
    List<Map<String, Object>> statisticsLogCountByUser(LocalDateTime startTime, LocalDateTime endTime, Integer limit);

    /**
     * 统计日志数量（按模块）
     */
    List<Map<String, Object>> statisticsLogCountByModule(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计日志数量（按操作）
     */
    List<Map<String, Object>> statisticsLogCountByOperation(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计日志数量（按日期）
     */
    List<Map<String, Object>> statisticsLogCountByDate(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计性能指标
     */
    Map<String, Object> statisticsPerformanceMetrics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询慢查询日志
     */
    List<SysLog> getSlowQueryLogs(Long threshold, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询错误日志
     */
    List<SysLog> getErrorLogs(LocalDateTime startTime, LocalDateTime endTime);

    
    /**
     * 删除过期日志
     */
    boolean deleteExpiredLogs(LocalDateTime expirationDate);

    /**
     * 查询最近的日志
     */
    List<SysLog> getRecentLogs(Integer limit);

    /**
     * 查询用户操作轨迹
     */
    List<SysLog> getUserOperationTrail(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询热门操作
     */
    List<Map<String, Object>> getPopularOperations(LocalDateTime startTime, LocalDateTime endTime, Integer limit);

    /**
     * 创建业务日志
     */
    void createBizLog(String username, String description, String className, String methodName,
                     String requestUri, String method, String ip, String userAgent,
                     String requestParams, String response, Long executionTime,
                     boolean success, String errorMsg);

    /**
     * 创建异常日志
     */
    void createExceptionLog(String layer, String requestUri, String method, String ip, String userAgent,
                            String exceptionClass, String exceptionMessage, String stackTrace);

    /**
     * 创建性能日志
     */
    void createPerformanceLog(String className, String methodName, String layer, long executionTime,
                              boolean success, String errorMsg);

    /**
     * 获取日志统计数据
     */
    Map<String, Object> getLogStatistics(LogQueryRequest query);

    /**
     * 获取日志统计图表数据
     */
    Map<String, Object> getLogChartStatistics(LogQueryRequest query);

    /**
     * 导出日志数据
     */
    byte[] exportLogs(LogQueryRequest query);

    /**
     * 清理日志数据
     */
    boolean cleanLogs(LocalDateTime beforeDate);

    /**
     * 获取日志配置
     */
    Map<String, Object> getLogConfig();

    /**
     * 更新日志配置
     */
    boolean updateLogConfig(Map<String, Object> config);

}