package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.dto.request.UserCreateRequest;
import com.example.admin.dto.request.UserPasswordResetRequest;
import com.example.admin.dto.request.UserQueryRequest;
import com.example.admin.dto.request.UserUpdateRequest;
import com.example.admin.dto.response.UserExportResponse;
import com.example.admin.dto.response.UserResponse;
import com.example.admin.entity.SysUser;

import java.util.List;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysUserService {

    /**
     * 创建用户
     *
     * @param request 用户创建请求
     * @return 用户响应
     */
    UserResponse createUser(UserCreateRequest request);

    /**
     * 更新用户
     *
     * @param request 用户更新请求
     * @return 用户响应
     */
    UserResponse updateUser(UserUpdateRequest request);

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    void deleteUser(Long id);

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     */
    void deleteUsers(List<Long> ids);

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户响应
     */
    UserResponse getUserById(Long id);

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    SysUser getUserByUsername(String username);

    /**
     * 分页查询用户
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<UserResponse> getUserPage(UserQueryRequest request);

    /**
     * 获取所有用户列表
     *
     * @param request 查询请求
     * @return 用户列表
     */
    List<UserResponse> getUserList(UserQueryRequest request);

    /**
     * 启用用户
     *
     * @param id 用户ID
     */
    void enableUser(Long id);

    /**
     * 禁用用户
     *
     * @param id 用户ID
     */
    void disableUser(Long id);

    /**
     * 批量启用用户
     *
     * @param ids 用户ID列表
     */
    void enableUsers(List<Long> ids);

    /**
     * 批量禁用用户
     *
     * @param ids 用户ID列表
     */
    void disableUsers(List<Long> ids);

    /**
     * 重置用户密码
     *
     * @param request 密码重置请求
     */
    void resetPassword(UserPasswordResetRequest request);

    /**
     * 重置用户密码为默认密码
     *
     * @param id 用户ID
     * @return 默认密码
     */
    String resetPasswordToDefault(Long id);

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignRoles(Long userId, List<Long> roleIds);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查用户名是否存在（排除指定用户）
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByUsername(String username, Long excludeId);

    /**
     * 检查邮箱是否存在（排除指定用户）
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByEmail(String email, Long excludeId);

    /**
     * 检查手机号是否存在（排除指定用户）
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByPhone(String phone, Long excludeId);

    /**
     * 导出用户数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    List<UserResponse> exportUsers(UserQueryRequest request);

    /**
     * 导出用户数据为Excel格式
     *
     * @param request 查询请求
     * @return 用户导出数据列表
     */
    List<UserExportResponse> exportUsersForExcel(UserQueryRequest request);
}
