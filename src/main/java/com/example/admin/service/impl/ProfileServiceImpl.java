package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.*;
import com.example.admin.entity.*;
import com.example.admin.mapper.*;
import com.example.admin.service.ConfigCacheService;
import com.example.admin.service.ProfileService;
import com.example.admin.utils.FileUploadUtils;
import com.example.admin.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Base64;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人中心服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProfileServiceImpl implements ProfileService {

    private final SysUserMapper sysUserMapper;
    private final SysUserProfileMapper sysUserProfileMapper;
    private final SysUserDeviceMapper sysUserDeviceMapper;
    private final SysUserLogMapper sysUserLogMapper;
    private final SysUserPreferenceMapper sysUserPreferenceMapper;
    private final SysUserPasswordHistoryMapper sysUserPasswordHistoryMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final FileUploadUtils fileUploadUtils;
    private final ConfigCacheService configCacheService;

    @Value("${app.security.password-history-size:5}")
    private Integer passwordHistorySize;

    @Override
    public ProfileResponse getProfile() {
        log.info("获取个人信息");
        
        Long currentUserId = getCurrentUserId();
        SysUser user = sysUserMapper.selectById(currentUserId);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 获取用户个人资料
        SysUserProfile profile = sysUserProfileMapper.selectByUserId(currentUserId);
        
        // 获取用户偏好设置
        Map<String, Object> preferences = getPreferences(currentUserId);
        
        // 获取用户设备列表
        List<DeviceResponse> devices = getDevices();
        
        return convertToProfileResponse(user, profile, preferences, devices);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProfileResponse updateProfile(ProfileUpdateRequest request) {
        log.info("更新个人信息: {}", request);
        
        Long currentUserId = getCurrentUserId();
        SysUser user = sysUserMapper.selectById(currentUserId);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 获取或创建用户个人资料
        SysUserProfile profile = sysUserProfileMapper.selectByUserId(currentUserId);
        if (profile == null) {
            profile = new SysUserProfile();
            profile.setUserId(currentUserId);
            profile.setCreateTime(LocalDateTime.now());
        }
        
        // 更新用户基本信息
        BeanUtil.copyProperties(request, user, "id", "username", "password", "email", "phone");
        sysUserMapper.updateById(user);
        
        // 更新个人资料信息
        BeanUtil.copyProperties(request, profile, "id", "userId");
        profile.setUpdateTime(LocalDateTime.now());
        if (profile.getId() == null) {
            sysUserProfileMapper.insert(profile);
        } else {
            sysUserProfileMapper.updateById(profile);
        }
        
        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("update", "更新个人信息", "profile", "/api/profile/info", 
                     "PUT", request.toString(), null, 1, null, System.currentTimeMillis() - startTime);
        
        log.info("个人信息更新成功: {}", currentUserId);
        return getProfile();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(PasswordChangeRequest request) {
        log.info("修改密码");
        
        // 验证新密码和确认密码是否一致
        if (!Objects.equals(request.getNewPassword(), request.getConfirmPassword())) {
            throw new BusinessException("新密码和确认密码不一致");
        }
        
        Long currentUserId = getCurrentUserId();
        SysUser user = sysUserMapper.selectById(currentUserId);
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BusinessException("旧密码不正确");
        }
        
        // 检查新密码是否与旧密码相同
        if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与旧密码相同");
        }
        
        // 检查密码历史记录
        String encodedNewPassword = passwordEncoder.encode(request.getNewPassword());
        if (sysUserPasswordHistoryMapper.existsPasswordInHistory(currentUserId, encodedNewPassword)) {
            throw new BusinessException("新密码不能与最近使用的密码相同");
        }
        
        // 记录密码修改历史
        SysUserPasswordHistory passwordHistory = new SysUserPasswordHistory();
        passwordHistory.setUserId(currentUserId);
        passwordHistory.setOldPassword(user.getPassword());
        passwordHistory.setNewPassword(encodedNewPassword);
        passwordHistory.setChangeType("manual");
        passwordHistory.setChangeReason("用户自行修改");
        
        // 获取当前请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest httpRequest = attributes.getRequest();
            passwordHistory.setIpAddress(getClientIpAddress(httpRequest));
            passwordHistory.setDeviceId(httpRequest.getHeader("Device-Id"));
        }
        
        sysUserPasswordHistoryMapper.insert(passwordHistory);
        
        // 更新密码
        user.setPassword(encodedNewPassword);
        user.setPasswordUpdateTime(LocalDateTime.now());
        sysUserMapper.updateById(user);
        
        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("update", "修改密码", "profile", "/api/profile/password", 
                     "PUT", "密码修改", null, 1, null, System.currentTimeMillis() - startTime);
        
        log.info("密码修改成功: {}", currentUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadAvatar(MultipartFile file) {
        log.info("上传头像: {}", file.getOriginalFilename());

        try {
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw new BusinessException("只支持图片文件");
            }
            
            // 验证文件大小
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (file.getSize() > maxSize) {
                throw new BusinessException("文件大小不能超过5MB");
            }

            // 上传头像文件
            String relativePath = fileUploadUtils.uploadAvatar(file);
            String avatarUrl = fileUploadUtils.getFileUrl(relativePath);

            // 更新用户头像
            Long currentUserId = getCurrentUserId();

            // 获取旧头像，用于删除
            SysUser oldUser = sysUserMapper.selectById(currentUserId);
            String oldAvatar = oldUser != null ? oldUser.getAvatar() : null;

            // 更新新头像
            SysUser user = new SysUser();
            user.setId(currentUserId);
            user.setAvatar(avatarUrl);
            sysUserMapper.updateById(user);

            // 删除旧头像文件（如果存在且不是默认头像）
            if (StrUtil.isNotBlank(oldAvatar) && !oldAvatar.contains("default")) {
                // 从URL中提取相对路径
                String oldRelativePath = oldAvatar.replace("/api/files/", "");
                fileUploadUtils.deleteFile(oldRelativePath);
            }

            // 记录操作日志
            long startTime = System.currentTimeMillis();
            recordUserLog("upload", "上传头像", "profile", "/api/profile/avatar",
                         file.getOriginalFilename(), "POST", avatarUrl, 1, null, System.currentTimeMillis() - startTime);

            log.info("头像上传成功: {}", avatarUrl);
            return avatarUrl;

        } catch (Exception e) {
            log.error("头像上传失败", e);

            // 记录操作日志
            long startTime = System.currentTimeMillis();
            recordUserLog("upload", "上传头像", "profile", "/api/profile/avatar",
                         file.getOriginalFilename(), "POST", null, 0, e.getMessage(), System.currentTimeMillis() - startTime);

            throw new BusinessException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadAvatarBase64(AvatarUploadRequest request) {
        log.info("上传头像(Base64): {}", request.getFileName());

        try {
            // 验证Base64数据
            if (StrUtil.isBlank(request.getAvatarBase64())) {
                throw new BusinessException("头像数据不能为空");
            }
            
            // 解码Base64数据
            byte[] imageData = Base64.getDecoder().decode(request.getAvatarBase64().split(",")[1]);
            
            // 验证文件大小
            long maxSize = 5 * 1024 * 1024; // 5MB
            if (imageData.length > maxSize) {
                throw new BusinessException("文件大小不能超过5MB");
            }

            // 生成文件名
            String fileName = request.getFileName();
            if (StrUtil.isBlank(fileName)) {
                fileName = "avatar_" + System.currentTimeMillis() + ".jpg";
            }
            
            // 上传头像文件
            String relativePath = fileUploadUtils.uploadAvatarBase64(imageData, fileName);
            String avatarUrl = fileUploadUtils.getFileUrl(relativePath);

            // 更新用户头像
            Long currentUserId = getCurrentUserId();

            // 获取旧头像，用于删除
            SysUser oldUser = sysUserMapper.selectById(currentUserId);
            String oldAvatar = oldUser != null ? oldUser.getAvatar() : null;

            // 更新新头像
            SysUser user = new SysUser();
            user.setId(currentUserId);
            user.setAvatar(avatarUrl);
            sysUserMapper.updateById(user);

            // 删除旧头像文件（如果存在且不是默认头像）
            if (StrUtil.isNotBlank(oldAvatar) && !oldAvatar.contains("default")) {
                // 从URL中提取相对路径
                String oldRelativePath = oldAvatar.replace("/api/files/", "");
                fileUploadUtils.deleteFile(oldRelativePath);
            }

            // 记录操作日志
            long startTime = System.currentTimeMillis();
            recordUserLog("upload", "上传头像(Base64)", "profile", "/api/profile/avatar/base64",
                         fileName, "POST", avatarUrl, 1, null, System.currentTimeMillis() - startTime);

            log.info("头像上传成功(Base64): {}", avatarUrl);
            return avatarUrl;

        } catch (Exception e) {
            log.error("头像上传失败(Base64)", e);

            // 记录操作日志
            long startTime = System.currentTimeMillis();
            recordUserLog("upload", "上传头像(Base64)", "profile", "/api/profile/avatar/base64",
                         request.getFileName(), "POST", null, 0, e.getMessage(), System.currentTimeMillis() - startTime);

            throw new BusinessException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    public SettingsResponse getSettings() {
        log.info("获取个人设置");

        Long currentUserId = getCurrentUserId();
        SysUserProfile profile = sysUserProfileMapper.selectByUserId(currentUserId);

        if (profile == null) {
            // 如果没有配置，创建默认配置
            profile = createDefaultProfile(currentUserId);
        }

        return convertToSettingsResponse(profile);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SettingsResponse updateSettings(SettingsUpdateRequest request) {
        log.info("更新个人设置: {}", request);

        Long currentUserId = getCurrentUserId();
        SysUserProfile profile = sysUserProfileMapper.selectByUserId(currentUserId);

        if (profile == null) {
            // 如果没有配置，创建新配置
            profile = new SysUserProfile();
            profile.setUserId(currentUserId);
            BeanUtil.copyProperties(request, profile);
            sysUserProfileMapper.insert(profile);
        } else {
            // 更新现有配置
            BeanUtil.copyProperties(request, profile, "id", "userId");
            sysUserProfileMapper.updateById(profile);
        }

        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("update", "更新个人设置", "profile", "/api/profile/settings",
                     request.toString(), "PUT", "SUCCESS", 1, null, System.currentTimeMillis() - startTime);

        log.info("个人设置更新成功: {}", currentUserId);
        return convertToSettingsResponse(profile);
    }

    @Override
    public Map<String, Object> getPreferences(Long userId) {
        log.info("获取用户偏好设置: {}", userId);
        
        List<SysUserPreference> preferences = sysUserPreferenceMapper.selectByUserId(userId);
        Map<String, Object> preferenceMap = new HashMap<>();
        
        for (SysUserPreference preference : preferences) {
            Object value = convertPreferenceValue(preference.getPreferenceValue(), preference.getDataType());
            preferenceMap.put(preference.getPreferenceKey(), value);
        }
        
        return preferenceMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPreference(PreferenceSetRequest request) {
        log.info("设置用户偏好: {}", request);
        
        Long currentUserId = getCurrentUserId();
        
        // 检查偏好设置是否存在
        SysUserPreference existingPreference = sysUserPreferenceMapper.selectByUserIdAndKey(currentUserId, request.getPreferenceKey());
        
        if (existingPreference != null) {
            // 更新现有偏好设置
            existingPreference.setPreferenceValue(request.getPreferenceValue());
            existingPreference.setDataType(request.getDataType());
            existingPreference.setDescription(request.getDescription());
            existingPreference.setUpdateTime(LocalDateTime.now());
            sysUserPreferenceMapper.updateById(existingPreference);
        } else {
            // 创建新偏好设置
            SysUserPreference preference = new SysUserPreference();
            preference.setUserId(currentUserId);
            preference.setPreferenceKey(request.getPreferenceKey());
            preference.setPreferenceValue(request.getPreferenceValue());
            preference.setDataType(request.getDataType());
            preference.setDescription(request.getDescription());
            preference.setCreateTime(LocalDateTime.now());
            sysUserPreferenceMapper.insert(preference);
        }
        
        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("update", "设置用户偏好", "profile", "/api/profile/preferences",
                     request.toString(), "POST", "SUCCESS", 1, null, System.currentTimeMillis() - startTime);
    }

    @Override
    public Object getPreference(Long userId, String preferenceKey) {
        log.info("获取指定偏好设置: {} - {}", userId, preferenceKey);
        
        SysUserPreference preference = sysUserPreferenceMapper.selectByUserIdAndKey(userId, preferenceKey);
        if (preference == null) {
            return null;
        }
        
        return convertPreferenceValue(preference.getPreferenceValue(), preference.getDataType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePreference(Long userId, String preferenceKey) {
        log.info("删除偏好设置: {} - {}", userId, preferenceKey);
        
        sysUserPreferenceMapper.deleteByUserIdAndKey(userId, preferenceKey);
        
        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("delete", "删除偏好设置", "profile", "/api/profile/preferences/" + preferenceKey,
                     preferenceKey, "DELETE", "SUCCESS", 1, null, System.currentTimeMillis() - startTime);
    }

    @Override
    public List<DeviceResponse> getDevices() {
        log.info("获取登录设备列表");

        Long currentUserId = getCurrentUserId();
        List<SysUserDevice> devices = sysUserDeviceMapper.selectByUserId(currentUserId);

        return devices.stream()
                .map(this::convertToDeviceResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDevice(Long deviceId) {
        log.info("删除登录设备: {}", deviceId);

        Long currentUserId = getCurrentUserId();
        SysUserDevice device = sysUserDeviceMapper.selectById(deviceId);

        if (device == null || !Objects.equals(device.getUserId(), currentUserId)) {
            throw new BusinessException("设备不存在或无权限操作");
        }

        // 检查是否是当前设备
        String currentDeviceId = getCurrentDeviceId();
        if (Objects.equals(device.getDeviceId(), currentDeviceId)) {
            throw new BusinessException("不能删除当前登录设备");
        }

        sysUserDeviceMapper.deleteById(deviceId);

        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("delete", "删除登录设备", "profile", "/api/profile/devices/" + deviceId,
                     device.getDeviceName(), "DELETE", "SUCCESS", 1, null, System.currentTimeMillis() - startTime);

        log.info("登录设备删除成功: {}", deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDeviceTrust(Long deviceId, Boolean isTrust) {
        log.info("设置设备信任状态: {} - {}", deviceId, isTrust);

        Long currentUserId = getCurrentUserId();
        SysUserDevice device = sysUserDeviceMapper.selectById(deviceId);

        if (device == null || !Objects.equals(device.getUserId(), currentUserId)) {
            throw new BusinessException("设备不存在或无权限操作");
        }

        device.setIsTrusted(isTrust ? 1 : 0);
        device.setUpdateTime(LocalDateTime.now());
        sysUserDeviceMapper.updateById(device);

        // 记录操作日志
        long startTime = System.currentTimeMillis();
        recordUserLog("update", "设置设备信任状态", "profile", "/api/profile/devices/" + deviceId + "/trust",
                     device.getDeviceName(), "PUT", isTrust.toString(), 1, null, System.currentTimeMillis() - startTime);

        log.info("设备信任状态设置成功: {} - {}", deviceId, isTrust);
    }

    @Override
    public Page<UserLogResponse> getUserLogs(UserLogQueryRequest request) {
        log.info("分页获取个人操作日志: {}", request);

        Long currentUserId = getCurrentUserId();
        Page<SysUserLog> page = new Page<>(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<SysUserLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserLog::getUserId, currentUserId)
               .eq(StrUtil.isNotBlank(request.getLogType()), SysUserLog::getLogType, request.getLogType())
               .ge(request.getStartTime() != null, SysUserLog::getCreateTime, request.getStartTime())
               .le(request.getEndTime() != null, SysUserLog::getCreateTime, request.getEndTime())
               .orderByDesc(SysUserLog::getCreateTime);

        Page<SysUserLog> logPage = sysUserLogMapper.selectPage(page, wrapper);

        Page<UserLogResponse> responsePage = new Page<>();
        BeanUtil.copyProperties(logPage, responsePage, "records");

        List<UserLogResponse> records = logPage.getRecords().stream()
                .map(this::convertToUserLogResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(records);

        return responsePage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordUserLog(String logType, String action, String module, String requestUrl, 
                             String requestMethod, String requestParams, String responseData, 
                             Integer status, String errorMessage, Long executionTime) {
        try {
            Long currentUserId = getCurrentUserId();

            SysUserLog userLog = new SysUserLog();
            userLog.setUserId(currentUserId);
            userLog.setLogType(logType);
            userLog.setAction(action);
            userLog.setModule(module);
            userLog.setRequestUrl(requestUrl);
            userLog.setRequestMethod(requestMethod);
            userLog.setRequestParams(requestParams);
            userLog.setResponseData(responseData);
            userLog.setStatus(status);
            userLog.setErrorMessage(errorMessage);
            userLog.setExecutionTime(executionTime);

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                userLog.setIpAddress(getClientIpAddress(request));
                userLog.setUserAgent(request.getHeader("User-Agent"));
                userLog.setDeviceId(getCurrentDeviceId());
            }

            sysUserLogMapper.insert(userLog);
        } catch (Exception e) {
            log.error("记录用户操作日志失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLoginInfo(String loginIp, String deviceInfo) {
        try {
            Long currentUserId = getCurrentUserId();
            
            // 更新用户个人资料中的登录信息
            SysUserProfile profile = sysUserProfileMapper.selectByUserId(currentUserId);
            if (profile != null) {
                profile.setLastLoginTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                profile.setLastLoginIp(loginIp);
                profile.setLastLoginDevice(deviceInfo);
                profile.setLoginCount(profile.getLoginCount() + 1);
                sysUserProfileMapper.updateById(profile);
            } else {
                // 创建新的个人资料
                profile = new SysUserProfile();
                profile.setUserId(currentUserId);
                profile.setLastLoginTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                profile.setLastLoginIp(loginIp);
                profile.setLastLoginDevice(deviceInfo);
                profile.setLoginCount(1);
                sysUserProfileMapper.insert(profile);
            }
            
            // 记录登录日志
            long startTime = System.currentTimeMillis();
            recordUserLog("login", "用户登录", "auth", "/api/auth/login",
                         "POST", "用户登录", "SUCCESS", 1, null, System.currentTimeMillis() - startTime);
            
        } catch (Exception e) {
            log.error("更新登录信息失败", e);
        }
    }

    @Override
    public List<PasswordHistoryResponse> getPasswordHistory(Integer limit) {
        log.info("获取密码修改历史: {}", limit);
        
        Long currentUserId = getCurrentUserId();
        List<SysUserPasswordHistory> histories = sysUserPasswordHistoryMapper.selectRecentByUserId(currentUserId, limit);
        
        return histories.stream()
                .map(this::convertToPasswordHistoryResponse)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        if (StrUtil.isBlank(username) || "anonymousUser".equals(username)) {
            throw new BusinessException("用户未登录");
        }
        
        SysUser user = sysUserMapper.selectOne(
            new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username)
        );
        
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        return user.getId();
    }

    /**
     * 获取当前设备ID
     */
    private String getCurrentDeviceId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest().getHeader("Device-Id");
        }
        return null;
    }

    /**
     * 创建默认用户配置
     */
    private SysUserProfile createDefaultProfile(Long userId) {
        SysUserProfile profile = new SysUserProfile();
        profile.setUserId(userId);
        profile.setTimezone("Asia/Shanghai");
        profile.setLanguage("zh-CN");
        profile.setTheme("light");
        profile.setEmailNotifications(1);
        profile.setSmsNotifications(1);
        profile.setCreateTime(LocalDateTime.now());
        profile.setUpdateTime(LocalDateTime.now());

        sysUserProfileMapper.insert(profile);
        return profile;
    }

    /**
     * 转换为个人信息响应对象
     */
    private ProfileResponse convertToProfileResponse(SysUser user, SysUserProfile profile, 
                                                    Map<String, Object> preferences, List<DeviceResponse> devices) {
        ProfileResponse response = new ProfileResponse();
        BeanUtil.copyProperties(user, response);

        // 设置个人资料信息
        if (profile != null) {
            response.setNickname(profile.getNickname());
            response.setBio(profile.getBio());
            response.setBirthday(profile.getBirthday());
            response.setGender(profile.getGender());
            response.setLocation(profile.getLocation());
            response.setWebsite(profile.getWebsite());
            response.setGithub(profile.getGithub());
            response.setTwitter(profile.getTwitter());
            response.setLinkedin(profile.getLinkedin());
            response.setTimezone(profile.getTimezone());
            response.setLanguage(profile.getLanguage());
            response.setTheme(profile.getTheme());
            response.setEmailNotifications(profile.getEmailNotifications());
            response.setSmsNotifications(profile.getSmsNotifications());
            // 转换String类型的lastLoginTime为LocalDateTime
            if (profile.getLastLoginTime() != null) {
                response.setLastLoginTime(LocalDateTime.parse(profile.getLastLoginTime(), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            }
            response.setLastLoginIp(profile.getLastLoginIp());
            response.setLastLoginDevice(profile.getLastLoginDevice());
            response.setLoginCount(profile.getLoginCount());
        }

        // 设置性别描述
        if (response.getGender() != null) {
            switch (response.getGender()) {
                case 0:
                    response.setGenderDesc("女");
                    break;
                case 1:
                    response.setGenderDesc("男");
                    break;
                case 2:
                    response.setGenderDesc("未知");
                    break;
                default:
                    response.setGenderDesc("未知");
                    break;
            }
        }

        // 设置偏好设置
        response.setPreferences(preferences);
        
        // 设置设备列表
        response.setDevices(devices);

        return response;
    }

    /**
     * 转换为个人设置响应对象
     */
    private SettingsResponse convertToSettingsResponse(SysUserProfile profile) {
        SettingsResponse response = new SettingsResponse();
        BeanUtil.copyProperties(profile, response);
        return response;
    }

    /**
     * 转换为设备响应对象
     */
    private DeviceResponse convertToDeviceResponse(SysUserDevice device) {
        DeviceResponse response = new DeviceResponse();
        BeanUtil.copyProperties(device, response);

        // 设置状态描述
        if (device.getStatus() != null) {
            response.setStatusDesc(device.getStatus() == 1 ? "在线" : "离线");
        }

        return response;
    }

    /**
     * 转换为用户日志响应对象
     */
    private UserLogResponse convertToUserLogResponse(SysUserLog userLog) {
        UserLogResponse response = new UserLogResponse();
        BeanUtil.copyProperties(userLog, response);
        return response;
    }

    /**
     * 转换为密码历史响应对象
     */
    private PasswordHistoryResponse convertToPasswordHistoryResponse(SysUserPasswordHistory history) {
        PasswordHistoryResponse response = new PasswordHistoryResponse();
        BeanUtil.copyProperties(history, response);
        
        // 设置修改类型描述
        switch (history.getChangeType()) {
            case "manual":
                response.setChangeTypeDesc("手动修改");
                break;
            case "reset":
                response.setChangeTypeDesc("重置密码");
                break;
            case "force":
                response.setChangeTypeDesc("强制修改");
                break;
            default:
                response.setChangeTypeDesc("未知");
                break;
        }
        
        return response;
    }

    /**
     * 转换偏好值类型
     */
    private Object convertPreferenceValue(String value, String dataType) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        
        switch (dataType) {
            case "STRING":
                return value;
            case "NUMBER":
                try {
                    return Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    return value;
                }
            case "BOOLEAN":
                return "true".equalsIgnoreCase(value) || "1".equals(value);
            case "JSON":
                return value; // 前端自行解析
            default:
                return value;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}