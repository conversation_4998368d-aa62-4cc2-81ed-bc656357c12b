package com.example.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.admin.entity.SysUser;
import com.example.admin.mapper.SysUserMapper;
import com.example.admin.service.SysPermissionService;
import com.example.admin.dto.response.PermissionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Spring Security用户详情服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final SysUserMapper sysUserMapper;
    private final SysPermissionService sysPermissionService;

    /**
     * 根据用户名加载用户详情
     *
     * @param username 用户名
     * @return 用户详情
     * @throws UsernameNotFoundException 用户不存在异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("正在加载用户详情，用户名: {}", username);

        // 查询用户信息
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getUsername, username)
                        .eq(SysUser::getDeleted, 0)
        );

        if (sysUser == null) {
            log.error("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        // 检查用户状态
        if (sysUser.getStatus() == null || sysUser.getStatus() == 0) {
            log.error("用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        // 获取用户权限
        Collection<? extends GrantedAuthority> authorities = getUserAuthorities(sysUser.getId());

        // 构建UserDetails对象
        return User.builder()
                .username(sysUser.getUsername())
                .password(sysUser.getPassword())
                .authorities(authorities)
                .accountExpired(false)
                .accountLocked(false)
                .credentialsExpired(false)
                .disabled(sysUser.getStatus() == 0)
                .build();
    }

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    private Collection<? extends GrantedAuthority> getUserAuthorities(Long userId) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        try {
            // 获取用户的所有权限
            List<PermissionResponse> permissions = sysPermissionService.getPermissionsByUserId(userId);
            log.debug("用户ID: {} 获取到权限数量: {}", userId, permissions.size());

            // 将权限转换为Spring Security的GrantedAuthority
            for (PermissionResponse permission : permissions) {
                if (permission.getPermissionCode() != null && !permission.getPermissionCode().trim().isEmpty()) {
                    authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
                    log.debug("添加权限: {}", permission.getPermissionCode());
                }
            }

            // 如果没有权限，添加一个默认权限
            if (authorities.isEmpty()) {
                log.warn("用户ID: {} 没有任何权限，添加默认权限", userId);
                authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            }

        } catch (Exception e) {
            log.error("获取用户权限失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            // 发生异常时，添加默认权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }

        log.debug("用户ID: {} 的最终权限列表: {}", userId, authorities);
        return authorities;
    }
}
