package com.example.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.dto.request.LogQueryRequest;
import com.example.admin.entity.SysLog;
import com.example.admin.mapper.SysLogMapper;
import com.example.admin.service.SysLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统日志服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

    private final SysLogMapper sysLogMapper;

    @Override
    public IPage<SysLog> getLogPage(LogQueryRequest query) {
        Page<SysLog> page = new Page<>(query.getPageNum(), query.getPageSize());
        return sysLogMapper.selectLogPage(page, query);
    }

    @Override
    public List<SysLog> getLogList(LogQueryRequest query) {
        return sysLogMapper.selectLogList(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLog(SysLog log) {
        log.setCreateTime(LocalDateTime.now());
        log.setUpdateTime(LocalDateTime.now());
        return save(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveLogs(List<SysLog> logs) {
        if (logs == null || logs.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        logs.forEach(log -> {
            log.setCreateTime(now);
            log.setUpdateTime(now);
        });
        
        return saveBatch(logs);
    }

    @Override
    public Long getLogCountByTimeRange(LocalDateTime startTime, LocalDateTime endTime, String logType) {
        return sysLogMapper.selectLogCountByTimeRange(startTime, endTime, logType);
    }

    @Override
    public List<SysLog> getLogsByUserId(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectLogsByUserId(userId, startTime, endTime);
    }

    @Override
    public List<SysLog> getLogsByIp(String ip, LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectLogsByIp(ip, startTime, endTime);
    }

    @Override
    public List<SysLog> getExceptionLogs(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectExceptionLogs(startTime, endTime);
    }

    @Override
    public List<SysLog> getPerformanceLogs(LocalDateTime startTime, LocalDateTime endTime, Long threshold) {
        return sysLogMapper.selectPerformanceLogs(startTime, endTime, threshold);
    }

    @Override
    public List<Map<String, Object>> statisticsLogCountByType(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.statisticsLogCountByType(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> statisticsLogCountByUser(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return sysLogMapper.statisticsLogCountByUser(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> statisticsLogCountByModule(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.statisticsLogCountByModule(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> statisticsLogCountByOperation(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.statisticsLogCountByOperation(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> statisticsLogCountByDate(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.statisticsLogCountByDate(startTime, endTime);
    }

    @Override
    public Map<String, Object> statisticsPerformanceMetrics(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.statisticsPerformanceMetrics(startTime, endTime);
    }

    @Override
    public List<SysLog> getSlowQueryLogs(Long threshold, LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectSlowQueryLogs(threshold, startTime, endTime);
    }

    @Override
    public List<SysLog> getErrorLogs(LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectErrorLogs(startTime, endTime);
    }

  
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteExpiredLogs(LocalDateTime expirationDate) {
        return sysLogMapper.deleteExpiredLogs(expirationDate) > 0;
    }

    @Override
    public List<SysLog> getRecentLogs(Integer limit) {
        return sysLogMapper.selectRecentLogs(limit);
    }

    @Override
    public List<SysLog> getUserOperationTrail(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return sysLogMapper.selectUserOperationTrail(userId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getPopularOperations(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return sysLogMapper.selectPopularOperations(startTime, endTime, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBizLog(String username, String description, String className, String methodName,
                             String requestUri, String method, String ip, String userAgent,
                             String requestParams, String response, Long executionTime,
                             boolean success, String errorMsg) {
        SysLog sysLog = new SysLog();
        sysLog.setLogType("BIZ_LOG");
        sysLog.setUsername(username);
        sysLog.setDescription(description);
        sysLog.setClassName(className);
        sysLog.setMethodName(methodName);
        sysLog.setRequestUri(requestUri);
        sysLog.setMethod(method);
        sysLog.setIp(ip);
        sysLog.setUserAgent(userAgent);
        sysLog.setRequestParams(requestParams);
        sysLog.setResponse(response);
        sysLog.setExecutionTime(executionTime);
        sysLog.setSuccess(success);
        sysLog.setErrorMsg(errorMsg);
        sysLog.setLevel("INFO");
        
        saveLog(sysLog);
        log.info("创建业务日志: {}", description);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createExceptionLog(String layer, String requestUri, String method, String ip, String userAgent,
                                  String exceptionClass, String exceptionMessage, String stackTrace) {
        SysLog sysLog = new SysLog();
        sysLog.setLogType("EXCEPTION_LOG");
        sysLog.setDescription("系统异常");
        sysLog.setRequestUri(requestUri);
        sysLog.setMethod(method);
        sysLog.setIp(ip);
        sysLog.setUserAgent(userAgent);
        sysLog.setExceptionType(exceptionClass);
        sysLog.setErrorMsg(exceptionMessage);
        sysLog.setStackTrace(stackTrace);
        sysLog.setLevel("ERROR");
        sysLog.setSuccess(false);
        
        saveLog(sysLog);
        log.error("创建异常日志: {} - {}", exceptionClass, exceptionMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPerformanceLog(String className, String methodName, String layer, long executionTime,
                                     boolean success, String errorMsg) {
        SysLog sysLog = new SysLog();
        sysLog.setLogType("PERFORMANCE_LOG");
        sysLog.setClassName(className);
        sysLog.setMethodName(methodName);
        sysLog.setDescription("性能监控");
        sysLog.setExecutionTime(executionTime);
        sysLog.setSuccess(success);
        sysLog.setErrorMsg(errorMsg);
        sysLog.setLevel("INFO");
        
        saveLog(sysLog);
        log.debug("创建性能日志: {}.{} - {}ms", className, methodName, executionTime);
    }

    @Override
    public Map<String, Object> getLogStatistics(LogQueryRequest query) {
        Map<String, Object> statistics = new HashMap<>();
        
        LocalDateTime endTime = query.getEndTime() != null ? query.getEndTime() : LocalDateTime.now();
        LocalDateTime startTime = query.getStartTime() != null ? query.getStartTime() : endTime.minusDays(30);
        
        // 基础统计
        statistics.put("totalLogCount", getLogCountByTimeRange(startTime, endTime, null));
        statistics.put("exceptionLogCount", getLogCountByTimeRange(startTime, endTime, "EXCEPTION_LOG"));
        statistics.put("performanceLogCount", getLogCountByTimeRange(startTime, endTime, "PERFORMANCE_LOG"));
        
        // 类型统计
        statistics.put("logTypeStatistics", statisticsLogCountByType(startTime, endTime));
        
        // 用户统计
        statistics.put("userStatistics", statisticsLogCountByUser(startTime, endTime, 10));
        
        // 模块统计
        statistics.put("moduleStatistics", statisticsLogCountByModule(startTime, endTime));
        
        // 操作统计
        statistics.put("operationStatistics", statisticsLogCountByOperation(startTime, endTime));
        
        // 性能指标
        statistics.put("performanceMetrics", statisticsPerformanceMetrics(startTime, endTime));
        
        return statistics;
    }

    @Override
    public Map<String, Object> getLogChartStatistics(LogQueryRequest query) {
        Map<String, Object> chartData = new HashMap<>();
        
        LocalDateTime endTime = query.getEndTime() != null ? query.getEndTime() : LocalDateTime.now();
        LocalDateTime startTime = query.getStartTime() != null ? query.getStartTime() : endTime.minusDays(7);
        
        // 日期趋势图
        chartData.put("dateTrend", statisticsLogCountByDate(startTime, endTime));
        
        // 模块分布图
        chartData.put("moduleDistribution", statisticsLogCountByModule(startTime, endTime));
        
        // 用户活跃度图
        chartData.put("userActivity", statisticsLogCountByUser(startTime, endTime, 20));
        
        // 操作频率图
        chartData.put("operationFrequency", statisticsLogCountByOperation(startTime, endTime));
        
        // 性能趋势图
        chartData.put("performanceTrend", getPerformanceTrendData(startTime, endTime));
        
        return chartData;
    }

    @Override
    public byte[] exportLogs(LogQueryRequest query) {
        // TODO: 实现日志导出功能
        // 这里可以使用Apache POI或EasyExcel等库实现Excel导出
        log.warn("日志导出功能尚未实现");
        return new byte[0];
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cleanLogs(LocalDateTime beforeDate) {
        log.info("开始清理{}之前的日志数据", beforeDate);
        boolean result = deleteExpiredLogs(beforeDate);
        log.info("日志清理完成，结果: {}", result);
        return result;
    }

    @Override
    public Map<String, Object> getLogConfig() {
        // TODO: 从数据库获取日志配置
        Map<String, Object> config = new HashMap<>();
        config.put("logRetentionDays", 30);
        config.put("enablePerformanceAlert", true);
        config.put("performanceAlertThreshold", 2000);
        config.put("enableExceptionAlert", true);
        config.put("enableRealTimePush", true);
        return config;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLogConfig(Map<String, Object> config) {
        // TODO: 更新日志配置到数据库
        log.info("更新日志配置: {}", config);
        return true;
    }

    /**
     * 获取性能趋势数据
     */
    private List<Map<String, Object>> getPerformanceTrendData(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里可以根据需求实现性能趋势数据的计算
        // 暂时返回空列表
        return List.of();
    }
    
}