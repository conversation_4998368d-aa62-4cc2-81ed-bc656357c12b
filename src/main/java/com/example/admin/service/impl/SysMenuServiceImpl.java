package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.example.admin.common.constant.CommonConstant;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.MenuCreateRequest;
import com.example.admin.dto.request.MenuQueryRequest;
import com.example.admin.dto.request.MenuUpdateRequest;
import com.example.admin.dto.response.MenuResponse;
import com.example.admin.dto.response.MenuTreeResponse;
import com.example.admin.entity.SysMenu;
import com.example.admin.mapper.SysMenuMapper;
import com.example.admin.service.SysMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统菜单服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl implements SysMenuService {

    private final SysMenuMapper sysMenuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MenuResponse createMenu(MenuCreateRequest request) {
        log.info("创建菜单: {}", request.getMenuName());

        // 检查菜单名称是否已存在
        if (existsByMenuName(request.getMenuName())) {
            throw new BusinessException("菜单名称已存在");
        }

        // 检查路由路径是否已存在（如果不为空）
        if (StrUtil.isNotBlank(request.getPath()) && existsByPath(request.getPath())) {
            throw new BusinessException("路由路径已存在");
        }

        // 检查权限标识是否已存在（如果不为空）
        if (StrUtil.isNotBlank(request.getPerms()) && existsByPerms(request.getPerms())) {
            throw new BusinessException("权限标识已存在");
        }

        // 检查父菜单是否存在
        if (request.getParentId() != null && request.getParentId() > 0) {
            SysMenu parentMenu = sysMenuMapper.selectById(request.getParentId());
            if (parentMenu == null || parentMenu.getDeleted() == 1) {
                throw new BusinessException("父菜单不存在");
            }
        }

        // 创建菜单实体
        SysMenu menu = new SysMenu();
        BeanUtil.copyProperties(request, menu);

        // 设置默认值
        if (menu.getParentId() == null) {
            menu.setParentId(0L);
        }
        if (menu.getStatus() == null) {
            menu.setStatus(CommonConstant.STATUS_ENABLED);
        }
        if (menu.getSortOrder() == null) {
            menu.setSortOrder(0);
        }
        if (menu.getIsSystem() == null) {
            menu.setIsSystem(CommonConstant.NO);
        }
        if (menu.getVisible() == null) {
            menu.setVisible(1);
        }
        if (menu.getIsFrame() == null) {
            menu.setIsFrame(0);
        }
        if (menu.getIsCache() == null) {
            menu.setIsCache(0);
        }

        // 保存菜单
        sysMenuMapper.insert(menu);

        log.info("菜单创建成功: {}", request.getMenuName());
        return getMenuById(menu.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MenuResponse updateMenu(MenuUpdateRequest request) {
        log.info("更新菜单: {}", request.getId());

        // 检查菜单是否存在
        SysMenu existingMenu = sysMenuMapper.selectById(request.getId());
        if (existingMenu == null || existingMenu.getDeleted() == 1) {
            throw new BusinessException("菜单不存在");
        }

        // 检查是否为系统内置菜单
        if (existingMenu.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置菜单不允许修改");
        }

        // 检查菜单名称是否已存在（排除当前菜单）
        if (StrUtil.isNotBlank(request.getMenuName()) && 
            existsByMenuName(request.getMenuName(), request.getId())) {
            throw new BusinessException("菜单名称已存在");
        }

        // 检查路由路径是否已存在（排除当前菜单）
        if (StrUtil.isNotBlank(request.getPath()) && 
            existsByPath(request.getPath(), request.getId())) {
            throw new BusinessException("路由路径已存在");
        }

        // 检查权限标识是否已存在（排除当前菜单）
        if (StrUtil.isNotBlank(request.getPerms()) && 
            existsByPerms(request.getPerms(), request.getId())) {
            throw new BusinessException("权限标识已存在");
        }

        // 检查父菜单是否存在
        if (request.getParentId() != null && request.getParentId() > 0) {
            SysMenu parentMenu = sysMenuMapper.selectById(request.getParentId());
            if (parentMenu == null || parentMenu.getDeleted() == 1) {
                throw new BusinessException("父菜单不存在");
            }
            
            // 检查是否设置自己为父菜单
            if (request.getParentId().equals(request.getId())) {
                throw new BusinessException("不能设置自己为父菜单");
            }
        }

        // 更新菜单实体
        SysMenu menu = new SysMenu();
        BeanUtil.copyProperties(request, menu);
        sysMenuMapper.updateById(menu);

        log.info("菜单更新成功: {}", request.getId());
        return getMenuById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(Long id) {
        log.info("删除菜单: {}", id);

        // 检查菜单是否存在
        SysMenu menu = sysMenuMapper.selectById(id);
        if (menu == null || menu.getDeleted() == 1) {
            throw new BusinessException("菜单不存在");
        }

        // 检查是否为系统内置菜单
        if (menu.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置菜单不允许删除");
        }

        // 检查是否有子菜单
        if (hasChildren(id)) {
            throw new BusinessException("存在子菜单，不允许删除");
        }

        // 删除菜单
        sysMenuMapper.deleteById(id);

        log.info("菜单删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteMenus(List<Long> ids) {
        log.info("批量删除菜单: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("菜单ID列表不能为空");
        }

        for (Long id : ids) {
            deleteMenu(id);
        }

        log.info("菜单批量删除成功: {}", ids);
    }

    @Override
    public MenuResponse getMenuById(Long id) {
        log.info("获取菜单: {}", id);

        SysMenu menu = sysMenuMapper.selectById(id);
        if (menu == null || menu.getDeleted() == 1) {
            throw new BusinessException("菜单不存在");
        }

        return convertToMenuResponse(menu);
    }

    @Override
    public List<MenuResponse> getMenuList(MenuQueryRequest request) {
        log.info("查询菜单列表: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysMenu> queryWrapper = buildQueryWrapper(request);

        // 查询菜单列表
        List<SysMenu> menus = sysMenuMapper.selectList(queryWrapper);

        // 转换为响应对象
        List<MenuResponse> responseList = menus.stream()
                .map(this::convertToMenuResponse)
                .collect(Collectors.toList());

        // 如果需要树形结构，则构建树形结构
        if (request.getTreeStructure() != null && request.getTreeStructure()) {
            return buildMenuTree(responseList);
        }

        return responseList;
    }

    @Override
    public List<MenuResponse> getMenuTree() {
        log.info("获取菜单树形结构");

        // 查询所有启用的菜单
        List<SysMenu> menus = sysMenuMapper.selectList(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getStatus, CommonConstant.STATUS_ENABLED)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)
                .orderByAsc(SysMenu::getSortOrder));

        // 转换为响应对象
        List<MenuResponse> responseList = menus.stream()
                .map(this::convertToMenuResponse)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildMenuTree(responseList);
    }

    @Override
    public List<MenuTreeResponse> getMenuTreeByUserId(Long userId) {
        log.info("根据用户ID获取菜单树形结构: {}", userId);

        List<SysMenu> menus = sysMenuMapper.selectMenuTreeByUserId(userId);
        List<MenuTreeResponse> responseList = menus.stream()
                .map(this::convertToMenuTreeResponse)
                .collect(Collectors.toList());

        return buildMenuTreeResponse(responseList);
    }

    @Override
    public List<MenuTreeResponse> getMenuTreeByRoleId(Long roleId) {
        log.info("根据角色ID获取菜单树形结构: {}", roleId);

        List<SysMenu> menus = sysMenuMapper.selectMenuTreeByRoleId(roleId);
        List<MenuTreeResponse> responseList = menus.stream()
                .map(this::convertToMenuTreeResponse)
                .collect(Collectors.toList());

        return buildMenuTreeResponse(responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableMenu(Long id) {
        log.info("启用菜单: {}", id);
        updateMenuStatus(id, CommonConstant.STATUS_ENABLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableMenu(Long id) {
        log.info("禁用菜单: {}", id);
        updateMenuStatus(id, CommonConstant.STATUS_DISABLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEnableMenus(List<Long> ids) {
        log.info("批量启用菜单: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("菜单ID列表不能为空");
        }

        for (Long id : ids) {
            enableMenu(id);
        }

        log.info("菜单批量启用成功: {}", ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDisableMenus(List<Long> ids) {
        log.info("批量禁用菜单: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("菜单ID列表不能为空");
        }

        for (Long id : ids) {
            disableMenu(id);
        }

        log.info("菜单批量禁用成功: {}", ids);
    }

    @Override
    public List<MenuResponse> getChildrenByParentId(Long parentId) {
        log.info("根据父菜单ID获取子菜单列表: {}", parentId);

        List<SysMenu> menus = sysMenuMapper.selectChildrenByParentId(parentId);
        return menus.stream()
                .map(this::convertToMenuResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByMenuName(String menuName) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getMenuName, menuName)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByMenuName(String menuName, Long excludeId) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getMenuName, menuName)
                .ne(SysMenu::getId, excludeId)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPath(String path) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getPath, path)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPath(String path, Long excludeId) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getPath, path)
                .ne(SysMenu::getId, excludeId)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPerms(String perms) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getPerms, perms)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPerms(String perms, Long excludeId) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getPerms, perms)
                .ne(SysMenu::getId, excludeId)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean hasChildren(Long parentId) {
        return sysMenuMapper.selectCount(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getParentId, parentId)
                .eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public List<String> getPermsByUserId(Long userId) {
        log.info("根据用户ID获取权限标识列表: {}", userId);
        return sysMenuMapper.selectPermsByUserId(userId);
    }

    @Override
    public List<String> getPermsByRoleId(Long roleId) {
        log.info("根据角色ID获取权限标识列表: {}", roleId);
        return sysMenuMapper.selectPermsByRoleId(roleId);
    }

    @Override
    public List<MenuTreeResponse> getEnabledMenuTree() {
        log.info("获取所有启用的菜单树形结构");

        List<SysMenu> menus = sysMenuMapper.selectEnabledMenus();
        List<MenuTreeResponse> responseList = menus.stream()
                .map(this::convertToMenuTreeResponse)
                .collect(Collectors.toList());

        return buildMenuTreeResponse(responseList);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SysMenu> buildQueryWrapper(MenuQueryRequest request) {
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();

        // 菜单名称模糊查询
        if (StrUtil.isNotBlank(request.getMenuName())) {
            queryWrapper.like(SysMenu::getMenuName, request.getMenuName());
        }

        // 菜单标题模糊查询
        if (StrUtil.isNotBlank(request.getMenuTitle())) {
            queryWrapper.like(SysMenu::getMenuTitle, request.getMenuTitle());
        }

        // 菜单类型
        if (request.getMenuType() != null) {
            queryWrapper.eq(SysMenu::getMenuType, request.getMenuType());
        }

        // 父菜单ID
        if (request.getParentId() != null) {
            queryWrapper.eq(SysMenu::getParentId, request.getParentId());
        }

        // 路由路径模糊查询
        if (StrUtil.isNotBlank(request.getPath())) {
            queryWrapper.like(SysMenu::getPath, request.getPath());
        }

        // 组件路径模糊查询
        if (StrUtil.isNotBlank(request.getComponent())) {
            queryWrapper.like(SysMenu::getComponent, request.getComponent());
        }

        // 是否外链
        if (request.getIsFrame() != null) {
            queryWrapper.eq(SysMenu::getIsFrame, request.getIsFrame());
        }

        // 是否缓存
        if (request.getIsCache() != null) {
            queryWrapper.eq(SysMenu::getIsCache, request.getIsCache());
        }

        // 是否显示
        if (request.getVisible() != null) {
            queryWrapper.eq(SysMenu::getVisible, request.getVisible());
        }

        // 状态
        if (request.getStatus() != null) {
            queryWrapper.eq(SysMenu::getStatus, request.getStatus());
        }

        // 权限标识模糊查询
        if (StrUtil.isNotBlank(request.getPerms())) {
            queryWrapper.like(SysMenu::getPerms, request.getPerms());
        }

        // 是否系统内置菜单
        if (request.getIsSystem() != null) {
            queryWrapper.eq(SysMenu::getIsSystem, request.getIsSystem());
        }

        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge(SysMenu::getCreateTime, request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le(SysMenu::getCreateTime, request.getCreateTimeEnd());
        }

        // 只查询菜单（排除按钮）
        if (request.getMenuOnly() != null && request.getMenuOnly()) {
            queryWrapper.in(SysMenu::getMenuType, 1, 2); // 1：目录，2：菜单
        }

        // 逻辑删除
        queryWrapper.eq(SysMenu::getDeleted, CommonConstant.NOT_DELETED);

        // 排序
        if (StrUtil.isNotBlank(request.getSortField())) {
            if ("desc".equalsIgnoreCase(request.getSortOrder())) {
                queryWrapper.orderByDesc(getSortColumn(request.getSortField()));
            } else {
                queryWrapper.orderByAsc(getSortColumn(request.getSortField()));
            }
        } else {
            queryWrapper.orderByAsc(SysMenu::getSortOrder);
        }

        return queryWrapper;
    }

    /**
     * 获取排序字段
     */
    private SFunction<SysMenu, ?> getSortColumn(String sortField) {
        return switch (sortField) {
            case "menuName" -> SysMenu::getMenuName;
            case "menuType" -> SysMenu::getMenuType;
            case "sortOrder" -> SysMenu::getSortOrder;
            case "createTime" -> SysMenu::getCreateTime;
            case "updateTime" -> SysMenu::getUpdateTime;
            default -> SysMenu::getSortOrder;
        };
    }

    /**
     * 更新菜单状态
     */
    private void updateMenuStatus(Long id, Integer status) {
        // 检查菜单是否存在
        SysMenu menu = sysMenuMapper.selectById(id);
        if (menu == null || menu.getDeleted() == 1) {
            throw new BusinessException("菜单不存在");
        }

        // 检查是否为系统内置菜单
        if (menu.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置菜单不允许修改状态");
        }

        // 更新状态
        menu.setStatus(status);
        sysMenuMapper.updateById(menu);
    }

    /**
     * 转换为菜单响应对象
     */
    private MenuResponse convertToMenuResponse(SysMenu menu) {
        MenuResponse response = new MenuResponse();
        BeanUtil.copyProperties(menu, response);

        // 设置菜单类型描述
        response.setMenuTypeDesc(getMenuTypeDesc(menu.getMenuType()));

        // 设置状态描述
        response.setStatusDesc(menu.getStatus() == CommonConstant.STATUS_ENABLED ? "启用" : "禁用");

        // 设置是否外链描述
        response.setIsFrameDesc(menu.getIsFrame() == 1 ? "是" : "否");

        // 设置是否缓存描述
        response.setIsCacheDesc(menu.getIsCache() == 1 ? "是" : "否");

        // 设置是否显示描述
        response.setVisibleDesc(menu.getVisible() == 1 ? "显示" : "隐藏");

        // 设置父菜单名称
        if (menu.getParentId() != null && menu.getParentId() > 0) {
            SysMenu parentMenu = sysMenuMapper.selectById(menu.getParentId());
            if (parentMenu != null && parentMenu.getDeleted() == 0) {
                response.setParentName(parentMenu.getMenuName());
            }
        }

        // 设置是否有子菜单
        response.setHasChildren(hasChildren(menu.getId()));

        return response;
    }

    /**
     * 转换为菜单树形响应对象
     */
    private MenuTreeResponse convertToMenuTreeResponse(SysMenu menu) {
        MenuTreeResponse response = new MenuTreeResponse();
        response.setId(menu.getId());
        response.setName(menu.getMenuName());
        response.setTitle(menu.getMenuTitle() != null ? menu.getMenuTitle() : menu.getMenuName());
        response.setPath(menu.getPath());
        response.setComponent(menu.getComponent());
        response.setQuery(menu.getQuery());
        response.setIcon(menu.getIcon());
        response.setIsFrame(menu.getIsFrame());
        response.setIsCache(menu.getIsCache());
        response.setVisible(menu.getVisible());
        response.setMenuType(menu.getMenuType());
        response.setPerms(menu.getPerms());
        response.setSortOrder(menu.getSortOrder());
        response.setParentId(menu.getParentId());

        // 设置路由元信息
        MenuTreeResponse.Meta meta = new MenuTreeResponse.Meta();
        meta.setTitle(menu.getMenuTitle() != null ? menu.getMenuTitle() : menu.getMenuName());
        meta.setIcon(menu.getIcon());
        meta.setHidden(menu.getVisible() == 0);
        meta.setKeepAlive(menu.getIsCache() == 1);
        meta.setIsFrame(menu.getIsFrame() == 1);
        if (StrUtil.isNotBlank(menu.getPerms())) {
            meta.setPermissions(new String[]{menu.getPerms()});
        }
        meta.setSortOrder(menu.getSortOrder());
        response.setMeta(meta);

        return response;
    }

    /**
     * 获取菜单类型描述
     */
    private String getMenuTypeDesc(Integer menuType) {
        return switch (menuType) {
            case 1 -> "目录";
            case 2 -> "菜单";
            case 3 -> "按钮";
            default -> "未知";
        };
    }

    /**
     * 构建菜单树形结构
     */
    private List<MenuResponse> buildMenuTree(List<MenuResponse> menus) {
        if (CollUtil.isEmpty(menus)) {
            return new ArrayList<>();
        }

        // 按父菜单ID分组
        Map<Long, List<MenuResponse>> parentMap = menus.stream()
                .collect(Collectors.groupingBy(menu ->
                    menu.getParentId() == null ? 0L : menu.getParentId()));

        // 构建树形结构
        List<MenuResponse> rootMenus = parentMap.getOrDefault(0L, new ArrayList<>());
        for (MenuResponse menu : rootMenus) {
            buildChildren(menu, parentMap);
        }

        return rootMenus;
    }

    /**
     * 递归构建子菜单
     */
    private void buildChildren(MenuResponse menu, Map<Long, List<MenuResponse>> parentMap) {
        List<MenuResponse> children = parentMap.get(menu.getId());
        if (CollUtil.isNotEmpty(children)) {
            menu.setChildren(children);
            for (MenuResponse child : children) {
                buildChildren(child, parentMap);
            }
        }
    }

    /**
     * 构建菜单树形响应结构
     */
    private List<MenuTreeResponse> buildMenuTreeResponse(List<MenuTreeResponse> menus) {
        if (CollUtil.isEmpty(menus)) {
            return new ArrayList<>();
        }

        // 按父菜单ID分组
        Map<Long, List<MenuTreeResponse>> parentMap = menus.stream()
                .collect(Collectors.groupingBy(menu ->
                    menu.getParentId() == null ? 0L : menu.getParentId()));

        // 构建树形结构
        List<MenuTreeResponse> rootMenus = parentMap.getOrDefault(0L, new ArrayList<>());
        for (MenuTreeResponse menu : rootMenus) {
            buildTreeChildren(menu, parentMap);
        }

        return rootMenus;
    }

    /**
     * 递归构建子菜单树形响应
     */
    private void buildTreeChildren(MenuTreeResponse menu, Map<Long, List<MenuTreeResponse>> parentMap) {
        List<MenuTreeResponse> children = parentMap.get(menu.getId());
        if (CollUtil.isNotEmpty(children)) {
            menu.setChildren(children);
            for (MenuTreeResponse child : children) {
                buildTreeChildren(child, parentMap);
            }
        }
    }
}
