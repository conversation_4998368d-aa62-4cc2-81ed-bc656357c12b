package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.RoleCreateRequest;
import com.example.admin.dto.request.RoleQueryRequest;
import com.example.admin.dto.request.RoleUpdateRequest;
import com.example.admin.dto.request.RolePermissionAssignRequest;
import com.example.admin.dto.response.RoleResponse;
import com.example.admin.entity.SysRole;
import com.example.admin.entity.SysRolePermission;
import com.example.admin.entity.SysUserRole;
import com.example.admin.mapper.SysRoleMapper;
import com.example.admin.mapper.SysRolePermissionMapper;
import com.example.admin.mapper.SysUserRoleMapper;
import com.example.admin.service.SysRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统角色服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl implements SysRoleService {

    private final SysRoleMapper sysRoleMapper;
    private final SysRolePermissionMapper sysRolePermissionMapper;
    private final SysUserRoleMapper sysUserRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RoleResponse createRole(RoleCreateRequest request) {
        log.info("创建角色: {}", request.getRoleCode());

        // 检查角色编码是否已存在
        if (existsByRoleCode(request.getRoleCode())) {
            throw new BusinessException("角色编码已存在");
        }

        // 检查角色名称是否已存在
        if (existsByRoleName(request.getRoleName())) {
            throw new BusinessException("角色名称已存在");
        }

        // 创建角色实体
        SysRole role = new SysRole();
        BeanUtil.copyProperties(request, role);

        // 保存角色
        sysRoleMapper.insert(role);

        // 分配权限
        if (request.getPermissionIds() != null && request.getPermissionIds().length > 0) {
            RolePermissionAssignRequest assignRequest = new RolePermissionAssignRequest();
            assignRequest.setRoleId(role.getId());
            assignRequest.setPermissionIds(request.getPermissionIds());
            assignPermissions(assignRequest);
        }

        log.info("角色创建成功: {}", request.getRoleCode());
        return getRoleById(role.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RoleResponse updateRole(RoleUpdateRequest request) {
        log.info("更新角色: {}", request.getId());

        // 检查角色是否存在
        SysRole existingRole = sysRoleMapper.selectById(request.getId());
        if (existingRole == null || existingRole.getDeleted() == 1) {
            throw new BusinessException("角色不存在");
        }

        // 检查是否为系统内置角色
        if (existingRole.getIsSystem() == 1) {
            throw new BusinessException("系统内置角色不允许修改");
        }

        // 检查角色名称是否已存在（排除当前角色）
        if (existsByRoleName(request.getRoleName(), request.getId())) {
            throw new BusinessException("角色名称已存在");
        }

        // 更新角色信息
        BeanUtil.copyProperties(request, existingRole, "id", "roleCode", "createTime", "createBy");
        sysRoleMapper.updateById(existingRole);

        // 更新权限分配
        if (request.getPermissionIds() != null) {
            RolePermissionAssignRequest assignRequest = new RolePermissionAssignRequest();
            assignRequest.setRoleId(request.getId());
            assignRequest.setPermissionIds(request.getPermissionIds());
            assignPermissions(assignRequest);
        }

        log.info("角色更新成功: {}", request.getId());
        return getRoleById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        log.info("删除角色: {}", id);

        // 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(id);
        if (role == null || role.getDeleted() == 1) {
            throw new BusinessException("角色不存在");
        }

        // 检查是否为系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException("系统内置角色不允许删除");
        }

        // 检查是否有用户使用该角色
        long userCount = sysUserRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getRoleId, id));
        if (userCount > 0) {
            throw new BusinessException("该角色下还有用户，不允许删除");
        }

        // 软删除角色
        role.setDeleted(1);
        sysRoleMapper.updateById(role);

        // 删除角色权限关联
        sysRolePermissionMapper.delete(new LambdaQueryWrapper<SysRolePermission>()
                .eq(SysRolePermission::getRoleId, id));

        log.info("角色删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoles(List<Long> ids) {
        log.info("批量删除角色: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("角色ID列表不能为空");
        }

        for (Long id : ids) {
            deleteRole(id);
        }

        log.info("批量删除角色成功: {}", ids);
    }

    @Override
    public RoleResponse getRoleById(Long id) {
        SysRole role = sysRoleMapper.selectById(id);
        if (role == null || role.getDeleted() == 1) {
            throw new BusinessException("角色不存在");
        }

        return convertToRoleResponse(role);
    }

    @Override
    public SysRole getRoleByCode(String roleCode) {
        return sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleCode, roleCode)
                .eq(SysRole::getDeleted, 0));
    }

    @Override
    public Page<RoleResponse> getRolePage(RoleQueryRequest request) {
        log.info("分页查询角色: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysRole> queryWrapper = buildQueryWrapper(request);

        // 分页查询
        Page<SysRole> page = new Page<>(request.getCurrent(), request.getSize());
        Page<SysRole> rolePage = sysRoleMapper.selectPage(page, queryWrapper);

        // 转换为响应对象
        Page<RoleResponse> responsePage = new Page<>();
        BeanUtil.copyProperties(rolePage, responsePage);
        
        List<RoleResponse> roleResponses = rolePage.getRecords().stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(roleResponses);

        return responsePage;
    }

    @Override
    public List<RoleResponse> getRoleList(RoleQueryRequest request) {
        log.info("查询角色列表: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysRole> queryWrapper = buildQueryWrapper(request);

        // 查询角色列表
        List<SysRole> roles = sysRoleMapper.selectList(queryWrapper);

        // 转换为响应对象
        return roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableRole(Long id) {
        log.info("启用角色: {}", id);
        updateRoleStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableRole(Long id) {
        log.info("禁用角色: {}", id);
        updateRoleStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableRoles(List<Long> ids) {
        log.info("批量启用角色: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("角色ID列表不能为空");
        }

        for (Long id : ids) {
            enableRole(id);
        }

        log.info("批量启用角色成功: {}", ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableRoles(List<Long> ids) {
        log.info("批量禁用角色: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("角色ID列表不能为空");
        }

        for (Long id : ids) {
            disableRole(id);
        }

        log.info("批量禁用角色成功: {}", ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignPermissions(RolePermissionAssignRequest request) {
        log.info("分配角色权限: roleId={}, permissionIds={}", request.getRoleId(), request.getPermissionIds());

        // 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(request.getRoleId());
        if (role == null || role.getDeleted() == 1) {
            throw new BusinessException("角色不存在");
        }

        // 删除原有权限关联
        sysRolePermissionMapper.delete(new LambdaQueryWrapper<SysRolePermission>()
                .eq(SysRolePermission::getRoleId, request.getRoleId()));

        // 添加新的权限关联
        if (ArrayUtil.isNotEmpty(request.getPermissionIds())) {
            List<SysRolePermission> rolePermissions = List.of(request.getPermissionIds()).stream()
                    .map(permissionId -> {
                        SysRolePermission rolePermission = new SysRolePermission();
                        rolePermission.setRoleId(request.getRoleId());
                        rolePermission.setPermissionId(permissionId);
                        return rolePermission;
                    })
                    .collect(Collectors.toList());

            for (SysRolePermission rolePermission : rolePermissions) {
                sysRolePermissionMapper.insert(rolePermission);
            }
        }

        log.info("角色权限分配成功: roleId={}", request.getRoleId());
    }

    @Override
    public List<Long> getRolePermissionIds(Long roleId) {
        return sysRolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    public boolean existsByRoleCode(String roleCode) {
        return sysRoleMapper.selectCount(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleCode, roleCode)
                .eq(SysRole::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByRoleName(String roleName) {
        return sysRoleMapper.selectCount(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleName, roleName)
                .eq(SysRole::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByRoleCode(String roleCode, Long excludeId) {
        if (StrUtil.isBlank(roleCode)) {
            return false;
        }
        return sysRoleMapper.selectCount(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleCode, roleCode)
                .ne(SysRole::getId, excludeId)
                .eq(SysRole::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByRoleName(String roleName, Long excludeId) {
        if (StrUtil.isBlank(roleName)) {
            return false;
        }
        return sysRoleMapper.selectCount(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleName, roleName)
                .ne(SysRole::getId, excludeId)
                .eq(SysRole::getDeleted, 0)) > 0;
    }

    @Override
    public List<RoleResponse> getUserRoles(Long userId) {
        // 查询用户角色关联
        List<SysUserRole> userRoles = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, userId));

        if (CollUtil.isEmpty(userRoles)) {
            return List.of();
        }

        // 获取角色ID列表
        List<Long> roleIds = userRoles.stream()
                .map(SysUserRole::getRoleId)
                .collect(Collectors.toList());

        // 查询角色信息
        List<SysRole> roles = sysRoleMapper.selectBatchIds(roleIds);

        // 转换为响应对象
        return roles.stream()
                .filter(role -> role.getDeleted() == 0)
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<RoleResponse> exportRoles(RoleQueryRequest request) {
        log.info("导出角色数据: {}", request);
        return getRoleList(request);
    }

    /**
     * 更新角色状态
     */
    private void updateRoleStatus(Long id, Integer status) {
        // 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(id);
        if (role == null || role.getDeleted() == 1) {
            throw new BusinessException("角色不存在");
        }

        // 检查是否为系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException("系统内置角色不允许修改状态");
        }

        // 更新状态
        role.setStatus(status);
        sysRoleMapper.updateById(role);

        log.info("角色状态更新成功: id={}, status={}", id, status);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SysRole> buildQueryWrapper(RoleQueryRequest request) {
        LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(SysRole::getDeleted, 0);

        if (StrUtil.isNotBlank(request.getRoleCode())) {
            queryWrapper.like(SysRole::getRoleCode, request.getRoleCode());
        }

        if (StrUtil.isNotBlank(request.getRoleName())) {
            queryWrapper.like(SysRole::getRoleName, request.getRoleName());
        }

        if (request.getStatus() != null) {
            queryWrapper.eq(SysRole::getStatus, request.getStatus());
        }

        if (request.getDataScope() != null) {
            queryWrapper.eq(SysRole::getDataScope, request.getDataScope());
        }

        if (request.getIsSystem() != null) {
            queryWrapper.eq(SysRole::getIsSystem, request.getIsSystem());
        }

        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge(SysRole::getCreateTime, request.getCreateTimeStart());
        }

        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le(SysRole::getCreateTime, request.getCreateTimeEnd());
        }

        // 排序
        if (StrUtil.isNotBlank(request.getSortField())) {
            if ("desc".equalsIgnoreCase(request.getSortOrder())) {
                queryWrapper.orderByDesc(getSortFunction(request.getSortField()));
            } else {
                queryWrapper.orderByAsc(getSortFunction(request.getSortField()));
            }
        } else {
            queryWrapper.orderByAsc(SysRole::getSortOrder);
        }

        return queryWrapper;
    }

    /**
     * 获取排序字段对应的Lambda表达式
     */
    private SFunction<SysRole, ?> getSortFunction(String sortField) {
        return switch (sortField) {
            case "createTime" -> SysRole::getCreateTime;
            case "updateTime" -> SysRole::getUpdateTime;
            case "sortOrder" -> SysRole::getSortOrder;
            case "roleCode" -> SysRole::getRoleCode;
            case "roleName" -> SysRole::getRoleName;
            default -> SysRole::getCreateTime;
        };
    }

    /**
     * 转换为角色响应对象
     */
    private RoleResponse convertToRoleResponse(SysRole role) {
        RoleResponse response = new RoleResponse();
        BeanUtil.copyProperties(role, response);

        // 设置状态描述
        if (role.getStatus() != null) {
            response.setStatusDesc(role.getStatus() == 1 ? "启用" : "禁用");
        }

        // 设置数据权限范围描述
        if (role.getDataScope() != null) {
            switch (role.getDataScope()) {
                case 1:
                    response.setDataScopeDesc("全部数据");
                    break;
                case 2:
                    response.setDataScopeDesc("本部门及以下数据");
                    break;
                case 3:
                    response.setDataScopeDesc("本部门数据");
                    break;
                case 4:
                    response.setDataScopeDesc("仅本人数据");
                    break;
                case 5:
                    response.setDataScopeDesc("自定义数据");
                    break;
                default:
                    response.setDataScopeDesc("未知");
                    break;
            }
        }

        // 获取角色权限信息
        List<RoleResponse.PermissionInfo> permissions = getRolePermissions(role.getId());
        response.setPermissions(permissions);

        // 获取用户数量
        Integer userCount = getUserCountByRoleId(role.getId());
        response.setUserCount(userCount);

        return response;
    }

    /**
     * 获取角色权限信息
     */
    private List<RoleResponse.PermissionInfo> getRolePermissions(Long roleId) {
        // TODO: 实现获取角色权限信息的逻辑
        // 这里暂时返回空列表，后续在权限管理模块中完善
        return List.of();
    }

    /**
     * 获取角色下的用户数量
     */
    private Integer getUserCountByRoleId(Long roleId) {
        return Math.toIntExact(sysUserRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getRoleId, roleId)));
    }
}
