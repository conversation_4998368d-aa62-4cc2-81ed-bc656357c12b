package com.example.admin.service.impl;

import com.example.admin.entity.SysLog;
import com.example.admin.service.WebSocketLogService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * WebSocket日志推送服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketLogServiceImpl implements WebSocketLogService {

    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    // 存储在线用户信息
    private final Map<String, String> onlineUsers = new ConcurrentHashMap<>();
    private final List<String> activeSessions = new CopyOnWriteArrayList<>();

    @Override
    public void pushRealTimeLog(SysLog sysLog) {
        try {
            // 推送到所有客户端
            messagingTemplate.convertAndSend("/topic/logs/realtime", sysLog);
            
            // 推送到特定用户的频道
            if (sysLog.getUsername() != null && !sysLog.getUsername().isEmpty()) {
                String userDestination = "/topic/logs/user/" + sysLog.getUsername();
                messagingTemplate.convertAndSend(userDestination, sysLog);
            }
            
            // 根据日志类型推送到不同频道
            String typeDestination = "/topic/logs/type/" + sysLog.getLogType();
            messagingTemplate.convertAndSend(typeDestination, sysLog);
            
            log.debug("推送实时日志成功: {}", sysLog.getDescription());
            
        } catch (Exception e) {
            log.error("推送实时日志失败", e);
        }
    }

    @Override
    public void pushBatchRealTimeLogs(List<SysLog> logs) {
        try {
            if (logs == null || logs.isEmpty()) {
                return;
            }

            // 批量推送到所有客户端
            messagingTemplate.convertAndSend("/topic/logs/batch", logs);
            
            // 按用户分组推送
            Map<String, List<SysLog>> userLogs = logs.stream()
                    .filter(log -> log.getUsername() != null && !log.getUsername().isEmpty())
                    .collect(java.util.stream.Collectors.groupingBy(SysLog::getUsername));
            
            for (Map.Entry<String, List<SysLog>> entry : userLogs.entrySet()) {
                String userDestination = "/topic/logs/user/" + entry.getKey();
                messagingTemplate.convertAndSend(userDestination, entry.getValue());
            }
            
            // 按类型分组推送
            Map<String, List<SysLog>> typeLogs = logs.stream()
                    .filter(log -> log.getLogType() != null && !log.getLogType().isEmpty())
                    .collect(java.util.stream.Collectors.groupingBy(SysLog::getLogType));
            
            for (Map.Entry<String, List<SysLog>> entry : typeLogs.entrySet()) {
                String typeDestination = "/topic/logs/type/" + entry.getKey();
                messagingTemplate.convertAndSend(typeDestination, entry.getValue());
            }
            
            log.info("批量推送实时日志成功，数量: {}", logs.size());
            
        } catch (Exception e) {
            log.error("批量推送实时日志失败", e);
        }
    }

    @Override
    public void pushLogStatistics(String statisticsType, Object statistics) {
        try {
            String destination = "/topic/logs/statistics/" + statisticsType;
            messagingTemplate.convertAndSend(destination, statistics);
            
            log.debug("推送日志统计成功: {}", statisticsType);
            
        } catch (Exception e) {
            log.error("推送日志统计失败: {}", statisticsType, e);
        }
    }

    @Override
    public void pushAlert(String alertType, String alertMessage, Object alertData) {
        try {
            // 构建告警消息
            Map<String, Object> alert = Map.of(
                    "alertType", alertType,
                    "message", alertMessage,
                    "data", alertData,
                    "timestamp", System.currentTimeMillis()
            );
            
            // 推送到所有客户端
            messagingTemplate.convertAndSend("/topic/logs/alerts", alert);
            
            // 推送到管理员频道
            messagingTemplate.convertAndSend("/topic/logs/alerts/admin", alert);
            
            log.warn("推送告警信息成功: {} - {}", alertType, alertMessage);
            
        } catch (Exception e) {
            log.error("推送告警信息失败: {} - {}", alertType, alertMessage, e);
        }
    }

    @Override
    public void pushPerformanceMetrics(Object metrics) {
        try {
            String destination = "/topic/logs/performance";
            messagingTemplate.convertAndSend(destination, metrics);
            
            log.debug("推送性能指标成功");
            
        } catch (Exception e) {
            log.error("推送性能指标失败", e);
        }
    }

    @Override
    public void pushSystemStatus(Object status) {
        try {
            String destination = "/topic/logs/system/status";
            messagingTemplate.convertAndSend(destination, status);
            
            log.debug("推送系统状态成功");
            
        } catch (Exception e) {
            log.error("推送系统状态失败", e);
        }
    }

    @Override
    public void broadcastToAll(String destination, Object message) {
        try {
            messagingTemplate.convertAndSend(destination, message);
            
            log.debug("广播消息成功: {}", destination);
            
        } catch (Exception e) {
            log.error("广播消息失败: {}", destination, e);
        }
    }

    @Override
    public void sendToUser(String username, String destination, Object message) {
        try {
            String userDestination = "/topic/user/" + username + destination;
            messagingTemplate.convertAndSend(userDestination, message);
            
            log.debug("发送消息给用户成功: {} - {}", username, destination);
            
        } catch (Exception e) {
            log.error("发送消息给用户失败: {} - {}", username, destination, e);
        }
    }

    @Override
    public void sendToSession(String sessionId, String destination, Object message) {
        try {
            String sessionDestination = "/topic/session/" + sessionId + destination;
            messagingTemplate.convertAndSend(sessionDestination, message);
            
            log.debug("发送消息给会话成功: {} - {}", sessionId, destination);
            
        } catch (Exception e) {
            log.error("发送消息给会话失败: {} - {}", sessionId, destination, e);
        }
    }

    @Override
    public int getOnlineClientCount() {
        return activeSessions.size();
    }

    @Override
    public List<String> getOnlineUsers() {
        return new ArrayList<>(onlineUsers.keySet());
    }

    @Override
    public boolean isUserOnline(String username) {
        return onlineUsers.containsKey(username);
    }

    /**
     * 用户连接时调用
     */
    public void handleUserConnect(String sessionId, String username) {
        activeSessions.add(sessionId);
        if (username != null && !username.isEmpty()) {
            onlineUsers.put(username, sessionId);
        }
        
        log.info("用户连接成功: {}, 在线用户数: {}", username, getOnlineClientCount());
        
        // 推送在线用户数更新
        Map<String, Object> status = Map.of(
                "onlineCount", getOnlineClientCount(),
                "onlineUsers", getOnlineUsers()
        );
        pushSystemStatus(status);
    }

    /**
     * 用户断开连接时调用
     */
    public void handleUserDisconnect(String sessionId, String username) {
        activeSessions.remove(sessionId);
        if (username != null && !username.isEmpty()) {
            onlineUsers.remove(username);
        }
        
        log.info("用户断开连接: {}, 在线用户数: {}", username, getOnlineClientCount());
        
        // 推送在线用户数更新
        Map<String, Object> status = Map.of(
                "onlineCount", getOnlineClientCount(),
                "onlineUsers", getOnlineUsers()
        );
        pushSystemStatus(status);
    }

    /**
     * 处理用户订阅
     */
    public void handleUserSubscribe(String sessionId, String username, String destination) {
        log.debug("用户订阅: {} - {}", username, destination);
        
        // 可以根据不同的订阅目的地发送不同的初始数据
        if (destination.startsWith("/topic/logs/")) {
            // 发送最近的日志给新订阅的用户
            // 这里可以调用相关的服务获取最近的日志数据
        }
    }

    /**
     * 处理用户取消订阅
     */
    public void handleUserUnsubscribe(String sessionId, String username, String destination) {
        log.debug("用户取消订阅: {} - {}", username, destination);
    }

    @Override
    public void broadcastToAllUsers(String destination, Object message) {
        try {
            messagingTemplate.convertAndSend(destination, message);
            log.debug("广播消息成功: {}", destination);
        } catch (Exception e) {
            log.error("广播消息失败: {}", destination, e);
        }
    }

    @Override
    public void sendMessageToUser(String username, String destination, Object message) {
        try {
            String userDestination = "/topic/user/" + username + destination;
            messagingTemplate.convertAndSend(userDestination, message);
            log.debug("发送消息给用户成功: {}, {}", username, destination);
        } catch (Exception e) {
            log.error("发送消息给用户失败: {}, {}", username, destination, e);
        }
    }

    @Override
    public Map<String, String> getOnlineUsersInfo() {
        return new HashMap<>(onlineUsers);
    }

    @Override
    public Map<String, List<String>> getUserSubscriptions() {
        // 简化实现，返回空Map
        return new HashMap<>();
    }

}