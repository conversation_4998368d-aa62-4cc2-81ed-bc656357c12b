package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.UserCreateRequest;
import com.example.admin.dto.request.UserPasswordResetRequest;
import com.example.admin.dto.request.UserQueryRequest;
import com.example.admin.dto.request.UserUpdateRequest;
import com.example.admin.dto.response.UserExportResponse;
import com.example.admin.dto.response.UserResponse;
import com.example.admin.entity.SysRole;
import com.example.admin.entity.SysUser;
import com.example.admin.entity.SysUserRole;
import com.example.admin.mapper.SysRoleMapper;
import com.example.admin.mapper.SysUserMapper;
import com.example.admin.mapper.SysUserRoleMapper;
import com.example.admin.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统用户服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements SysUserService {

    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMapper sysRoleMapper;
    private final PasswordEncoder passwordEncoder;

    /**
     * 默认密码
     */
    private static final String DEFAULT_PASSWORD = "123456";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponse createUser(UserCreateRequest request) {
        log.info("创建用户: {}", request.getUsername());

        // 验证密码一致性
        if (!Objects.equals(request.getPassword(), request.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 检查用户名是否已存在
        if (existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StrUtil.isNotBlank(request.getEmail()) && existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 检查手机号是否已存在
        if (StrUtil.isNotBlank(request.getPhone()) && existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已存在");
        }

        // 创建用户实体
        SysUser user = new SysUser();
        BeanUtil.copyProperties(request, user);
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPasswordUpdateTime(LocalDateTime.now());

        // 保存用户
        sysUserMapper.insert(user);

        // 分配角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            assignRoles(user.getId(), request.getRoleIds());
        }

        log.info("用户创建成功: {}", request.getUsername());
        return getUserById(user.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResponse updateUser(UserUpdateRequest request) {
        log.info("更新用户: {}", request.getId());

        // 检查用户是否存在
        SysUser existingUser = sysUserMapper.selectById(request.getId());
        if (existingUser == null || existingUser.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 检查邮箱是否已存在（排除当前用户）
        if (StrUtil.isNotBlank(request.getEmail()) && existsByEmail(request.getEmail(), request.getId())) {
            throw new BusinessException("邮箱已存在");
        }

        // 检查手机号是否已存在（排除当前用户）
        if (StrUtil.isNotBlank(request.getPhone()) && existsByPhone(request.getPhone(), request.getId())) {
            throw new BusinessException("手机号已存在");
        }

        // 更新用户信息
        SysUser user = new SysUser();
        BeanUtil.copyProperties(request, user);
        user.setUsername(null); // 用户名不允许修改
        
        sysUserMapper.updateById(user);

        // 更新角色分配
        if (request.getRoleIds() != null) {
            assignRoles(request.getId(), request.getRoleIds());
        }

        log.info("用户更新成功: {}", request.getId());
        return getUserById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        log.info("删除用户: {}", id);

        // 检查用户是否存在
        SysUser user = sysUserMapper.selectById(id);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 软删除用户
        user.setDeleted(1);
        sysUserMapper.updateById(user);

        // 删除用户角色关联
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, id));

        log.info("用户删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUsers(List<Long> ids) {
        log.info("批量删除用户: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("用户ID列表不能为空");
        }

        // 批量查询用户
        List<SysUser> users = sysUserMapper.selectBatchIds(ids);
        if (users.size() != ids.size()) {
            throw new BusinessException("部分用户不存在");
        }

        // 批量软删除用户
        users.forEach(user -> user.setDeleted(1));
        users.forEach(sysUserMapper::updateById);

        // 批量删除用户角色关联
        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .in(SysUserRole::getUserId, ids)
        );

        log.info("批量删除用户成功: {}", ids);
    }

    @Override
    public UserResponse getUserById(Long id) {
        SysUser user = sysUserMapper.selectById(id);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        return convertToUserResponse(user);
    }

    @Override
    public SysUser getUserByUsername(String username) {
        return sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, username)
                .eq(SysUser::getDeleted, 0));
    }

    @Override
    public Page<UserResponse> getUserPage(UserQueryRequest request) {
        log.info("分页查询用户: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysUser> queryWrapper = buildQueryWrapper(request);

        // 分页查询
        Page<SysUser> page = new Page<>(request.getCurrent(), request.getSize());
        Page<SysUser> userPage = sysUserMapper.selectPage(page, queryWrapper);

        // 转换为响应对象
        Page<UserResponse> responsePage = new Page<>();
        BeanUtil.copyProperties(userPage, responsePage);
        
        List<UserResponse> userResponses = userPage.getRecords().stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(userResponses);

        return responsePage;
    }

    @Override
    public List<UserResponse> getUserList(UserQueryRequest request) {
        log.info("查询用户列表: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysUser> queryWrapper = buildQueryWrapper(request);

        // 查询用户列表
        List<SysUser> users = sysUserMapper.selectList(queryWrapper);

        // 转换为响应对象
        return users.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableUser(Long id) {
        log.info("启用用户: {}", id);
        updateUserStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableUser(Long id) {
        log.info("禁用用户: {}", id);
        updateUserStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableUsers(List<Long> ids) {
        log.info("批量启用用户: {}", ids);
        updateUsersStatus(ids, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableUsers(List<Long> ids) {
        log.info("批量禁用用户: {}", ids);
        updateUsersStatus(ids, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(UserPasswordResetRequest request) {
        log.info("重置用户密码: {}", request.getUserId());

        // 验证密码一致性
        if (!Objects.equals(request.getNewPassword(), request.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 检查用户是否存在
        SysUser user = sysUserMapper.selectById(request.getUserId());
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setPasswordUpdateTime(LocalDateTime.now());
        sysUserMapper.updateById(user);

        log.info("用户密码重置成功: {}", request.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetPasswordToDefault(Long id) {
        log.info("重置用户密码为默认密码: {}", id);

        // 检查用户是否存在
        SysUser user = sysUserMapper.selectById(id);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 更新密码为默认密码
        user.setPassword(passwordEncoder.encode(DEFAULT_PASSWORD));
        user.setPasswordUpdateTime(LocalDateTime.now());
        sysUserMapper.updateById(user);

        log.info("用户密码重置为默认密码成功: {}", id);
        return DEFAULT_PASSWORD;
    }

    @Override
    public void assignRoles(Long userId, List<Long> roleIds) {
        log.info("分配用户角色: userId={}, roleIds={}", userId, roleIds);

        // 检查用户是否存在
        SysUser user = sysUserMapper.selectById(userId);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 去重处理
        List<Long> distinctRoleIds = CollUtil.isNotEmpty(roleIds) 
                ? roleIds.stream().distinct().collect(Collectors.toList())
                : new ArrayList<>();

        // 查询现有角色关联
        List<SysUserRole> existingRoles = sysUserRoleMapper.selectList(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, userId)
        );

        // 找出需要删除的角色
        List<Long> existingRoleIds = existingRoles.stream()
                .map(SysUserRole::getRoleId)
                .collect(Collectors.toList());
        
        List<Long> rolesToDelete = existingRoleIds.stream()
                .filter(roleId -> !distinctRoleIds.contains(roleId))
                .collect(Collectors.toList());

        // 找出需要添加的角色
        List<Long> rolesToAdd = distinctRoleIds.stream()
                .filter(roleId -> !existingRoleIds.contains(roleId))
                .collect(Collectors.toList());

        // 删除不再需要的角色
        if (CollUtil.isNotEmpty(rolesToDelete)) {
            sysUserRoleMapper.delete(
                    new LambdaQueryWrapper<SysUserRole>()
                            .eq(SysUserRole::getUserId, userId)
                            .in(SysUserRole::getRoleId, rolesToDelete)
            );
        }

        // 添加新的角色关联
        if (CollUtil.isNotEmpty(rolesToAdd)) {
            List<SysUserRole> userRoles = rolesToAdd.stream()
                    .map(roleId -> {
                        SysUserRole userRole = new SysUserRole();
                        userRole.setUserId(userId);
                        userRole.setRoleId(roleId);
                        return userRole;
                    })
                    .collect(Collectors.toList());

            for (SysUserRole userRole : userRoles) {
                try {
                    sysUserRoleMapper.insert(userRole);
                } catch (Exception e) {
                    log.warn("插入用户角色关联失败，可能已存在: userId={}, roleId={}", 
                            userId, userRole.getRoleId(), e);
                }
            }
        }

        log.info("用户角色分配成功: userId={}", userId);
    }

    @Override
    public boolean existsByUsername(String username) {
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, username)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getEmail, email)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getPhone, phone)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByUsername(String username, Long excludeId) {
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, username)
                .ne(SysUser::getId, excludeId)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByEmail(String email, Long excludeId) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getEmail, email)
                .ne(SysUser::getId, excludeId)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public boolean existsByPhone(String phone, Long excludeId) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        return sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getPhone, phone)
                .ne(SysUser::getId, excludeId)
                .eq(SysUser::getDeleted, 0)) > 0;
    }

    @Override
    public List<UserResponse> exportUsers(UserQueryRequest request) {
        log.info("导出用户数据: {}", request);
        return getUserList(request);
    }

    @Override
    public List<UserExportResponse> exportUsersForExcel(UserQueryRequest request) {
        log.info("导出用户数据为Excel格式: {}", request);
        
        List<UserResponse> userResponseList = getUserList(request);
        List<UserExportResponse> exportList = new ArrayList<>();
        
        for (UserResponse user : userResponseList) {
            try {
                UserExportResponse export = new UserExportResponse();
                export.setId(user.getId());
                export.setUsername(user.getUsername() != null ? user.getUsername() : "");
                export.setRealName(user.getRealName() != null ? user.getRealName() : "");
                export.setNickname(user.getNickname() != null ? user.getNickname() : "");
                export.setEmail(user.getEmail() != null ? user.getEmail() : "");
                export.setPhone(user.getPhone() != null ? user.getPhone() : "");
                export.setGender(user.getGender());
                export.setStatus(user.getStatus());
                export.setLastLoginTime(user.getLastLoginTime());
                export.setCreateTime(user.getCreateTime());
                export.setRemark(user.getRemark() != null ? user.getRemark() : "");
                exportList.add(export);
            } catch (Exception e) {
                log.error("转换用户数据失败，用户ID: {}", user.getId(), e);
                // 继续处理其他用户，不要因为一个用户失败而影响整个导出
                continue;
            }
        }
        
        return exportList;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SysUser> buildQueryWrapper(UserQueryRequest request) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(SysUser::getDeleted, 0);

        if (StrUtil.isNotBlank(request.getUsername())) {
            queryWrapper.like(SysUser::getUsername, request.getUsername());
        }

        if (StrUtil.isNotBlank(request.getRealName())) {
            queryWrapper.like(SysUser::getRealName, request.getRealName());
        }

        if (StrUtil.isNotBlank(request.getNickname())) {
            queryWrapper.like(SysUser::getNickname, request.getNickname());
        }

        if (StrUtil.isNotBlank(request.getEmail())) {
            queryWrapper.like(SysUser::getEmail, request.getEmail());
        }

        if (StrUtil.isNotBlank(request.getPhone())) {
            queryWrapper.like(SysUser::getPhone, request.getPhone());
        }

        if (request.getGender() != null) {
            queryWrapper.eq(SysUser::getGender, request.getGender());
        }

        if (request.getStatus() != null) {
            queryWrapper.eq(SysUser::getStatus, request.getStatus());
        }

        if (request.getDeptId() != null) {
            queryWrapper.eq(SysUser::getDeptId, request.getDeptId());
        }

        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge(SysUser::getCreateTime, request.getCreateTimeStart());
        }

        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le(SysUser::getCreateTime, request.getCreateTimeEnd());
        }

        if (request.getLastLoginTimeStart() != null) {
            queryWrapper.ge(SysUser::getLastLoginTime, request.getLastLoginTimeStart());
        }

        if (request.getLastLoginTimeEnd() != null) {
            queryWrapper.le(SysUser::getLastLoginTime, request.getLastLoginTimeEnd());
        }

        // 排序
        if (StrUtil.isNotBlank(request.getSortField())) {
            boolean isAsc = "asc".equalsIgnoreCase(request.getSortOrder());
            switch (request.getSortField()) {
                case "createTime":
                    queryWrapper.orderBy(true, isAsc, SysUser::getCreateTime);
                    break;
                case "updateTime":
                    queryWrapper.orderBy(true, isAsc, SysUser::getUpdateTime);
                    break;
                case "lastLoginTime":
                    queryWrapper.orderBy(true, isAsc, SysUser::getLastLoginTime);
                    break;
                case "username":
                    queryWrapper.orderBy(true, isAsc, SysUser::getUsername);
                    break;
                default:
                    queryWrapper.orderByDesc(SysUser::getCreateTime);
                    break;
            }
        } else {
            queryWrapper.orderByDesc(SysUser::getCreateTime);
        }

        return queryWrapper;
    }

    /**
     * 转换为用户响应对象
     */
    private UserResponse convertToUserResponse(SysUser user) {
        UserResponse response = new UserResponse();
        BeanUtil.copyProperties(user, response);

        // 设置性别描述
        if (user.getGender() != null) {
            switch (user.getGender()) {
                case 0:
                    response.setGenderDesc("女");
                    break;
                case 1:
                    response.setGenderDesc("男");
                    break;
                case 2:
                    response.setGenderDesc("未知");
                    break;
                default:
                    response.setGenderDesc("未知");
                    break;
            }
        }

        // 设置状态描述
        if (user.getStatus() != null) {
            response.setStatusDesc(user.getStatus() == 1 ? "启用" : "禁用");
        }

        // 获取用户角色信息
        List<UserResponse.RoleInfo> roles = getUserRoles(user.getId());
        response.setRoles(roles);

        return response;
    }

    /**
     * 获取用户角色信息
     */
    private List<UserResponse.RoleInfo> getUserRoles(Long userId) {
        // 查询用户角色关联
        List<SysUserRole> userRoles = sysUserRoleMapper.selectList(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, userId)
        );

        if (CollUtil.isEmpty(userRoles)) {
            return new ArrayList<>();
        }

        // 获取角色ID列表
        List<Long> roleIds = userRoles.stream()
                .map(SysUserRole::getRoleId)
                .collect(Collectors.toList());

        // 查询角色信息
        List<SysRole> roles = sysRoleMapper.selectBatchIds(roleIds);

        // 转换为角色信息对象
        return roles.stream()
                .map(role -> {
                    UserResponse.RoleInfo roleInfo = new UserResponse.RoleInfo();
                    roleInfo.setId(role.getId());
                    roleInfo.setCode(role.getRoleCode());
                    roleInfo.setName(role.getRoleName());
                    roleInfo.setDescription(role.getDescription());
                    return roleInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新用户状态
     */
    private void updateUserStatus(Long id, Integer status) {
        // 检查用户是否存在
        SysUser user = sysUserMapper.selectById(id);
        if (user == null || user.getDeleted() == 1) {
            throw new BusinessException("用户不存在");
        }

        // 更新状态
        user.setStatus(status);
        sysUserMapper.updateById(user);

        log.info("用户状态更新成功: id={}, status={}", id, status);
    }

    /**
     * 批量更新用户状态
     */
    private void updateUsersStatus(List<Long> ids, Integer status) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("用户ID列表不能为空");
        }

        // 批量更新用户状态，使用单条SQL语句提高性能
        sysUserMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                        .in(SysUser::getId, ids)
                        .eq(SysUser::getDeleted, 0)
                        .set(SysUser::getStatus, status)
        );

        log.info("批量更新用户状态成功: ids={}, status={}", ids, status);
    }
}
