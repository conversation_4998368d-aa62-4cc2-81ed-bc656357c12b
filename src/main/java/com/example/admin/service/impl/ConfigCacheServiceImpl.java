package com.example.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.example.admin.entity.SysConfig;
import com.example.admin.mapper.SysConfigMapper;
import com.example.admin.service.ConfigCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 配置缓存服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigCacheServiceImpl implements ConfigCacheService {

    private static final String CONFIG_CACHE_PREFIX = "sys:config:";
    private static final long CACHE_EXPIRE_TIME = 24; // 24小时过期

    private final RedisTemplate<String, Object> redisTemplate;
    private final SysConfigMapper sysConfigMapper;

    /**
     * 应用启动时加载所有配置到缓存
     */
    @PostConstruct
    public void initCache() {
        log.info("初始化系统配置缓存");
        refreshAllConfigs();
    }

    @Override
    public String getConfigValue(String configKey) {
        if (StrUtil.isBlank(configKey)) {
            return null;
        }

        String cacheKey = CONFIG_CACHE_PREFIX + configKey;
        Object value = redisTemplate.opsForValue().get(cacheKey);
        
        if (value != null) {
            return value.toString();
        }

        // 缓存中没有，从数据库查询
        SysConfig config = sysConfigMapper.selectByConfigKey(configKey);
        if (config != null && config.getStatus() == 1) {
            // 存入缓存
            setConfigValue(configKey, config.getConfigValue());
            return config.getConfigValue();
        }

        return null;
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return StrUtil.isNotBlank(value) ? value : defaultValue;
    }

    @Override
    public void setConfigValue(String configKey, String configValue) {
        if (StrUtil.isBlank(configKey)) {
            return;
        }

        String cacheKey = CONFIG_CACHE_PREFIX + configKey;
        redisTemplate.opsForValue().set(cacheKey, configValue, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
        log.debug("设置配置缓存: {} = {}", configKey, configValue);
    }

    @Override
    public void removeConfig(String configKey) {
        if (StrUtil.isBlank(configKey)) {
            return;
        }

        String cacheKey = CONFIG_CACHE_PREFIX + configKey;
        redisTemplate.delete(cacheKey);
        log.debug("删除配置缓存: {}", configKey);
    }

    @Override
    public Map<String, String> getAllConfigs() {
        Map<String, String> configMap = new HashMap<>();
        
        // 从数据库获取所有启用的配置
        List<SysConfig> configs = sysConfigMapper.selectEnabledConfigs();
        for (SysConfig config : configs) {
            configMap.put(config.getConfigKey(), config.getConfigValue());
        }
        
        return configMap;
    }

    @Override
    public void refreshAllConfigs() {
        log.info("刷新所有配置缓存");
        
        // 清空现有缓存
        clearAllConfigs();
        
        // 重新加载所有配置
        List<SysConfig> configs = sysConfigMapper.selectEnabledConfigs();
        for (SysConfig config : configs) {
            setConfigValue(config.getConfigKey(), config.getConfigValue());
        }
        
        log.info("配置缓存刷新完成，共加载 {} 个配置", configs.size());
    }

    @Override
    public void refreshConfig(String configKey) {
        if (StrUtil.isBlank(configKey)) {
            return;
        }

        log.debug("刷新配置缓存: {}", configKey);
        
        // 删除现有缓存
        removeConfig(configKey);
        
        // 重新加载配置
        SysConfig config = sysConfigMapper.selectByConfigKey(configKey);
        if (config != null && config.getStatus() == 1) {
            setConfigValue(configKey, config.getConfigValue());
        }
    }

    @Override
    public void clearAllConfigs() {
        log.info("清空所有配置缓存");
        
        // 获取所有配置缓存的key
        String pattern = CONFIG_CACHE_PREFIX + "*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }

    @Override
    public boolean hasConfig(String configKey) {
        if (StrUtil.isBlank(configKey)) {
            return false;
        }

        String cacheKey = CONFIG_CACHE_PREFIX + configKey;
        return Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey));
    }
}
