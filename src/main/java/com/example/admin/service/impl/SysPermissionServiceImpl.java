package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.common.constant.CommonConstant;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.PermissionCreateRequest;
import com.example.admin.dto.request.PermissionQueryRequest;
import com.example.admin.dto.request.PermissionUpdateRequest;
import com.example.admin.dto.response.PermissionResponse;
import com.example.admin.entity.SysPermission;
import com.example.admin.entity.SysRolePermission;
import com.example.admin.mapper.SysPermissionMapper;
import com.example.admin.mapper.SysRolePermissionMapper;
import com.example.admin.service.SysPermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统权限服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysPermissionServiceImpl implements SysPermissionService {

    private final SysPermissionMapper sysPermissionMapper;
    private final SysRolePermissionMapper sysRolePermissionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PermissionResponse createPermission(PermissionCreateRequest request) {
        log.info("创建权限: {}", request.getPermissionCode());

        // 检查权限编码是否已存在
        if (existsByPermissionCode(request.getPermissionCode())) {
            throw new BusinessException("权限编码已存在");
        }

        // 检查权限名称是否已存在
        if (existsByPermissionName(request.getPermissionName())) {
            throw new BusinessException("权限名称已存在");
        }

        // 检查父权限是否存在
        if (request.getParentId() != null && request.getParentId() > 0) {
            SysPermission parentPermission = sysPermissionMapper.selectById(request.getParentId());
            if (parentPermission == null || parentPermission.getDeleted() == 1) {
                throw new BusinessException("父权限不存在");
            }
        }

        // 创建权限实体
        SysPermission permission = new SysPermission();
        BeanUtil.copyProperties(request, permission);

        // 设置默认值
        if (permission.getParentId() == null) {
            permission.setParentId(0L);
        }
        if (permission.getStatus() == null) {
            permission.setStatus(CommonConstant.STATUS_ENABLED);
        }
        if (permission.getSortOrder() == null) {
            permission.setSortOrder(0);
        }
        if (permission.getIsSystem() == null) {
            permission.setIsSystem(CommonConstant.NO);
        }

        // 保存权限
        sysPermissionMapper.insert(permission);

        log.info("权限创建成功: {}", request.getPermissionCode());
        return getPermissionById(permission.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PermissionResponse updatePermission(PermissionUpdateRequest request) {
        log.info("更新权限: {}", request.getId());

        // 检查权限是否存在
        SysPermission existingPermission = sysPermissionMapper.selectById(request.getId());
        if (existingPermission == null || existingPermission.getDeleted() == 1) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否为系统内置权限
        if (existingPermission.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置权限不允许修改");
        }

        // 检查权限名称是否已存在（排除当前权限）
        if (StrUtil.isNotBlank(request.getPermissionName()) && 
            existsByPermissionName(request.getPermissionName(), request.getId())) {
            throw new BusinessException("权限名称已存在");
        }

        // 检查父权限是否存在
        if (request.getParentId() != null && request.getParentId() > 0) {
            SysPermission parentPermission = sysPermissionMapper.selectById(request.getParentId());
            if (parentPermission == null || parentPermission.getDeleted() == 1) {
                throw new BusinessException("父权限不存在");
            }
            
            // 检查是否设置自己为父权限
            if (request.getParentId().equals(request.getId())) {
                throw new BusinessException("不能设置自己为父权限");
            }
        }

        // 更新权限实体
        SysPermission permission = new SysPermission();
        BeanUtil.copyProperties(request, permission);
        sysPermissionMapper.updateById(permission);

        log.info("权限更新成功: {}", request.getId());
        return getPermissionById(request.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long id) {
        log.info("删除权限: {}", id);

        // 检查权限是否存在
        SysPermission permission = sysPermissionMapper.selectById(id);
        if (permission == null || permission.getDeleted() == 1) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否为系统内置权限
        if (permission.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置权限不允许删除");
        }

        // 检查是否有子权限
        if (hasChildren(id)) {
            throw new BusinessException("存在子权限，不允许删除");
        }

        // 检查是否有角色关联
        long roleCount = sysRolePermissionMapper.selectCount(new LambdaQueryWrapper<SysRolePermission>()
                .eq(SysRolePermission::getPermissionId, id));
        if (roleCount > 0) {
            throw new BusinessException("权限已被角色关联，不允许删除");
        }

        // 删除权限
        sysPermissionMapper.deleteById(id);

        log.info("权限删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermissions(List<Long> ids) {
        log.info("批量删除权限: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("权限ID列表不能为空");
        }

        for (Long id : ids) {
            deletePermission(id);
        }

        log.info("权限批量删除成功: {}", ids);
    }

    @Override
    public PermissionResponse getPermissionById(Long id) {
        log.info("获取权限: {}", id);

        SysPermission permission = sysPermissionMapper.selectById(id);
        if (permission == null || permission.getDeleted() == 1) {
            throw new BusinessException("权限不存在");
        }

        return convertToPermissionResponse(permission);
    }

    @Override
    public SysPermission getPermissionByCode(String permissionCode) {
        log.info("根据编码获取权限: {}", permissionCode);

        return sysPermissionMapper.selectOne(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getPermissionCode, permissionCode)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED));
    }

    @Override
    public Page<PermissionResponse> getPermissionPage(PermissionQueryRequest request) {
        log.info("分页查询权限: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysPermission> queryWrapper = buildQueryWrapper(request);

        // 分页查询
        Page<SysPermission> page = new Page<>(request.getCurrent(), request.getSize());
        page = sysPermissionMapper.selectPage(page, queryWrapper);

        // 转换为响应对象
        Page<PermissionResponse> responsePage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<PermissionResponse> responseList = page.getRecords().stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    @Override
    public List<PermissionResponse> getPermissionList(PermissionQueryRequest request) {
        log.info("查询权限列表: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysPermission> queryWrapper = buildQueryWrapper(request);

        // 查询权限列表
        List<SysPermission> permissions = sysPermissionMapper.selectList(queryWrapper);

        // 转换为响应对象
        List<PermissionResponse> responseList = permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());

        // 如果需要树形结构，则构建树形结构
        if (request.getTreeStructure() != null && request.getTreeStructure()) {
            return buildPermissionTree(responseList);
        }

        return responseList;
    }

    @Override
    public List<PermissionResponse> getPermissionTree() {
        log.info("获取权限树形结构");

        // 查询所有启用的权限
        List<SysPermission> permissions = sysPermissionMapper.selectList(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getStatus, CommonConstant.STATUS_ENABLED)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)
                .orderByAsc(SysPermission::getSortOrder));

        // 转换为响应对象
        List<PermissionResponse> responseList = permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildPermissionTree(responseList);
    }

    @Override
    public List<PermissionResponse> getPermissionTree(PermissionQueryRequest request) {
        log.info("获取权限树形结构: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysPermission> queryWrapper = buildQueryWrapper(request);

        // 查询权限列表
        List<SysPermission> permissions = sysPermissionMapper.selectList(queryWrapper);

        // 转换为响应对象
        List<PermissionResponse> responseList = permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildPermissionTree(responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enablePermission(Long id) {
        log.info("启用权限: {}", id);
        updatePermissionStatus(id, CommonConstant.STATUS_ENABLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disablePermission(Long id) {
        log.info("禁用权限: {}", id);
        updatePermissionStatus(id, CommonConstant.STATUS_DISABLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enablePermissions(List<Long> ids) {
        log.info("批量启用权限: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("权限ID列表不能为空");
        }

        for (Long id : ids) {
            enablePermission(id);
        }

        log.info("权限批量启用成功: {}", ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disablePermissions(List<Long> ids) {
        log.info("批量禁用权限: {}", ids);

        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("权限ID列表不能为空");
        }

        for (Long id : ids) {
            disablePermission(id);
        }

        log.info("权限批量禁用成功: {}", ids);
    }

    @Override
    public List<PermissionResponse> getPermissionsByRoleId(Long roleId) {
        log.info("根据角色ID获取权限列表: {}", roleId);

        List<SysPermission> permissions = sysPermissionMapper.selectPermissionsByRoleId(roleId);
        return permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionResponse> getPermissionsByUserId(Long userId) {
        log.info("根据用户ID获取权限列表: {}", userId);

        List<SysPermission> permissions = sysPermissionMapper.selectPermissionsByUserId(userId);
        return permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionResponse> getChildrenByParentId(Long parentId) {
        log.info("根据父权限ID获取子权限列表: {}", parentId);

        List<SysPermission> permissions = sysPermissionMapper.selectChildrenByParentId(parentId);
        return permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByPermissionCode(String permissionCode) {
        return sysPermissionMapper.selectCount(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getPermissionCode, permissionCode)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPermissionName(String permissionName) {
        return sysPermissionMapper.selectCount(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getPermissionName, permissionName)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPermissionCode(String permissionCode, Long excludeId) {
        return sysPermissionMapper.selectCount(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getPermissionCode, permissionCode)
                .ne(SysPermission::getId, excludeId)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean existsByPermissionName(String permissionName, Long excludeId) {
        return sysPermissionMapper.selectCount(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getPermissionName, permissionName)
                .ne(SysPermission::getId, excludeId)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public boolean hasChildren(Long parentId) {
        return sysPermissionMapper.selectCount(new LambdaQueryWrapper<SysPermission>()
                .eq(SysPermission::getParentId, parentId)
                .eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED)) > 0;
    }

    @Override
    public List<PermissionResponse> exportPermissions(PermissionQueryRequest request) {
        log.info("导出权限数据: {}", request);

        // 构建查询条件
        LambdaQueryWrapper<SysPermission> queryWrapper = buildQueryWrapper(request);

        // 查询权限列表
        List<SysPermission> permissions = sysPermissionMapper.selectList(queryWrapper);

        // 转换为响应对象
        return permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SysPermission> buildQueryWrapper(PermissionQueryRequest request) {
        LambdaQueryWrapper<SysPermission> queryWrapper = new LambdaQueryWrapper<>();

        // 如果request为null，返回基本查询条件
        if (request == null) {
            queryWrapper.eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED);
            queryWrapper.orderByAsc(SysPermission::getSortOrder);
            return queryWrapper;
        }

        // 权限编码模糊查询
        if (StrUtil.isNotBlank(request.getPermissionCode())) {
            queryWrapper.like(SysPermission::getPermissionCode, request.getPermissionCode());
        }

        // 权限名称模糊查询
        if (StrUtil.isNotBlank(request.getPermissionName())) {
            queryWrapper.like(SysPermission::getPermissionName, request.getPermissionName());
        }

        // 权限类型
        if (request.getPermissionType() != null) {
            queryWrapper.eq(SysPermission::getPermissionType, request.getPermissionType());
        }

        // 父权限ID
        if (request.getParentId() != null) {
            queryWrapper.eq(SysPermission::getParentId, request.getParentId());
        }

        // 状态
        if (request.getStatus() != null) {
            queryWrapper.eq(SysPermission::getStatus, request.getStatus());
        }

        // 是否系统内置权限
        if (request.getIsSystem() != null) {
            queryWrapper.eq(SysPermission::getIsSystem, request.getIsSystem());
        }

        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            queryWrapper.ge(SysPermission::getCreateTime, request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            queryWrapper.le(SysPermission::getCreateTime, request.getCreateTimeEnd());
        }

        // 逻辑删除
        queryWrapper.eq(SysPermission::getDeleted, CommonConstant.NOT_DELETED);

        // 排序
        if (StrUtil.isNotBlank(request.getSortField())) {
            if ("desc".equalsIgnoreCase(request.getSortOrder())) {
                queryWrapper.orderByDesc(getSortFunction(request.getSortField()));
            } else {
                queryWrapper.orderByAsc(getSortFunction(request.getSortField()));
            }
        } else {
            queryWrapper.orderByAsc(SysPermission::getSortOrder);
        }

        return queryWrapper;
    }

    /**
     * 获取排序字段对应的Lambda表达式
     */
    private SFunction<SysPermission, ?> getSortFunction(String sortField) {
        switch (sortField) {
            case "permissionCode":
                return SysPermission::getPermissionCode;
            case "permissionName":
                return SysPermission::getPermissionName;
            case "permissionType":
                return SysPermission::getPermissionType;
            case "sortOrder":
                return SysPermission::getSortOrder;
            case "createTime":
                return SysPermission::getCreateTime;
            case "updateTime":
                return SysPermission::getUpdateTime;
            default:
                return SysPermission::getSortOrder;
        }
    }

    /**
     * 更新权限状态
     */
    private void updatePermissionStatus(Long id, Integer status) {
        // 检查权限是否存在
        SysPermission permission = sysPermissionMapper.selectById(id);
        if (permission == null || permission.getDeleted() == 1) {
            throw new BusinessException("权限不存在");
        }

        // 检查是否为系统内置权限
        if (permission.getIsSystem() == CommonConstant.YES) {
            throw new BusinessException("系统内置权限不允许修改状态");
        }

        // 更新状态
        permission.setStatus(status);
        sysPermissionMapper.updateById(permission);
    }

    /**
     * 转换为权限响应对象
     */
    private PermissionResponse convertToPermissionResponse(SysPermission permission) {
        PermissionResponse response = new PermissionResponse();
        BeanUtil.copyProperties(permission, response);

        // 设置权限类型描述
        response.setPermissionTypeDesc(getPermissionTypeDesc(permission.getPermissionType()));

        // 设置状态描述
        response.setStatusDesc(permission.getStatus() == CommonConstant.STATUS_ENABLED ? "启用" : "禁用");

        // 设置父权限名称
        if (permission.getParentId() != null && permission.getParentId() > 0) {
            SysPermission parentPermission = sysPermissionMapper.selectById(permission.getParentId());
            if (parentPermission != null && parentPermission.getDeleted() == 0) {
                response.setParentName(parentPermission.getPermissionName());
            }
        }

        return response;
    }

    /**
     * 获取权限类型描述
     */
    private String getPermissionTypeDesc(Integer permissionType) {
        if (permissionType == null) {
            return "";
        }
        switch (permissionType) {
            case 1:
                return "菜单";
            case 2:
                return "按钮";
            case 3:
                return "接口";
            default:
                return "未知";
        }
    }

    /**
     * 构建权限树形结构
     */
    private List<PermissionResponse> buildPermissionTree(List<PermissionResponse> permissions) {
        if (CollUtil.isEmpty(permissions)) {
            return new ArrayList<>();
        }

        // 按父权限ID分组
        Map<Long, List<PermissionResponse>> parentMap = permissions.stream()
                .collect(Collectors.groupingBy(permission ->
                    permission.getParentId() == null ? 0L : permission.getParentId()));

        // 构建树形结构
        List<PermissionResponse> rootPermissions = parentMap.getOrDefault(0L, new ArrayList<>());
        for (PermissionResponse permission : rootPermissions) {
            buildChildren(permission, parentMap);
        }

        return rootPermissions;
    }

    /**
     * 递归构建子权限
     */
    private void buildChildren(PermissionResponse permission, Map<Long, List<PermissionResponse>> parentMap) {
        List<PermissionResponse> children = parentMap.get(permission.getId());
        if (CollUtil.isNotEmpty(children)) {
            permission.setChildren(children);
            for (PermissionResponse child : children) {
                buildChildren(child, parentMap);
            }
        }
    }
}
