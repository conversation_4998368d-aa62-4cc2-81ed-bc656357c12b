package com.example.admin.service.impl;

import com.example.admin.dto.response.DashboardResponse;
import com.example.admin.service.SysDashboardService;
import com.example.admin.service.SysLogService;
import com.example.admin.service.SysMenuService;
import com.example.admin.service.SysRoleService;
import com.example.admin.service.SysUserService;
import com.example.admin.utils.OnlineUserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.admin.entity.SysUser;
import com.example.admin.entity.SysRole;
import com.example.admin.entity.SysMenu;
import com.example.admin.mapper.SysUserMapper;
import com.example.admin.mapper.SysRoleMapper;
import com.example.admin.mapper.SysMenuMapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 仪表盘统计服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDashboardServiceImpl implements SysDashboardService {

    private final SysUserService sysUserService;
    private final SysRoleService sysRoleService;
    private final SysMenuService sysMenuService;
    private final SysLogService sysLogService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final OnlineUserUtils onlineUserUtils;
    private final SysUserMapper sysUserMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SysMenuMapper sysMenuMapper;

    private static final String ONLINE_USERS_KEY = "admin:online:users";
    private static final String DASHBOARD_CACHE_KEY = "admin:dashboard:statistics";
    private static final long CACHE_EXPIRE_MINUTES = 5;

    @Override
    public DashboardResponse getDashboardStatistics() {
        // 尝试从缓存获取
        DashboardResponse cached = (DashboardResponse) redisTemplate.opsForValue().get(DASHBOARD_CACHE_KEY);
        if (cached != null) {
            log.debug("从缓存获取仪表盘统计数据");
            return cached;
        }

        log.info("计算仪表盘统计数据");
        DashboardResponse response = new DashboardResponse();

        // 获取各模块统计数据
        response.setUserCount(getUserCount());
        response.setRoleCount(getRoleCount());
        response.setMenuCount(getMenuCount());
        response.setOnlineUserCount(getOnlineUserCount());
        response.setTodayActiveUserCount(getTodayActiveUserCount());
        response.setSystemInfo(getSystemInfo());
        
        // 获取最近活动
        Map<String, Object> recentActivity = getRecentActivity(10);
        response.setRecentActivities((List<Map<String, Object>>) recentActivity.get("activities"));
        
        // 获取用户增长趋势
        Map<String, Object> growthTrend = getUserGrowthTrend();
        response.setUserGrowthTrend((List<Map<String, Object>>) growthTrend.get("trend"));
        
        // 获取系统负载
        response.setSystemLoad(getSystemLoad());
        
        // 设置统计时间
        response.setStatisticsTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 缓存结果
        redisTemplate.opsForValue().set(DASHBOARD_CACHE_KEY, response, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return response;
    }

    @Override
    public long getOnlineUserCount() {
        return onlineUserUtils.getOnlineUserCount();
    }

    @Override
    public Map<String, Object> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // 系统基本信息
        systemInfo.put("systemVersion", "v1.0.0");
        systemInfo.put("environment", getEnvironment());
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("jvmName", System.getProperty("java.vm.name"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("serverTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 内存信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / (1024 * 1024);
        long freeMemory = runtime.freeMemory() / (1024 * 1024);
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory() / (1024 * 1024);
        
        Map<String, Object> memoryInfo = new HashMap<>();
        memoryInfo.put("total", totalMemory + " MB");
        memoryInfo.put("used", usedMemory + " MB");
        memoryInfo.put("free", freeMemory + " MB");
        memoryInfo.put("max", maxMemory + " MB");
        memoryInfo.put("usageRate", String.format("%.2f%%", (double) usedMemory / totalMemory * 100));
        
        systemInfo.put("memory", memoryInfo);
        
        // 数据库信息 (模拟)
        systemInfo.put("database", "MySQL 8.0");
        systemInfo.put("redis", "Redis 7.0");
        
        return systemInfo;
    }

    @Override
    public Map<String, Object> getRecentActivity(Integer limit) {
        Map<String, Object> result = new HashMap<>();
        
        // 这里可以从日志服务获取最近的活动记录
        // 由于日志模块已经存在，我们可以调用相关的服务
        
        List<Map<String, Object>> activities = new ArrayList<>();
        
        // 模拟一些活动数据，实际应该从日志表查询
        activities.add(createActivity("系统管理员", "登录系统", "2025-01-27 12:30"));
        activities.add(createActivity("系统管理员", "查看用户列表", "2025-01-27 12:15"));
        activities.add(createActivity("张三", "修改个人信息", "2025-01-27 11:45"));
        activities.add(createActivity("李四", "新增角色权限", "2025-01-27 10:20"));
        activities.add(createActivity("系统管理员", "系统配置更新", "2025-01-27 09:15"));
        
        // 限制数量
        if (activities.size() > limit) {
            activities = activities.subList(0, limit);
        }
        
        result.put("activities", activities);
        result.put("total", activities.size());
        
        return result;
    }

    @Override
    public Map<String, Object> getUserGrowthTrend() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> trend = new ArrayList<>();
        
        // 生成最近7天的用户增长趋势 (模拟数据)
        for (int i = 6; i >= 0; i--) {
            LocalDateTime date = LocalDateTime.now().minusDays(i);
            String dateStr = date.format(DateTimeFormatter.ofPattern("MM-dd"));
            
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", dateStr);
            dayData.put("newUsers", Math.round(Math.random() * 20) + 5); // 5-25个新用户
            dayData.put("totalUsers", 1200 + i * 15 + Math.round(Math.random() * 10)); // 递增的总用户数
            
            trend.add(dayData);
        }
        
        result.put("trend", trend);
        result.put("period", "最近7天");
        
        return result;
    }

    @Override
    public Map<String, Object> getSystemLoad() {
        Map<String, Object> load = new HashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        // CPU使用率 (模拟，实际需要更复杂的实现)
        double cpuUsage = Math.random() * 100;
        
        // 内存使用率
        double memoryUsage = (double) usedMemory / totalMemory * 100;
        
        // 磁盘使用率 (模拟)
        double diskUsage = Math.random() * 80 + 10;
        
        load.put("cpuUsage", String.format("%.1f%%", cpuUsage));
        load.put("memoryUsage", String.format("%.1f%%", memoryUsage));
        load.put("diskUsage", String.format("%.1f%%", diskUsage));
        load.put("status", cpuUsage > 80 || memoryUsage > 80 ? "warning" : "normal");
        
        return load;
    }

    private long getUserCount() {
        try {
            return sysUserMapper.selectCount(null);
        } catch (Exception e) {
            log.error("获取用户总数失败", e);
            return 0L;
        }
    }

    private long getRoleCount() {
        try {
            return sysRoleMapper.selectCount(null);
        } catch (Exception e) {
            log.error("获取角色总数失败", e);
            return 0L;
        }
    }

    private long getMenuCount() {
        try {
            return sysMenuMapper.selectCount(null);
        } catch (Exception e) {
            log.error("获取菜单总数失败", e);
            return 0L;
        }
    }

    private long getTodayActiveUserCount() {
        try {
            // 获取今日活跃用户数
            // 这里应该从日志表或用户活动表统计
            return 156L;
        } catch (Exception e) {
            log.error("获取今日活跃用户数失败", e);
            return 0L;
        }
    }

    private String getEnvironment() {
        String env = System.getProperty("spring.profiles.active");
        return env != null ? env : "开发环境";
    }

    private Map<String, Object> createActivity(String username, String action, String time) {
        Map<String, Object> activity = new HashMap<>();
        activity.put("username", username);
        activity.put("action", action);
        activity.put("time", time);
        return activity;
    }
}