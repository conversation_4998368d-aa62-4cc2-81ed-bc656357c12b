package com.example.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.entity.SysLogDetail;
import com.example.admin.mapper.SysLogDetailMapper;
import com.example.admin.service.SysLogDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统日志详情服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysLogDetailServiceImpl extends ServiceImpl<SysLogDetailMapper, SysLogDetail> implements SysLogDetailService {

    private final SysLogDetailMapper sysLogDetailMapper;

    @Override
    public List<SysLogDetail> getDetailsByLogId(Long logId) {
        return sysLogDetailMapper.selectDetailsByLogId(logId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDetails(List<SysLogDetail> details) {
        if (details == null || details.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        details.forEach(detail -> {
            detail.setCreateTime(now);
            detail.setUpdateTime(now);
        });
        
        return saveBatch(details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByLogId(Long logId) {
        return sysLogDetailMapper.deleteByLogId(logId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByLogIds(List<Long> logIds) {
        if (logIds == null || logIds.isEmpty()) {
            return false;
        }
        return sysLogDetailMapper.deleteByLogIds(logIds) > 0;
    }

    @Override
    public List<SysLogDetail> getSensitiveDetails(String startTime, String endTime) {
        return sysLogDetailMapper.selectSensitiveDetails(startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaskedStatus(Long id, String maskedValue) {
        return sysLogDetailMapper.updateMaskedStatus(id, maskedValue) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLogDetail(Long logId, String fieldName, String fieldValue, 
                                 String fieldType, String fieldDesc, Boolean isSensitive) {
        SysLogDetail detail = new SysLogDetail();
        detail.setLogId(logId);
        detail.setFieldName(fieldName);
        detail.setFieldValue(fieldValue);
        detail.setFieldType(fieldType);
        detail.setFieldDesc(fieldDesc);
        detail.setIsSensitive(isSensitive != null && isSensitive);
        detail.setIsMasked(false);
        detail.setFieldStatus("ACTIVE");
        detail.setSortOrder(0);
        
        return save(detail);
    }
    
}