package com.example.admin.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.example.admin.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 验证码缓存前缀
     */
    private static final String CAPTCHA_PREFIX = "captcha:";

    /**
     * 验证码过期时间（分钟）
     */
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    /**
     * 验证码图片宽度
     */
    private static final int CAPTCHA_WIDTH = 120;

    /**
     * 验证码图片高度
     */
    private static final int CAPTCHA_HEIGHT = 40;

    /**
     * 验证码字符数
     */
    private static final int CAPTCHA_CODE_COUNT = 4;

    /**
     * 干扰线数量
     */
    private static final int CAPTCHA_LINE_COUNT = 5;

    @Override
    public Map<String, String> generateCaptcha() {
        // 生成验证码
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(
                CAPTCHA_WIDTH, 
                CAPTCHA_HEIGHT, 
                CAPTCHA_CODE_COUNT, 
                CAPTCHA_LINE_COUNT
        );

        // 生成唯一标识
        String uuid = IdUtil.simpleUUID();

        // 获取验证码文本
        String code = lineCaptcha.getCode();

        // 获取验证码图片的base64编码
        String imageBase64 = lineCaptcha.getImageBase64Data();

        // 将验证码存储到Redis，设置过期时间
        String cacheKey = CAPTCHA_PREFIX + uuid;
        stringRedisTemplate.opsForValue().set(cacheKey, code, CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);

        log.info("生成验证码成功，UUID: {}, 验证码: {}", uuid, code);

        // 返回验证码信息
        Map<String, String> result = new HashMap<>();
        result.put("uuid", uuid);
        result.put("image", imageBase64);

        return result;
    }

    @Override
    public boolean validateCaptcha(String uuid, String code) {
        if (StrUtil.isBlank(uuid) || StrUtil.isBlank(code)) {
            log.warn("验证码验证失败：UUID或验证码为空");
            return false;
        }

        String cacheKey = CAPTCHA_PREFIX + uuid;
        String cachedCode = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isBlank(cachedCode)) {
            log.warn("验证码验证失败：验证码已过期或不存在，UUID: {}", uuid);
            return false;
        }

        // 验证码不区分大小写
        boolean isValid = code.equalsIgnoreCase(cachedCode);

        if (isValid) {
            log.info("验证码验证成功，UUID: {}", uuid);
            // 验证成功后删除验证码
            removeCaptcha(uuid);
        } else {
            log.warn("验证码验证失败：验证码不匹配，UUID: {}, 输入: {}, 期望: {}", uuid, code, cachedCode);
        }

        return isValid;
    }

    @Override
    public void removeCaptcha(String uuid) {
        if (StrUtil.isNotBlank(uuid)) {
            String cacheKey = CAPTCHA_PREFIX + uuid;
            stringRedisTemplate.delete(cacheKey);
            log.debug("删除验证码缓存，UUID: {}", uuid);
        }
    }
}
