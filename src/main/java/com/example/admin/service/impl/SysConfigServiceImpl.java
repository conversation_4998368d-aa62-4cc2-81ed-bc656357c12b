package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.ConfigCreateRequest;
import com.example.admin.dto.request.ConfigQueryRequest;
import com.example.admin.dto.request.ConfigUpdateRequest;
import com.example.admin.dto.response.ConfigResponse;
import com.example.admin.entity.SysConfig;
import com.example.admin.mapper.SysConfigMapper;
import com.example.admin.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

    private final SysConfigMapper sysConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigResponse createConfig(ConfigCreateRequest request) {
        log.info("创建系统配置: {}", request.getConfigKey());

        // 检查配置键是否已存在
        if (existsConfigKey(request.getConfigKey(), null)) {
            throw new BusinessException("配置键已存在: " + request.getConfigKey());
        }

        // 创建配置实体
        SysConfig config = new SysConfig();
        BeanUtil.copyProperties(request, config);

        // 保存配置
        save(config);

        // 刷新缓存
        refreshCache(request.getConfigKey());

        log.info("系统配置创建成功: {}", config.getId());
        return convertToResponse(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigResponse updateConfig(ConfigUpdateRequest request) {
        log.info("更新系统配置: {}", request.getId());

        // 检查配置是否存在
        SysConfig existingConfig = getById(request.getId());
        if (existingConfig == null) {
            throw new BusinessException("配置不存在");
        }

        // 检查配置键是否已被其他配置使用
        if (existsConfigKey(request.getConfigKey(), request.getId())) {
            throw new BusinessException("配置键已存在: " + request.getConfigKey());
        }

        // 系统配置不允许修改某些字段
        if (existingConfig.getIsSystem() == 1) {
            request.setIsEncrypted(existingConfig.getIsEncrypted());
        }

        // 更新配置
        BeanUtil.copyProperties(request, existingConfig, "createTime", "createBy");
        updateById(existingConfig);

        // 刷新缓存
        refreshCache(request.getConfigKey());

        log.info("系统配置更新成功: {}", existingConfig.getId());
        return convertToResponse(existingConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "sys_config", allEntries = true)
    public void deleteConfig(Long id) {
        log.info("删除系统配置: {}", id);

        SysConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("配置不存在");
        }

        // 系统配置不允许删除
        if (config.getIsSystem() == 1) {
            throw new BusinessException("系统配置不允许删除");
        }

        removeById(id);
        log.info("系统配置删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "sys_config", allEntries = true)
    public void deleteConfigs(List<Long> ids) {
        log.info("批量删除系统配置: {}", ids);

        // 检查是否包含系统配置
        List<SysConfig> configs = listByIds(ids);
        boolean hasSystemConfig = configs.stream().anyMatch(config -> config.getIsSystem() == 1);
        if (hasSystemConfig) {
            throw new BusinessException("不能删除系统配置");
        }

        removeByIds(ids);
        log.info("批量删除系统配置成功，数量: {}", ids.size());
    }

    @Override
    public ConfigResponse getConfigById(Long id) {
        SysConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("配置不存在");
        }
        return convertToResponse(config);
    }

    @Override
    @Cacheable(value = "sys_config", key = "#configKey")
    public String getConfigValue(String configKey) {
        SysConfig config = sysConfigMapper.selectByConfigKey(configKey);
        return config != null && config.getStatus() == 1 ? config.getConfigValue() : null;
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return StrUtil.isNotBlank(value) ? value : defaultValue;
    }

    @Override
    public Page<ConfigResponse> pageConfigs(ConfigQueryRequest request) {
        Page<SysConfig> page = new Page<>(request.getCurrent(), request.getSize());
        
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(request.getConfigKey()), SysConfig::getConfigKey, request.getConfigKey())
               .eq(StrUtil.isNotBlank(request.getConfigType()), SysConfig::getConfigType, request.getConfigType())
               .eq(request.getIsSystem() != null, SysConfig::getIsSystem, request.getIsSystem())
               .eq(request.getStatus() != null, SysConfig::getStatus, request.getStatus())
               .orderByDesc(SysConfig::getCreateTime);

        Page<SysConfig> configPage = page(page, wrapper);
        
        Page<ConfigResponse> responsePage = new Page<>();
        BeanUtil.copyProperties(configPage, responsePage, "records");
        responsePage.setRecords(configPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public List<ConfigResponse> getEnabledConfigs() {
        List<SysConfig> configs = sysConfigMapper.selectEnabledConfigs();
        return configs.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "sys_config", allEntries = true)
    public void refreshCache() {
        log.info("刷新所有配置缓存");
    }

    @Override
    @CacheEvict(value = "sys_config", key = "#configKey")
    public void refreshCache(String configKey) {
        log.info("刷新配置缓存: {}", configKey);
    }

    @Override
    public boolean existsConfigKey(String configKey, Long excludeId) {
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysConfig::getConfigKey, configKey);
        if (excludeId != null) {
            wrapper.ne(SysConfig::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    /**
     * 转换为响应DTO
     */
    private ConfigResponse convertToResponse(SysConfig config) {
        ConfigResponse response = new ConfigResponse();
        BeanUtil.copyProperties(config, response);
        
        // 如果是加密配置，不返回实际值
        if (config.getIsEncrypted() == 1) {
            response.setConfigValue("******");
        }
        
        return response;
    }
}
