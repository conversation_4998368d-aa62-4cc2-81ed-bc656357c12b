package com.example.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.LoginRequest;
import com.example.admin.dto.response.LoginResponse;
import com.example.admin.dto.response.PermissionResponse;
import com.example.admin.dto.response.RoleResponse;
import com.example.admin.entity.SysUser;
import com.example.admin.mapper.SysUserMapper;
import com.example.admin.service.AuthService;
import com.example.admin.service.CaptchaService;
import com.example.admin.service.SysPermissionService;
import com.example.admin.service.SysRoleService;
import com.example.admin.utils.JwtUtils;
import com.example.admin.utils.OnlineUserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final SysUserMapper sysUserMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CaptchaService captchaService;
    private final SysPermissionService sysPermissionService;
    private final SysRoleService sysRoleService;
    private final OnlineUserUtils onlineUserUtils;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();
        String captcha = loginRequest.getCaptcha();
        String captchaKey = loginRequest.getCaptchaKey();

        log.info("用户登录尝试: {}", username);

        // 验证验证码（如果提供了验证码）
        if (captcha != null && captchaKey != null) {
            if (!captchaService.validateCaptcha(captchaKey, captcha)) {
                throw new BusinessException("验证码错误");
            }
        }

        try {
            // 使用Spring Security进行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );

            // 认证成功，查询用户信息
            SysUser user = sysUserMapper.selectOne(
                    new LambdaQueryWrapper<SysUser>()
                            .eq(SysUser::getUsername, username)
                            .eq(SysUser::getDeleted, 0)
            );

            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 检查用户状态
            if (user.getStatus() == null || user.getStatus() == 0) {
                throw new BusinessException("用户已被禁用");
            }

            // 生成JWT令牌
            String token = jwtUtils.generateToken(username, user.getId());

            // 更新用户最后登录时间
            user.setLastLoginTime(LocalDateTime.now());
            sysUserMapper.updateById(user);

            // 将令牌存储到Redis（可选，用于令牌管理）
            String tokenKey = "jwt:token:" + user.getId();
            redisTemplate.opsForValue().set(tokenKey, token, jwtUtils.getExpiration(), TimeUnit.MILLISECONDS);

            // 记录用户在线状态
            onlineUserUtils.userLogin(user.getId(), username);

            log.info("用户 {} 登录成功", username);

            // 获取用户权限和角色
            List<String> permissions = getPermissionsByUserId(user.getId());
            List<String> roles = getRolesByUserId(user.getId());

            // 构建登录响应
            return LoginResponse.builder()
                    .token(token)
                    .tokenType("Bearer")
                    .expiresIn(jwtUtils.getExpiration())
                    .userId(user.getId())
                    .username(user.getUsername())
                    .realName(user.getRealName())
                    .nickname(user.getNickname())
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .avatar(user.getAvatar())
                    .permissions(permissions)
                    .roles(roles)
                    .build();

        } catch (AuthenticationException e) {
            log.error("用户 {} 登录失败: {}", username, e.getMessage());
            throw new BusinessException("用户名或密码错误");
        } catch (Exception e) {
            log.error("用户 {} 登录过程中发生异常: {}", username, e.getMessage());
            throw new BusinessException("登录失败，请稍后重试");
        }
    }

    /**
     * 用户登出
     *
     * @param token JWT令牌
     */
    @Override
    public void logout(String token) {
        try {
            if (jwtUtils.validateToken(token)) {
                Long userId = jwtUtils.getUserIdFromToken(token);
                String username = jwtUtils.getUsernameFromToken(token);

                // 从Redis中删除令牌
                String tokenKey = "jwt:token:" + userId;
                redisTemplate.delete(tokenKey);

                // 将令牌加入黑名单（可选）
                String blacklistKey = "jwt:blacklist:" + token;
                redisTemplate.opsForValue().set(blacklistKey, "1", jwtUtils.getExpiration(), TimeUnit.MILLISECONDS);

                // 移除用户在线状态
                onlineUserUtils.userLogout(userId, username);

                log.info("用户 {} 登出成功", username);
            }
        } catch (Exception e) {
            log.error("用户登出过程中发生异常: {}", e.getMessage());
        }
    }

    /**
     * 刷新令牌
     *
     * @param token 当前令牌
     * @return 新的令牌
     */
    @Override
    public String refreshToken(String token) {
        try {
            if (jwtUtils.validateToken(token)) {
                String username = jwtUtils.getUsernameFromToken(token);
                Long userId = jwtUtils.getUserIdFromToken(token);

                // 生成新的令牌
                String newToken = jwtUtils.generateToken(username, userId);

                // 更新Redis中的令牌
                String tokenKey = "jwt:token:" + userId;
                redisTemplate.opsForValue().set(tokenKey, newToken, jwtUtils.getExpiration(), TimeUnit.MILLISECONDS);

                // 将旧令牌加入黑名单
                String blacklistKey = "jwt:blacklist:" + token;
                redisTemplate.opsForValue().set(blacklistKey, "1", jwtUtils.getExpiration(), TimeUnit.MILLISECONDS);

                log.info("用户 {} 令牌刷新成功", username);
                return newToken;
            } else {
                throw new BusinessException("令牌无效");
            }
        } catch (Exception e) {
            log.error("令牌刷新过程中发生异常: {}", e.getMessage());
            throw new BusinessException("令牌刷新失败");
        }
    }

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限编码列表
     */
    private List<String> getPermissionsByUserId(Long userId) {
        try {
            log.info("开始获取用户权限，用户ID: {}", userId);

            // 先获取用户的角色列表
            List<RoleResponse> roles = sysRoleService.getUserRoles(userId);
            log.info("获取到用户角色数量: {}", roles.size());

            if (roles.isEmpty()) {
                log.warn("用户ID: {} 没有分配任何角色", userId);
                return Arrays.asList("user:read"); // 返回默认权限
            }

            // 根据角色获取权限列表
            Set<String> permissionCodes = new HashSet<>();
            for (RoleResponse role : roles) {
                log.debug("处理角色: {} - {}", role.getRoleCode(), role.getRoleName());
                List<PermissionResponse> rolePermissions = sysPermissionService.getPermissionsByRoleId(role.getId());
                log.debug("角色 {} 拥有权限数量: {}", role.getRoleCode(), rolePermissions.size());

                for (PermissionResponse permission : rolePermissions) {
                    log.debug("权限: {} - {}", permission.getPermissionCode(), permission.getPermissionName());
                    permissionCodes.add(permission.getPermissionCode());
                }
            }

            List<String> result = new ArrayList<>(permissionCodes);
            log.info("最终权限列表: {}", result);
            return result;
        } catch (Exception e) {
            log.error("获取用户权限失败: {}", e.getMessage(), e);
            return Arrays.asList("user:read"); // 返回默认权限
        }
    }

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色编码列表
     */
    private List<String> getRolesByUserId(Long userId) {
        try {
            log.info("开始获取用户角色，用户ID: {}", userId);
            List<RoleResponse> roles = sysRoleService.getUserRoles(userId);
            log.info("获取到角色数量: {}", roles.size());

            List<String> roleCodes = roles.stream()
                    .map(role -> {
                        log.debug("角色: {} - {}", role.getRoleCode(), role.getRoleName());
                        return role.getRoleCode();
                    })
                    .collect(Collectors.toList());

            log.info("最终角色列表: {}", roleCodes);
            return roleCodes;
        } catch (Exception e) {
            log.error("获取用户角色失败: {}", e.getMessage(), e);
            return Arrays.asList("USER"); // 返回默认角色
        }
    }
}
