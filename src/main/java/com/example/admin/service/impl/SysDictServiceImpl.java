package com.example.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.admin.common.exception.BusinessException;
import com.example.admin.dto.request.*;
import com.example.admin.dto.response.DictItemResponse;
import com.example.admin.dto.response.DictResponse;
import com.example.admin.entity.SysDict;
import com.example.admin.entity.SysDictItem;
import com.example.admin.mapper.SysDictItemMapper;
import com.example.admin.mapper.SysDictMapper;
import com.example.admin.service.SysDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据字典服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    private final SysDictMapper sysDictMapper;
    private final SysDictItemMapper sysDictItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictResponse createDict(DictCreateRequest request) {
        log.info("创建数据字典: {}", request.getDictCode());

        // 检查字典编码是否已存在
        if (existsDictCode(request.getDictCode(), null)) {
            throw new BusinessException("字典编码已存在: " + request.getDictCode());
        }

        // 创建字典实体
        SysDict dict = new SysDict();
        BeanUtil.copyProperties(request, dict);

        // 保存字典
        save(dict);

        log.info("数据字典创建成功: {}", dict.getId());
        return convertToResponse(dict);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictResponse updateDict(DictUpdateRequest request) {
        log.info("更新数据字典: {}", request.getId());

        // 检查字典是否存在
        SysDict existingDict = getById(request.getId());
        if (existingDict == null) {
            throw new BusinessException("字典不存在");
        }

        // 检查字典编码是否已被其他字典使用
        if (existsDictCode(request.getDictCode(), request.getId())) {
            throw new BusinessException("字典编码已存在: " + request.getDictCode());
        }

        // 更新字典
        BeanUtil.copyProperties(request, existingDict, "createTime", "createBy");
        updateById(existingDict);

        log.info("数据字典更新成功: {}", existingDict.getId());
        return convertToResponse(existingDict);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDict(Long id) {
        log.info("删除数据字典: {}", id);

        SysDict dict = getById(id);
        if (dict == null) {
            throw new BusinessException("字典不存在");
        }

        // 删除字典项
        sysDictItemMapper.deleteByDictId(id);
        
        // 删除字典
        removeById(id);
        
        log.info("数据字典删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDicts(List<Long> ids) {
        log.info("批量删除数据字典: {}", ids);

        // 删除所有相关字典项
        for (Long id : ids) {
            sysDictItemMapper.deleteByDictId(id);
        }
        
        // 删除字典
        removeByIds(ids);
        
        log.info("批量删除数据字典成功，数量: {}", ids.size());
    }

    @Override
    public DictResponse getDictById(Long id) {
        SysDict dict = getById(id);
        if (dict == null) {
            throw new BusinessException("字典不存在");
        }
        
        DictResponse response = convertToResponse(dict);
        // 获取字典项
        List<DictItemResponse> items = getDictItemsByDictId(id);
        response.setItems(items);
        
        return response;
    }

    @Override
    public DictResponse getDictByCode(String dictCode) {
        SysDict dict = sysDictMapper.selectByDictCode(dictCode);
        if (dict == null) {
            throw new BusinessException("字典不存在: " + dictCode);
        }
        
        DictResponse response = convertToResponse(dict);
        // 获取字典项
        List<DictItemResponse> items = getDictItemsByDictCode(dictCode);
        response.setItems(items);
        
        return response;
    }

    @Override
    public Page<DictResponse> pageDicts(DictQueryRequest request) {
        Page<SysDict> page = new Page<>(request.getCurrent(), request.getSize());
        
        LambdaQueryWrapper<SysDict> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(request.getDictCode()), SysDict::getDictCode, request.getDictCode())
               .like(StrUtil.isNotBlank(request.getDictName()), SysDict::getDictName, request.getDictName())
               .eq(request.getStatus() != null, SysDict::getStatus, request.getStatus())
               .orderByDesc(SysDict::getCreateTime);

        Page<SysDict> dictPage = page(page, wrapper);
        
        Page<DictResponse> responsePage = new Page<>();
        BeanUtil.copyProperties(dictPage, responsePage, "records");
        responsePage.setRecords(dictPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList()));
        
        return responsePage;
    }

    @Override
    public List<DictResponse> getEnabledDicts() {
        List<SysDict> dicts = sysDictMapper.selectEnabledDicts();
        return dicts.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsDictCode(String dictCode, Long excludeId) {
        LambdaQueryWrapper<SysDict> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDict::getDictCode, dictCode);
        if (excludeId != null) {
            wrapper.ne(SysDict::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    // 字典项相关方法

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictItemResponse createDictItem(DictItemCreateRequest request) {
        log.info("创建字典项: {}-{}", request.getDictId(), request.getItemCode());

        // 检查字典是否存在
        SysDict dict = getById(request.getDictId());
        if (dict == null) {
            throw new BusinessException("字典不存在");
        }

        // 检查字典项编码是否已存在
        if (existsDictItemCode(request.getDictId(), request.getItemCode(), null)) {
            throw new BusinessException("字典项编码已存在: " + request.getItemCode());
        }

        // 创建字典项实体
        SysDictItem dictItem = new SysDictItem();
        BeanUtil.copyProperties(request, dictItem);

        // 保存字典项
        sysDictItemMapper.insert(dictItem);

        log.info("字典项创建成功: {}", dictItem.getId());
        return convertToItemResponse(dictItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictItemResponse updateDictItem(DictItemUpdateRequest request) {
        log.info("更新字典项: {}", request.getId());

        // 检查字典项是否存在
        SysDictItem existingItem = sysDictItemMapper.selectById(request.getId());
        if (existingItem == null) {
            throw new BusinessException("字典项不存在");
        }

        // 检查字典是否存在
        SysDict dict = getById(request.getDictId());
        if (dict == null) {
            throw new BusinessException("字典不存在");
        }

        // 检查字典项编码是否已被其他字典项使用
        if (existsDictItemCode(request.getDictId(), request.getItemCode(), request.getId())) {
            throw new BusinessException("字典项编码已存在: " + request.getItemCode());
        }

        // 更新字典项
        BeanUtil.copyProperties(request, existingItem, "createTime", "createBy");
        sysDictItemMapper.updateById(existingItem);

        log.info("字典项更新成功: {}", existingItem.getId());
        return convertToItemResponse(existingItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDictItem(Long id) {
        log.info("删除字典项: {}", id);

        SysDictItem dictItem = sysDictItemMapper.selectById(id);
        if (dictItem == null) {
            throw new BusinessException("字典项不存在");
        }

        sysDictItemMapper.deleteById(id);
        log.info("字典项删除成功: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDictItems(List<Long> ids) {
        log.info("批量删除字典项: {}", ids);

        for (Long id : ids) {
            sysDictItemMapper.deleteById(id);
        }

        log.info("批量删除字典项成功，数量: {}", ids.size());
    }

    @Override
    public List<DictItemResponse> getDictItemsByDictId(Long dictId) {
        List<SysDictItem> items = sysDictItemMapper.selectEnabledByDictId(dictId);
        return items.stream()
                .map(this::convertToItemResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<DictItemResponse> getDictItemsByDictCode(String dictCode) {
        List<SysDictItem> items = sysDictItemMapper.selectEnabledByDictCode(dictCode);
        return items.stream()
                .map(this::convertToItemResponse)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsDictItemCode(Long dictId, String itemCode, Long excludeId) {
        LambdaQueryWrapper<SysDictItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDictItem::getDictId, dictId)
               .eq(SysDictItem::getItemCode, itemCode);
        if (excludeId != null) {
            wrapper.ne(SysDictItem::getId, excludeId);
        }
        return sysDictItemMapper.selectCount(wrapper) > 0;
    }

    /**
     * 转换为响应DTO
     */
    private DictResponse convertToResponse(SysDict dict) {
        DictResponse response = new DictResponse();
        BeanUtil.copyProperties(dict, response);
        return response;
    }

    /**
     * 转换为字典项响应DTO
     */
    private DictItemResponse convertToItemResponse(SysDictItem dictItem) {
        DictItemResponse response = new DictItemResponse();
        BeanUtil.copyProperties(dictItem, response);
        return response;
    }
}
