package com.example.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.admin.dto.request.PermissionCreateRequest;
import com.example.admin.dto.request.PermissionQueryRequest;
import com.example.admin.dto.request.PermissionUpdateRequest;
import com.example.admin.dto.response.PermissionResponse;
import com.example.admin.entity.SysPermission;

import java.util.List;

/**
 * 系统权限服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysPermissionService {

    /**
     * 创建权限
     *
     * @param request 权限创建请求
     * @return 权限响应
     */
    PermissionResponse createPermission(PermissionCreateRequest request);

    /**
     * 更新权限
     *
     * @param request 权限更新请求
     * @return 权限响应
     */
    PermissionResponse updatePermission(PermissionUpdateRequest request);

    /**
     * 删除权限
     *
     * @param id 权限ID
     */
    void deletePermission(Long id);

    /**
     * 批量删除权限
     *
     * @param ids 权限ID列表
     */
    void deletePermissions(List<Long> ids);

    /**
     * 根据ID获取权限
     *
     * @param id 权限ID
     * @return 权限响应
     */
    PermissionResponse getPermissionById(Long id);

    /**
     * 根据权限编码获取权限
     *
     * @param permissionCode 权限编码
     * @return 权限实体
     */
    SysPermission getPermissionByCode(String permissionCode);

    /**
     * 分页查询权限
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<PermissionResponse> getPermissionPage(PermissionQueryRequest request);

    /**
     * 获取所有权限列表
     *
     * @param request 查询请求
     * @return 权限列表
     */
    List<PermissionResponse> getPermissionList(PermissionQueryRequest request);

    /**
     * 获取权限树形结构
     *
     * @param request 查询请求
     * @return 权限树形结构
     */
    List<PermissionResponse> getPermissionTree(PermissionQueryRequest request);

    /**
     * 获取权限树形结构（所有启用的权限）
     *
     * @return 权限树形结构
     */
    List<PermissionResponse> getPermissionTree();

    /**
     * 启用权限
     *
     * @param id 权限ID
     */
    void enablePermission(Long id);

    /**
     * 禁用权限
     *
     * @param id 权限ID
     */
    void disablePermission(Long id);

    /**
     * 批量启用权限
     *
     * @param ids 权限ID列表
     */
    void enablePermissions(List<Long> ids);

    /**
     * 批量禁用权限
     *
     * @param ids 权限ID列表
     */
    void disablePermissions(List<Long> ids);

    /**
     * 根据角色ID获取权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionResponse> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<PermissionResponse> getPermissionsByUserId(Long userId);

    /**
     * 根据父权限ID获取子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<PermissionResponse> getChildrenByParentId(Long parentId);

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @return 是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @return 是否存在
     */
    boolean existsByPermissionName(String permissionName);

    /**
     * 检查权限编码是否存在（排除指定权限）
     *
     * @param permissionCode 权限编码
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean existsByPermissionCode(String permissionCode, Long excludeId);

    /**
     * 检查权限名称是否存在（排除指定权限）
     *
     * @param permissionName 权限名称
     * @param excludeId 排除的权限ID
     * @return 是否存在
     */
    boolean existsByPermissionName(String permissionName, Long excludeId);

    /**
     * 检查是否有子权限
     *
     * @param parentId 父权限ID
     * @return 是否有子权限
     */
    boolean hasChildren(Long parentId);

    /**
     * 导出权限数据
     *
     * @param request 查询请求
     * @return 导出数据
     */
    List<PermissionResponse> exportPermissions(PermissionQueryRequest request);
}
