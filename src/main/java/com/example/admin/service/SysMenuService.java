package com.example.admin.service;

import com.example.admin.dto.request.MenuCreateRequest;
import com.example.admin.dto.request.MenuQueryRequest;
import com.example.admin.dto.request.MenuUpdateRequest;
import com.example.admin.dto.response.MenuResponse;
import com.example.admin.dto.response.MenuTreeResponse;

import java.util.List;

/**
 * 系统菜单服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SysMenuService {

    /**
     * 创建菜单
     * 
     * @param request 菜单创建请求
     * @return 菜单响应
     */
    MenuResponse createMenu(MenuCreateRequest request);

    /**
     * 更新菜单
     * 
     * @param request 菜单更新请求
     * @return 菜单响应
     */
    MenuResponse updateMenu(MenuUpdateRequest request);

    /**
     * 删除菜单
     * 
     * @param id 菜单ID
     */
    void deleteMenu(Long id);

    /**
     * 批量删除菜单
     * 
     * @param ids 菜单ID列表
     */
    void batchDeleteMenus(List<Long> ids);

    /**
     * 根据ID获取菜单
     * 
     * @param id 菜单ID
     * @return 菜单响应
     */
    MenuResponse getMenuById(Long id);

    /**
     * 获取菜单列表
     * 
     * @param request 查询请求
     * @return 菜单列表
     */
    List<MenuResponse> getMenuList(MenuQueryRequest request);

    /**
     * 获取菜单树形结构
     * 
     * @return 菜单树形结构
     */
    List<MenuResponse> getMenuTree();

    /**
     * 根据用户ID获取菜单树形结构
     * 
     * @param userId 用户ID
     * @return 菜单树形结构
     */
    List<MenuTreeResponse> getMenuTreeByUserId(Long userId);

    /**
     * 根据角色ID获取菜单树形结构
     * 
     * @param roleId 角色ID
     * @return 菜单树形结构
     */
    List<MenuTreeResponse> getMenuTreeByRoleId(Long roleId);

    /**
     * 启用菜单
     * 
     * @param id 菜单ID
     */
    void enableMenu(Long id);

    /**
     * 禁用菜单
     * 
     * @param id 菜单ID
     */
    void disableMenu(Long id);

    /**
     * 批量启用菜单
     * 
     * @param ids 菜单ID列表
     */
    void batchEnableMenus(List<Long> ids);

    /**
     * 批量禁用菜单
     * 
     * @param ids 菜单ID列表
     */
    void batchDisableMenus(List<Long> ids);

    /**
     * 根据父菜单ID获取子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<MenuResponse> getChildrenByParentId(Long parentId);

    /**
     * 检查菜单名称是否存在
     * 
     * @param menuName 菜单名称
     * @return 是否存在
     */
    boolean existsByMenuName(String menuName);

    /**
     * 检查菜单名称是否存在（排除指定ID）
     * 
     * @param menuName 菜单名称
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    boolean existsByMenuName(String menuName, Long excludeId);

    /**
     * 检查路由路径是否存在
     * 
     * @param path 路由路径
     * @return 是否存在
     */
    boolean existsByPath(String path);

    /**
     * 检查路由路径是否存在（排除指定ID）
     * 
     * @param path 路由路径
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    boolean existsByPath(String path, Long excludeId);

    /**
     * 检查权限标识是否存在
     * 
     * @param perms 权限标识
     * @return 是否存在
     */
    boolean existsByPerms(String perms);

    /**
     * 检查权限标识是否存在（排除指定ID）
     * 
     * @param perms 权限标识
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    boolean existsByPerms(String perms, Long excludeId);

    /**
     * 检查是否有子菜单
     * 
     * @param parentId 父菜单ID
     * @return 是否有子菜单
     */
    boolean hasChildren(Long parentId);

    /**
     * 根据用户ID获取权限标识列表
     * 
     * @param userId 用户ID
     * @return 权限标识列表
     */
    List<String> getPermsByUserId(Long userId);

    /**
     * 根据角色ID获取权限标识列表
     * 
     * @param roleId 角色ID
     * @return 权限标识列表
     */
    List<String> getPermsByRoleId(Long roleId);

    /**
     * 获取所有启用的菜单（用于路由生成）
     * 
     * @return 菜单树形结构
     */
    List<MenuTreeResponse> getEnabledMenuTree();
}
