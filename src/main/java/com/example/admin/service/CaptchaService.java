package com.example.admin.service;

import java.util.Map;

/**
 * 验证码服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface CaptchaService {

    /**
     * 生成验证码
     *
     * @return 验证码信息（包含图片base64和uuid）
     */
    Map<String, String> generateCaptcha();

    /**
     * 验证验证码
     *
     * @param uuid 验证码唯一标识
     * @param code 用户输入的验证码
     * @return 验证结果
     */
    boolean validateCaptcha(String uuid, String code);

    /**
     * 删除验证码
     *
     * @param uuid 验证码唯一标识
     */
    void removeCaptcha(String uuid);
}
