package com.example.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.admin.entity.SysLogDetail;

import java.util.List;

/**
 * 系统日志详情服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface SysLogDetailService extends IService<SysLogDetail> {

    /**
     * 根据日志ID查询详情列表
     */
    List<SysLogDetail> getDetailsByLogId(Long logId);

    /**
     * 批量保存日志详情
     */
    boolean batchSaveDetails(List<SysLogDetail> details);

    /**
     * 根据日志ID删除详情
     */
    boolean deleteByLogId(Long logId);

    /**
     * 根据日志ID批量删除详情
     */
    boolean deleteByLogIds(List<Long> logIds);

    /**
     * 查询敏感字段详情
     */
    List<SysLogDetail> getSensitiveDetails(String startTime, String endTime);

    /**
     * 更新脱敏状态
     */
    boolean updateMaskedStatus(Long id, String maskedValue);

    /**
     * 创建日志详情
     */
    boolean createLogDetail(Long logId, String fieldName, String fieldValue, 
                           String fieldType, String fieldDesc, Boolean isSensitive);

}