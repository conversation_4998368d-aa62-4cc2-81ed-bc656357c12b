package com.example.admin.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;
import org.springframework.web.socket.messaging.SessionUnsubscribeEvent;

import java.util.Map;

/**
 * WebSocket事件处理器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class WebSocketEventHandler {

    /**
     * 处理WebSocket连接事件
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();
            
            // 获取用户信息
            Map<String, Object> sessionAttributes = headerAccessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket客户端连接成功: {}, 用户: {}", sessionId, username);
            
            // 这里可以调用WebSocketLogService处理连接事件
            // webSocketLogService.handleUserConnect(sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket连接事件失败", e);
        }
    }

    /**
     * 处理WebSocket断开连接事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();
            
            // 获取用户信息
            Map<String, Object> sessionAttributes = headerAccessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket客户端断开连接: {}, 用户: {}", sessionId, username);
            
            // 这里可以调用WebSocketLogService处理断开连接事件
            // webSocketLogService.handleUserDisconnect(sessionId, username);
            
        } catch (Exception e) {
            log.error("处理WebSocket断开连接事件失败", e);
        }
    }

    /**
     * 处理WebSocket订阅事件
     */
    @EventListener
    public void handleWebSocketSubscribeListener(SessionSubscribeEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();
            String destination = headerAccessor.getDestination();
            
            // 获取用户信息
            Map<String, Object> sessionAttributes = headerAccessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket客户端订阅: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
            // 这里可以调用WebSocketLogService处理订阅事件
            // webSocketLogService.handleUserSubscribe(sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket订阅事件失败", e);
        }
    }

    /**
     * 处理WebSocket取消订阅事件
     */
    @EventListener
    public void handleWebSocketUnsubscribeListener(SessionUnsubscribeEvent event) {
        try {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
            String sessionId = headerAccessor.getSessionId();
            String destination = headerAccessor.getDestination();
            
            // 获取用户信息
            Map<String, Object> sessionAttributes = headerAccessor.getSessionAttributes();
            String username = (String) sessionAttributes.get("username");
            
            log.info("WebSocket客户端取消订阅: {}, 用户: {}, 目的地: {}", sessionId, username, destination);
            
            // 这里可以调用WebSocketLogService处理取消订阅事件
            // webSocketLogService.handleUserUnsubscribe(sessionId, username, destination);
            
        } catch (Exception e) {
            log.error("处理WebSocket取消订阅事件失败", e);
        }
    }

}