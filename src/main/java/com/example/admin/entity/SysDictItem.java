package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 字典项实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict_item")
public class SysDictItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 字典ID
     */
    @NotNull(message = "字典ID不能为空")
    @TableField("dict_id")
    private Long dictId;

    /**
     * 字典项编码
     */
    @NotBlank(message = "字典项编码不能为空")
    @Size(max = 50, message = "字典项编码长度不能超过50个字符")
    @TableField("item_code")
    private String itemCode;

    /**
     * 字典项名称
     */
    @NotBlank(message = "字典项名称不能为空")
    @Size(max = 100, message = "字典项名称长度不能超过100个字符")
    @TableField("item_name")
    private String itemName;

    /**
     * 字典项值
     */
    @Size(max = 200, message = "字典项值长度不能超过200个字符")
    @TableField("item_value")
    private String itemValue;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder = 0;

    /**
     * 状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status = 1;
}
