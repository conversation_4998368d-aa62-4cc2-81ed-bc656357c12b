package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统日志统计实体类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log_statistics")
public class SysLogStatistics extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 统计日期
     */
    private LocalDate statDate;
    
    /**
     * 统计类型 (DAILY, WEEKLY, MONTHLY)
     */
    private String statType;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 总日志数量
     */
    private Long totalLogCount;
    
    /**
     * 成功日志数量
     */
    private Long successLogCount;
    
    /**
     * 失败日志数量
     */
    private Long failedLogCount;
    
    /**
     * 异常日志数量
     */
    private Long exceptionLogCount;
    
    /**
     * 性能日志数量
     */
    private Long performanceLogCount;
    
    /**
     * 平均执行时间 (毫秒)
     */
    private BigDecimal avgExecutionTime;
    
    /**
     * 最小执行时间 (毫秒)
     */
    private Long minExecutionTime;
    
    /**
     * 最大执行时间 (毫秒)
     */
    private Long maxExecutionTime;
    
    /**
     * 总执行时间 (毫秒)
     */
    private Long totalExecutionTime;
    
    /**
     * UV (独立用户数)
     */
    private Long uvCount;
    
    /**
     * PV (页面访问量)
     */
    private Long pvCount;
    
    /**
     * IP数量
     */
    private Long ipCount;
    
    /**
     * 用户数量
     */
    private Long userCount;
    
    /**
     * 慢查询数量 (执行时间 > 2秒)
     */
    private Long slowQueryCount;
    
    /**
     * 错误率 (%)
     */
    private BigDecimal errorRate;
    
    /**
     * 成功率 (%)
     */
    private BigDecimal successRate;
    
    /**
     * 异常率 (%)
     */
    private BigDecimal exceptionRate;
    
    /**
     * 响应时间分布 (JSON格式)
     */
    private String responseTimeDistribution;
    
    /**
     * 用户活跃度分布 (JSON格式)
     */
    private String userActivityDistribution;
    
    /**
     * 操作频率分布 (JSON格式)
     */
    private String operationFrequencyDistribution;
    
    /**
     * 地理位置分布 (JSON格式)
     */
    private String geoLocationDistribution;
    
    /**
     * 设备类型分布 (JSON格式)
     */
    private String deviceTypeDistribution;
    
    /**
     * 浏览器类型分布 (JSON格式)
     */
    private String browserTypeDistribution;
    
    /**
     * 操作系统分布 (JSON格式)
     */
    private String osDistribution;
    
    /**
     * 统计扩展信息 (JSON格式)
     */
    private String extInfo;
    
}