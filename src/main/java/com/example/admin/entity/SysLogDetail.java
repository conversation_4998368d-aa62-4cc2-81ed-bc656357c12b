package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统日志详情实体类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log_detail")
public class SysLogDetail extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 关联的日志ID
     */
    private Long logId;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 字段值
     */
    private String fieldValue;
    
    /**
     * 字段类型 (STRING, NUMBER, BOOLEAN, JSON)
     */
    private String fieldType;
    
    /**
     * 字段描述
     */
    private String fieldDesc;
    
    /**
     * 是否敏感信息
     */
    private Boolean isSensitive;
    
    /**
     * 是否已脱敏
     */
    private Boolean isMasked;
    
    /**
     * 脱敏后的值
     */
    private String maskedValue;
    
    /**
     * 字段状态 (ACTIVE, INACTIVE)
     */
    private String fieldStatus;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
}