package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 用户个人资料实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_profile")
public class SysUserProfile extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL
     */
    @Size(max = 255, message = "头像URL长度不能超过255个字符")
    @TableField("avatar")
    private String avatar;

    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    @TableField("bio")
    private String bio;

    /**
     * 生日
     */
    @TableField("birthday")
    private LocalDate birthday;

    /**
     * 性别：0-女，1-男，2-未知
     */
    @Min(value = 0, message = "性别值无效")
    @Max(value = 2, message = "性别值无效")
    @TableField("gender")
    private Integer gender;

    /**
     * 所在地
     */
    @Size(max = 100, message = "所在地长度不能超过100个字符")
    @TableField("location")
    private String location;

    /**
     * 个人网站
     */
    @Size(max = 255, message = "个人网站长度不能超过255个字符")
    @TableField("website")
    private String website;

    /**
     * GitHub地址
     */
    @Size(max = 255, message = "GitHub地址长度不能超过255个字符")
    @TableField("github")
    private String github;

    /**
     * Twitter地址
     */
    @Size(max = 255, message = "Twitter地址长度不能超过255个字符")
    @TableField("twitter")
    private String twitter;

    /**
     * LinkedIn地址
     */
    @Size(max = 255, message = "LinkedIn地址长度不能超过255个字符")
    @TableField("linkedin")
    private String linkedin;

    /**
     * 时区
     */
    @Size(max = 50, message = "时区长度不能超过50个字符")
    @TableField("timezone")
    private String timezone;

    /**
     * 语言偏好
     */
    @Size(max = 10, message = "语言偏好长度不能超过10个字符")
    @TableField("language")
    private String language;

    /**
     * 主题偏好：light-浅色，dark-深色
     */
    @Size(max = 20, message = "主题偏好长度不能超过20个字符")
    @TableField("theme")
    private String theme;

    /**
     * 邮件通知：0-关闭，1-开启
     */
    @TableField("email_notifications")
    private Integer emailNotifications;

    /**
     * 短信通知：0-关闭，1-开启
     */
    @TableField("sms_notifications")
    private Integer smsNotifications;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private String lastLoginTime;

    /**
     * 最后登录IP
     */
    @Size(max = 50, message = "最后登录IP长度不能超过50个字符")
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 最后登录设备
     */
    @Size(max = 100, message = "最后登录设备长度不能超过100个字符")
    @TableField("last_login_device")
    private String lastLoginDevice;

    /**
     * 登录次数
     */
    @Min(value = 0, message = "登录次数不能小于0")
    @TableField("login_count")
    private Integer loginCount;
}
