package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;

/**
 * 用户偏好设置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_preference")
public class SysUserPreference extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 偏好键
     */
    @NotBlank(message = "偏好键不能为空")
    @Size(max = 100, message = "偏好键长度不能超过100个字符")
    @TableField("preference_key")
    private String preferenceKey;

    /**
     * 偏好值
     */
    @TableField("preference_value")
    private String preferenceValue;

    /**
     * 偏好描述
     */
    @Size(max = 255, message = "偏好描述长度不能超过255个字符")
    @TableField("description")
    private String description;

    /**
     * 数据类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔值，JSON-JSON对象
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 是否系统默认：0-否，1-是
     */
    @TableField("is_system")
    private Integer isSystem;
}