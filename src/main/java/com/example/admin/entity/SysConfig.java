package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统配置实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_config")
public class SysConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 配置键
     */
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置类型（STRING、NUMBER、BOOLEAN、JSON）
     */
    @Size(max = 20, message = "配置类型长度不能超过20个字符")
    @TableField("config_type")
    private String configType = "STRING";

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 是否加密（0：否，1：是）
     */
    @TableField("is_encrypted")
    private Integer isEncrypted = 0;

    /**
     * 是否系统配置（0：否，1：是）
     */
    @TableField("is_system")
    private Integer isSystem = 0;

    /**
     * 状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status = 1;
}
