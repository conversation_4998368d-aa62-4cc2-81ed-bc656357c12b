package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统日志实体类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log")
public class SysLog extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 日志类型 (BIZ_LOG, EXCEPTION_LOG, PERFORMANCE_LOG)
     */
    private String logType;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 操作描述
     */
    private String description;
    
    /**
     * 请求URI
     */
    private String requestUri;
    
    /**
     * 请求方法 (GET, POST, PUT, DELETE)
     */
    private String method;
    
    /**
     * 请求参数
     */
    private String requestParams;
    
    /**
     * 响应结果
     */
    private String response;
    
    /**
     * 执行时间 (毫秒)
     */
    private Long executionTime;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 操作状态 (true:成功, false:失败)
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 浏览器类型
     */
    private String browser;
    
    /**
     * 操作系统
     */
    private String os;
    
    /**
     * 设备信息
     */
    private String deviceInfo;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 请求来源
     */
    private String referer;
    
    /**
     * 异常类型
     */
    private String exceptionType;
    
    /**
     * 异常堆栈
     */
    private String stackTrace;
    
    /**
     * 日志级别 (INFO, WARN, ERROR, DEBUG)
     */
    private String level;
    
    /**
     * 线程名称
     */
    private String threadName;
    
      
    /**
     * 扩展字段 (JSON格式)
     */
    private String extInfo;
    
}