package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 用户个人操作日志实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_log")
public class SysUserLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 日志类型
     */
    @Size(max = 50, message = "日志类型长度不能超过50个字符")
    @TableField("log_type")
    private String logType;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作动作
     */
    @Size(max = 50, message = "操作动作长度不能超过50个字符")
    @TableField("action")
    private String action;

    /**
     * 操作模块
     */
    @Size(max = 50, message = "操作模块长度不能超过50个字符")
    @TableField("module")
    private String module;

    /**
     * 响应数据
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 操作状态（0：失败，1：成功）
     */
    @TableField("status")
    private Integer status;

    /**
     * 操作描述
     */
    @Size(max = 200, message = "操作描述长度不能超过200个字符")
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 请求方法
     */
    @Size(max = 10, message = "请求方法长度不能超过10个字符")
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求URL
     */
    @Size(max = 200, message = "请求URL长度不能超过200个字符")
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求参数
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 响应结果（SUCCESS/FAIL）
     */
    @Size(max = 10, message = "响应结果长度不能超过10个字符")
    @TableField("response_result")
    private String responseResult;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * IP地址
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 操作地点
     */
    @Size(max = 100, message = "操作地点长度不能超过100个字符")
    @TableField("location")
    private String location;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 设备ID
     */
    @Size(max = 100, message = "设备ID长度不能超过100个字符")
    @TableField("device_id")
    private String deviceId;

    /**
     * 执行时间（毫秒）
     */
    @TableField("execution_time")
    private Long executionTime;
}
