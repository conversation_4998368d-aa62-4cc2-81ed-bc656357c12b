package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;

/**
 * 用户密码修改记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_password_history")
public class SysUserPasswordHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 旧密码（加密）
     */
    @NotBlank(message = "旧密码不能为空")
    @TableField("old_password")
    private String oldPassword;

    /**
     * 新密码（加密）
     */
    @NotBlank(message = "新密码不能为空")
    @TableField("new_password")
    private String newPassword;

    /**
     * 修改类型：manual-手动修改，reset-重置，force-强制修改
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 修改原因
     */
    @Size(max = 255, message = "修改原因长度不能超过255个字符")
    @TableField("change_reason")
    private String changeReason;

    /**
     * IP地址
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 设备ID
     */
    @Size(max = 100, message = "设备ID长度不能超过100个字符")
    @TableField("device_id")
    private String deviceId;
}