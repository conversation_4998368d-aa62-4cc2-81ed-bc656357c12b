package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统日志配置实体类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_log_config")
public class SysLogConfig extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 配置名称
     */
    private String configName;
    
    /**
     * 配置代码
     */
    private String configCode;
    
    /**
     * 配置类型 (MODULE, OPERATION, LEVEL, ALERT)
     */
    private String configType;
    
    /**
     * 模块名称
     */
    private String moduleName;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 日志级别 (DEBUG, INFO, WARN, ERROR)
     */
    private String logLevel;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 是否记录请求参数
     */
    private Boolean recordRequest;
    
    /**
     * 是否记录响应结果
     */
    private Boolean recordResponse;
    
    /**
     * 是否记录执行时间
     */
    private Boolean recordExecutionTime;
    
    /**
     * 是否记录异常信息
     */
    private Boolean recordException;
    
    /**
     * 是否记录用户信息
     */
    private Boolean recordUserInfo;
    
    /**
     * 是否记录请求信息
     */
    private Boolean recordRequestInfo;
    
        
    /**
     * 是否发送实时推送
     */
    private Boolean sendRealTimePush;
    
    /**
     * 是否启用性能告警
     */
    private Boolean enablePerformanceAlert;
    
    /**
     * 性能告警阈值 (毫秒)
     */
    private Long performanceAlertThreshold;
    
    /**
     * 是否启用异常告警
     */
    private Boolean enableExceptionAlert;
    
    /**
     * 异常告警类型
     */
    private String exceptionAlertTypes;
    
    /**
     * 是否启用统计报告
     */
    private Boolean enableStatistics;
    
    /**
     * 统计报告周期 (DAILY, WEEKLY, MONTHLY)
     */
    private String statisticsPeriod;
    
    /**
     * 日志保留天数
     */
    private Integer logRetentionDays;
    
    /**
     * 敏感字段列表 (JSON格式)
     */
    private String sensitiveFields;
    
    /**
     * 脱敏规则 (JSON格式)
     */
    private String maskingRules;
    
    /**
     * 过滤规则 (JSON格式)
     */
    private String filterRules;
    
    /**
     * 告警配置 (JSON格式)
     */
    private String alertConfig;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
}