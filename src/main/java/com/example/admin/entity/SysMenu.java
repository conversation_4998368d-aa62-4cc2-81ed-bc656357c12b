package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统菜单实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_menu")
public class SysMenu extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 2, max = 50, message = "菜单名称长度必须在2-50个字符之间")
    @TableField("menu_name")
    private String menuName;

    /**
     * 菜单标题（用于国际化）
     */
    @Size(max = 50, message = "菜单标题长度不能超过50个字符")
    @TableField("menu_title")
    private String menuTitle;

    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 菜单类型（1：目录，2：菜单，3：按钮）
     */
    @TableField("menu_type")
    private Integer menuType;

    /**
     * 路由路径
     */
    @Size(max = 200, message = "路由路径长度不能超过200个字符")
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    @TableField("component")
    private String component;

    /**
     * 路由参数
     */
    @Size(max = 200, message = "路由参数长度不能超过200个字符")
    @TableField("query")
    private String query;

    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    @TableField("icon")
    private String icon;

    /**
     * 是否外链（0：否，1：是）
     */
    @TableField("is_frame")
    private Integer isFrame;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    @TableField("is_cache")
    private Integer isCache;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    @TableField("visible")
    private Integer visible;

    /**
     * 状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 权限标识
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    @TableField("perms")
    private String perms;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否系统内置菜单（0：否，1：是）
     */
    @TableField("is_system")
    private Integer isSystem;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;
}
