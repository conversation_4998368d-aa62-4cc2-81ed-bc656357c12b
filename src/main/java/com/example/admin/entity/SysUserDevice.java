package com.example.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.admin.common.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户登录设备实体类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_device")
public class SysUserDevice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 设备唯一标识
     */
    @NotBlank(message = "设备ID不能为空")
    @Size(max = 100, message = "设备ID长度不能超过100个字符")
    @TableField("device_id")
    private String deviceId;

    /**
     * 设备名称
     */
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备类型（PC/Mobile/Tablet）
     */
    @Size(max = 20, message = "设备类型长度不能超过20个字符")
    @TableField("device_type")
    private String deviceType;

    /**
     * 操作系统
     */
    @Size(max = 50, message = "操作系统长度不能超过50个字符")
    @TableField("os_name")
    private String osName;

    /**
     * 浏览器名称
     */
    @Size(max = 50, message = "浏览器名称长度不能超过50个字符")
    @TableField("browser_name")
    private String browserName;

    /**
     * 浏览器版本
     */
    @Size(max = 20, message = "浏览器版本长度不能超过20个字符")
    @TableField("browser_version")
    private String browserVersion;

    /**
     * IP地址
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 登录地点
     */
    @Size(max = 100, message = "登录地点长度不能超过100个字符")
    @TableField("location")
    private String location;

    /**
     * 用户代理字符串
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 状态（0：离线，1：在线）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否当前设备（0：否，1：是）
     */
    @TableField("is_current")
    private Integer isCurrent;

    /**
     * 是否信任设备（0：否，1：是）
     */
    @TableField("is_trusted")
    private Integer isTrusted;
}
