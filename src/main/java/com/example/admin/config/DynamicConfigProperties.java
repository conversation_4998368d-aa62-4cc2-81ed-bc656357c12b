package com.example.admin.config;

import com.example.admin.service.ConfigCacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 动态配置属性类
 * 支持配置热更新
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Component
@RequiredArgsConstructor
public class DynamicConfigProperties {

    private final ConfigCacheService configCacheService;

    // 系统基础配置
    private static final String SYSTEM_NAME = "system.name";
    private static final String SYSTEM_VERSION = "system.version";
    private static final String SYSTEM_DESCRIPTION = "system.description";
    private static final String SYSTEM_LOGO = "system.logo";
    private static final String SYSTEM_COPYRIGHT = "system.copyright";

    // 安全配置
    private static final String SECURITY_PASSWORD_MIN_LENGTH = "security.password.minLength";
    private static final String SECURITY_PASSWORD_REQUIRE_SPECIAL = "security.password.requireSpecial";
    private static final String SECURITY_LOGIN_FAIL_MAX_COUNT = "security.login.failMaxCount";
    private static final String SECURITY_LOGIN_LOCK_TIME = "security.login.lockTime";
    private static final String SECURITY_SESSION_TIMEOUT = "security.session.timeout";

    // 文件上传配置
    private static final String FILE_UPLOAD_MAX_SIZE = "file.upload.maxSize";
    private static final String FILE_UPLOAD_ALLOWED_TYPES = "file.upload.allowedTypes";
    private static final String FILE_UPLOAD_PATH = "file.upload.path";

    // 邮件配置
    private static final String MAIL_HOST = "mail.host";
    private static final String MAIL_PORT = "mail.port";
    private static final String MAIL_USERNAME = "mail.username";
    private static final String MAIL_PASSWORD = "mail.password";
    private static final String MAIL_FROM = "mail.from";

    /**
     * 获取系统名称
     */
    public String getSystemName() {
        return configCacheService.getConfigValue(SYSTEM_NAME, "Demo Admin");
    }

    /**
     * 获取系统版本
     */
    public String getSystemVersion() {
        return configCacheService.getConfigValue(SYSTEM_VERSION, "1.0.0");
    }

    /**
     * 获取系统描述
     */
    public String getSystemDescription() {
        return configCacheService.getConfigValue(SYSTEM_DESCRIPTION, "后台管理系统");
    }

    /**
     * 获取系统Logo
     */
    public String getSystemLogo() {
        return configCacheService.getConfigValue(SYSTEM_LOGO, "");
    }

    /**
     * 获取系统版权信息
     */
    public String getSystemCopyright() {
        return configCacheService.getConfigValue(SYSTEM_COPYRIGHT, "© 2025 Demo Admin");
    }

    /**
     * 获取密码最小长度
     */
    public int getPasswordMinLength() {
        String value = configCacheService.getConfigValue(SECURITY_PASSWORD_MIN_LENGTH, "6");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 6;
        }
    }

    /**
     * 是否要求密码包含特殊字符
     */
    public boolean isPasswordRequireSpecial() {
        String value = configCacheService.getConfigValue(SECURITY_PASSWORD_REQUIRE_SPECIAL, "false");
        return Boolean.parseBoolean(value);
    }

    /**
     * 获取登录失败最大次数
     */
    public int getLoginFailMaxCount() {
        String value = configCacheService.getConfigValue(SECURITY_LOGIN_FAIL_MAX_COUNT, "5");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 5;
        }
    }

    /**
     * 获取账户锁定时间（分钟）
     */
    public int getLoginLockTime() {
        String value = configCacheService.getConfigValue(SECURITY_LOGIN_LOCK_TIME, "30");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 30;
        }
    }

    /**
     * 获取会话超时时间（分钟）
     */
    public int getSessionTimeout() {
        String value = configCacheService.getConfigValue(SECURITY_SESSION_TIMEOUT, "30");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 30;
        }
    }

    /**
     * 获取文件上传最大大小（MB）
     */
    public int getFileUploadMaxSize() {
        String value = configCacheService.getConfigValue(FILE_UPLOAD_MAX_SIZE, "10");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 10;
        }
    }

    /**
     * 获取允许上传的文件类型
     */
    public String getFileUploadAllowedTypes() {
        return configCacheService.getConfigValue(FILE_UPLOAD_ALLOWED_TYPES, "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx");
    }

    /**
     * 获取文件上传路径
     */
    public String getFileUploadPath() {
        return configCacheService.getConfigValue(FILE_UPLOAD_PATH, "/uploads");
    }

    /**
     * 获取邮件服务器地址
     */
    public String getMailHost() {
        return configCacheService.getConfigValue(MAIL_HOST, "");
    }

    /**
     * 获取邮件服务器端口
     */
    public int getMailPort() {
        String value = configCacheService.getConfigValue(MAIL_PORT, "587");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 587;
        }
    }

    /**
     * 获取邮件用户名
     */
    public String getMailUsername() {
        return configCacheService.getConfigValue(MAIL_USERNAME, "");
    }

    /**
     * 获取邮件密码
     */
    public String getMailPassword() {
        return configCacheService.getConfigValue(MAIL_PASSWORD, "");
    }

    /**
     * 获取邮件发送者
     */
    public String getMailFrom() {
        return configCacheService.getConfigValue(MAIL_FROM, "");
    }
}
