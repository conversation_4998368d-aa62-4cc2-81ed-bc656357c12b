package com.example.admin.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket配置
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Configuration
@EnableWebSocketMessageBroker
@RequiredArgsConstructor
public class WebSocketBrokerConfig implements WebSocketMessageBrokerConfigurer {

    private final WebSocketConfig webSocketConfig;

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 启用简单的内存代理
        config.enableSimpleBroker(webSocketConfig.getMessageBroker());
        // 设置应用前缀
        config.setApplicationDestinationPrefixes("/app");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册WebSocket端点
        registry.addEndpoint(webSocketConfig.getEndpoint())
                .setAllowedOrigins(webSocketConfig.getAllowedOrigins().toArray(new String[0]))
                .withSockJS();
    }
    
}