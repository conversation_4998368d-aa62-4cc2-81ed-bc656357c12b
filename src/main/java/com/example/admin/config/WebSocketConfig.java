package com.example.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * WebSocket配置类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Configuration
@ConfigurationProperties(prefix = "websocket")
@Data
public class WebSocketConfig {
    
    /**
     * 允许的跨域来源
     */
    private List<String> allowedOrigins;
    
    /**
     * WebSocket端点
     */
    private String endpoint = "/ws/log";
    
    /**
     * 消息代理前缀
     */
    private String messageBroker = "/topic/logs";
    
}