<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysConfig">
        <id column="id" property="id" />
        <result column="config_key" property="configKey" />
        <result column="config_value" property="configValue" />
        <result column="config_type" property="configType" />
        <result column="description" property="description" />
        <result column="is_encrypted" property="isEncrypted" />
        <result column="is_system" property="isSystem" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_key, config_value, config_type, description, is_encrypted, is_system, status, 
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据配置键查询配置 -->
    <select id="selectByConfigKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_config
        WHERE config_key = #{configKey}
          AND deleted = 0
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_config
        WHERE status = 1
          AND deleted = 0
        ORDER BY config_key ASC
    </select>

    <!-- 根据配置类型查询配置 -->
    <select id="selectByConfigType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_config
        WHERE config_type = #{configType}
          AND deleted = 0
        ORDER BY config_key ASC
    </select>

    <!-- 查询系统配置 -->
    <select id="selectBySystemFlag" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_config
        WHERE is_system = #{isSystem}
          AND deleted = 0
        ORDER BY config_key ASC
    </select>

</mapper>
