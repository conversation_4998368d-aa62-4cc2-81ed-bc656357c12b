<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysUserDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysUserDevice">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="device_id" property="deviceId" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="os_name" property="osName" />
        <result column="os_version" property="osVersion" />
        <result column="browser_name" property="browserName" />
        <result column="browser_version" property="browserVersion" />
        <result column="ip_address" property="ipAddress" />
        <result column="location" property="location" />
        <result column="is_current" property="isCurrent" />
        <result column="status" property="status" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, device_id, device_name, device_type, os_name, os_version, browser_name,
        browser_version, ip_address, location, is_current, status, last_login_time,
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据用户ID获取设备列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_device
        WHERE user_id = #{userId}
          AND deleted = 0
        ORDER BY last_login_time DESC
    </select>

    <!-- 根据用户ID分页获取设备列表 -->
    <select id="selectPageByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_device
        WHERE user_id = #{userId}
          AND deleted = 0
        ORDER BY last_login_time DESC
    </select>

    <!-- 根据用户ID和设备ID获取设备信息 -->
    <select id="selectByUserIdAndDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_device
        WHERE user_id = #{userId}
          AND device_id = #{deviceId}
          AND deleted = 0
    </select>

    <!-- 更新设备状态为离线 -->
    <update id="updateStatusToOffline">
        UPDATE sys_user_device 
        SET status = 0, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
          <if test="excludeDeviceId != null and excludeDeviceId != ''">
              AND device_id != #{excludeDeviceId}
          </if>
    </update>

    <!-- 清除当前设备标记 -->
    <update id="clearCurrentDevice">
        UPDATE sys_user_device 
        SET is_current = 0, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
    </update>

    <!-- 根据用户ID删除所有设备 -->
    <delete id="deleteByUserId">
        UPDATE sys_user_device 
        SET deleted = 1, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
    </delete>

</mapper>
