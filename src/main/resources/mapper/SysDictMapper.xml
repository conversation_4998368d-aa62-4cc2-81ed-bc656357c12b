<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysDict">
        <id column="id" property="id" />
        <result column="dict_code" property="dictCode" />
        <result column="dict_name" property="dictName" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dict_code, dict_name, description, status, create_time, update_time, 
        create_by, update_by, deleted
    </sql>

    <!-- 根据字典编码查询字典 -->
    <select id="selectByDictCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict
        WHERE dict_code = #{dictCode}
          AND deleted = 0
    </select>

    <!-- 查询所有启用的字典 -->
    <select id="selectEnabledDicts" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict
        WHERE status = 1
          AND deleted = 0
        ORDER BY dict_code ASC
    </select>

    <!-- 根据字典名称模糊查询 -->
    <select id="selectByDictNameLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict
        WHERE dict_name LIKE CONCAT('%', #{dictName}, '%')
          AND deleted = 0
        ORDER BY dict_code ASC
    </select>

</mapper>
