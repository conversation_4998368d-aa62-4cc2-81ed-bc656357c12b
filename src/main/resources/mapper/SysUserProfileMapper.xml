<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysUserProfileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysUserProfile">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="theme" property="theme" />
        <result column="language" property="language" />
        <result column="timezone" property="timezone" />
        <result column="date_format" property="dateFormat" />
        <result column="time_format" property="timeFormat" />
        <result column="page_size" property="pageSize" />
        <result column="sidebar_collapsed" property="sidebarCollapsed" />
        <result column="notification_enabled" property="notificationEnabled" />
        <result column="email_notification" property="emailNotification" />
        <result column="sms_notification" property="smsNotification" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, theme, language, timezone, date_format, time_format, page_size,
        sidebar_collapsed, notification_enabled, email_notification, sms_notification,
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据用户ID获取用户配置 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_profile
        WHERE user_id = #{userId}
          AND deleted = 0
    </select>

    <!-- 根据用户ID删除用户配置 -->
    <delete id="deleteByUserId">
        UPDATE sys_user_profile 
        SET deleted = 1, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
    </delete>

</mapper>
