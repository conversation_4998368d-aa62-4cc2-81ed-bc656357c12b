<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysDictItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysDictItem">
        <id column="id" property="id" />
        <result column="dict_id" property="dictId" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="item_value" property="itemValue" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dict_id, item_code, item_name, item_value, sort_order, status, 
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据字典ID查询字典项 -->
    <select id="selectByDictId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict_item
        WHERE dict_id = #{dictId}
          AND deleted = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据字典编码查询字典项 -->
    <select id="selectByDictCode" resultMap="BaseResultMap">
        SELECT di.<include refid="Base_Column_List" />
        FROM sys_dict_item di
        LEFT JOIN sys_dict d ON di.dict_id = d.id
        WHERE d.dict_code = #{dictCode}
          AND di.deleted = 0
          AND d.deleted = 0
        ORDER BY di.sort_order ASC, di.id ASC
    </select>

    <!-- 根据字典ID查询启用的字典项 -->
    <select id="selectEnabledByDictId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict_item
        WHERE dict_id = #{dictId}
          AND status = 1
          AND deleted = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据字典编码查询启用的字典项 -->
    <select id="selectEnabledByDictCode" resultMap="BaseResultMap">
        SELECT di.<include refid="Base_Column_List" />
        FROM sys_dict_item di
        LEFT JOIN sys_dict d ON di.dict_id = d.id
        WHERE d.dict_code = #{dictCode}
          AND di.status = 1
          AND di.deleted = 0
          AND d.deleted = 0
          AND d.status = 1
        ORDER BY di.sort_order ASC, di.id ASC
    </select>

    <!-- 根据字典ID和字典项编码查询 -->
    <select id="selectByDictIdAndItemCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_dict_item
        WHERE dict_id = #{dictId}
          AND item_code = #{itemCode}
          AND deleted = 0
    </select>

    <!-- 删除字典下的所有字典项 -->
    <delete id="deleteByDictId">
        UPDATE sys_dict_item 
        SET deleted = 1, update_time = NOW()
        WHERE dict_id = #{dictId}
          AND deleted = 0
    </delete>

</mapper>
