<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysMenu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="menu_name" property="menuName" />
        <result column="menu_type" property="menuType" />
        <result column="menu_icon" property="menuIcon" />
        <result column="menu_path" property="menuPath" />
        <result column="component_path" property="componentPath" />
        <result column="permission_code" property="permissionCode" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_external" property="isExternal" />
        <result column="is_cached" property="isCached" />
        <result column="is_visible" property="isVisible" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, menu_name, menu_type, menu_icon, menu_path, component_path, permission_code,
        sort_order, is_external, is_cached, is_visible, status, create_time, update_time, 
        create_by, update_by, deleted
    </sql>

    <!-- 根据用户ID查询菜单列表 -->
    <select id="selectMenusByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT m.id, m.parent_id, m.menu_name, m.menu_type, m.menu_icon, m.menu_path, m.component_path, m.permission_code,
        m.sort_order, m.is_external, m.is_cached, m.is_visible, m.status, m.create_time, m.update_time,
        m.create_by, m.update_by, m.deleted
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        LEFT JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.status = 1
          AND m.deleted = 0
        ORDER BY m.sort_order ASC
    </select>

    <!-- 根据角色ID查询菜单列表 -->
    <select id="selectMenusByRoleId" resultMap="BaseResultMap">
        SELECT DISTINCT m.id, m.parent_id, m.menu_name, m.menu_type, m.menu_icon, m.menu_path, m.component_path, m.permission_code,
        m.sort_order, m.is_external, m.is_cached, m.is_visible, m.status, m.create_time, m.update_time,
        m.create_by, m.update_by, m.deleted
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND m.status = 1
          AND m.deleted = 0
        ORDER BY m.sort_order ASC
    </select>

    <!-- 根据父菜单ID查询子菜单列表 -->
    <select id="selectChildrenByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_menu
        WHERE parent_id = #{parentId}
          AND status = 1
          AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有菜单的树形结构 -->
    <select id="selectMenuTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_menu
        WHERE deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 根据用户ID查询菜单树形结构 -->
    <select id="selectMenuTreeByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT m.id, m.parent_id, m.menu_name, m.menu_type, m.menu_icon, m.menu_path, m.component_path, m.permission_code,
        m.sort_order, m.is_external, m.is_cached, m.is_visible, m.status, m.create_time, m.update_time,
        m.create_by, m.update_by, m.deleted
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        LEFT JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.deleted = 0
        ORDER BY m.sort_order ASC
    </select>

    <!-- 根据角色ID查询菜单树形结构 -->
    <select id="selectMenuTreeByRoleId" resultMap="BaseResultMap">
        SELECT DISTINCT m.id, m.parent_id, m.menu_name, m.menu_type, m.menu_icon, m.menu_path, m.component_path, m.permission_code,
        m.sort_order, m.is_external, m.is_cached, m.is_visible, m.status, m.create_time, m.update_time,
        m.create_by, m.update_by, m.deleted
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND m.deleted = 0
        ORDER BY m.sort_order ASC
    </select>

    <!-- 查询用户权限标识列表 -->
    <select id="selectPermsByUserId" resultType="java.lang.String">
        SELECT DISTINCT m.permission_code
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        LEFT JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND m.permission_code IS NOT NULL
          AND m.permission_code != ''
          AND m.status = 1
          AND m.deleted = 0
    </select>

    <!-- 查询角色权限标识列表 -->
    <select id="selectPermsByRoleId" resultType="java.lang.String">
        SELECT DISTINCT m.permission_code
        FROM sys_menu m
        LEFT JOIN sys_role_permission rp ON m.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND m.permission_code IS NOT NULL
          AND m.permission_code != ''
          AND m.status = 1
          AND m.deleted = 0
    </select>

    <!-- 根据菜单类型查询菜单列表 -->
    <select id="selectMenusByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_menu
        WHERE menu_type = #{menuType}
          AND status = 1
          AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有启用的菜单（用于路由生成） -->
    <select id="selectEnabledMenus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_menu
        WHERE status = 1
          AND deleted = 0
        ORDER BY sort_order ASC
    </select>

</mapper>
