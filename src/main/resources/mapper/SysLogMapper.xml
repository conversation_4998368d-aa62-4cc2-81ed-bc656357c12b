<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysLog">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="log_type" property="logType" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="description" property="description" />
        <result column="request_uri" property="requestUri" />
        <result column="method" property="method" />
        <result column="request_params" property="requestParams" />
        <result column="response" property="response" />
        <result column="execution_time" property="executionTime" />
        <result column="ip" property="ip" />
        <result column="user_agent" property="userAgent" />
        <result column="success" property="success" />
        <result column="error_msg" property="errorMsg" />
        <result column="class_name" property="className" />
        <result column="method_name" property="methodName" />
        <result column="module_name" property="moduleName" />
        <result column="operation_type" property="operationType" />
        <result column="browser" property="browser" />
        <result column="os" property="os" />
        <result column="device_info" property="deviceInfo" />
        <result column="session_id" property="sessionId" />
        <result column="referer" property="referer" />
        <result column="exception_type" property="exceptionType" />
        <result column="stack_trace" property="stackTrace" />
        <result column="level" property="level" />
        <result column="thread_name" property="threadName" />
        <result column="ext_info" property="extInfo" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, create_time, update_time, create_by, update_by, deleted, log_type, user_id, username, 
        description, request_uri, method, request_params, response, execution_time, ip, user_agent, 
        success, error_msg, class_name, method_name, module_name, operation_type, browser, os, 
        device_info, session_id, referer, exception_type, stack_trace, level, thread_name, 
        ext_info
    </sql>

    <!-- 分页查询日志列表 -->
    <select id="selectLogPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        <where>
            deleted = 0
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                    username LIKE CONCAT('%', #{query.keyword}, '%')
                    OR description LIKE CONCAT('%', #{query.keyword}, '%')
                    OR request_uri LIKE CONCAT('%', #{query.keyword}, '%')
                    OR class_name LIKE CONCAT('%', #{query.keyword}, '%')
                    OR method_name LIKE CONCAT('%', #{query.keyword}, '%')
                    OR module_name LIKE CONCAT('%', #{query.keyword}, '%')
                    OR operation_type LIKE CONCAT('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.logType != null and query.logType != ''">
                AND log_type = #{query.logType}
            </if>
            <if test="query.userId != null">
                AND user_id = #{query.userId}
            </if>
            <if test="query.username != null and query.username != ''">
                AND username LIKE CONCAT('%', #{query.username}, '%')
            </if>
            <if test="query.moduleName != null and query.moduleName != ''">
                AND module_name = #{query.moduleName}
            </if>
            <if test="query.operationType != null and query.operationType != ''">
                AND operation_type = #{query.operationType}
            </if>
            <if test="query.requestUri != null and query.requestUri != ''">
                AND request_uri LIKE CONCAT('%', #{query.requestUri}, '%')
            </if>
            <if test="query.method != null and query.method != ''">
                AND method = #{query.method}
            </if>
            <if test="query.ip != null and query.ip != ''">
                AND ip = #{query.ip}
            </if>
            <if test="query.success != null">
                AND success = #{query.success}
            </if>
            <if test="query.level != null and query.level != ''">
                AND level = #{query.level}
            </if>
            <if test="query.exceptionType != null and query.exceptionType != ''">
                AND exception_type = #{query.exceptionType}
            </if>
            <if test="query.browser != null and query.browser != ''">
                AND browser = #{query.browser}
            </if>
            <if test="query.os != null and query.os != ''">
                AND os = #{query.os}
            </if>
            <if test="query.startTime != null">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND create_time &lt;= #{query.endTime}
            </if>
            <if test="query.minExecutionTime != null">
                AND execution_time >= #{query.minExecutionTime}
            </if>
            <if test="query.maxExecutionTime != null">
                AND execution_time &lt;= #{query.maxExecutionTime}
            </if>
            <if test="query.userIds != null and query.userIds.size() > 0">
                AND user_id IN
                <foreach collection="query.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="query.moduleNames != null and query.moduleNames.size() > 0">
                AND module_name IN
                <foreach collection="query.moduleNames" item="moduleName" open="(" separator="," close=")">
                    #{moduleName}
                </foreach>
            </if>
            <if test="query.operationTypes != null and query.operationTypes.size() > 0">
                AND operation_type IN
                <foreach collection="query.operationTypes" item="operationType" open="(" separator="," close=")">
                    #{operationType}
                </foreach>
            </if>
            <if test="query.ips != null and query.ips.size() > 0">
                AND ip IN
                <foreach collection="query.ips" item="ip" open="(" separator="," close=")">
                    #{ip}
                </foreach>
            </if>
            <if test="query.levels != null and query.levels.size() > 0">
                AND level IN
                <foreach collection="query.levels" item="level" open="(" separator="," close=")">
                    #{level}
                </foreach>
            </if>
        </where>
        ORDER BY ${query.sortField} ${query.sortOrder}
    </select>

    <!-- 根据条件查询日志列表 -->
    <select id="selectLogList" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        <where>
            deleted = 0
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                    username LIKE CONCAT('%', #{query.keyword}, '%')
                    OR description LIKE CONCAT('%', #{query.keyword}, '%')
                    OR request_uri LIKE CONCAT('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.logType != null and query.logType != ''">
                AND log_type = #{query.logType}
            </if>
            <if test="query.startTime != null">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND create_time &lt;= #{query.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据时间范围查询日志数量 -->
    <select id="selectLogCountByTimeRange" resultType="java.lang.Long">
        SELECT COUNT(1) FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        <if test="logType != null and logType != ''">
            AND log_type = #{logType}
        </if>
    </select>

    <!-- 根据用户ID查询日志列表 -->
    <select id="selectLogsByUserId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND user_id = #{userId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据IP查询日志列表 -->
    <select id="selectLogsByIp" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND ip = #{ip}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询异常日志列表 -->
    <select id="selectExceptionLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND log_type = 'EXCEPTION_LOG'
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询性能日志列表 -->
    <select id="selectPerformanceLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND log_type = 'PERFORMANCE_LOG'
        <if test="threshold != null">
            AND execution_time > #{threshold}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY execution_time DESC
    </select>

    <!-- 统计日志数量（按类型） -->
    <select id="statisticsLogCountByType" resultType="java.util.Map">
        SELECT 
            log_type as logType,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY log_type
        ORDER BY count DESC
    </select>

    <!-- 统计日志数量（按用户） -->
    <select id="statisticsLogCountByUser" resultType="java.util.Map">
        SELECT 
            username,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY username
        ORDER BY count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计日志数量（按模块） -->
    <select id="statisticsLogCountByModule" resultType="java.util.Map">
        SELECT 
            module_name as moduleName,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY module_name
        ORDER BY count DESC
    </select>

    <!-- 统计日志数量（按操作） -->
    <select id="statisticsLogCountByOperation" resultType="java.util.Map">
        SELECT 
            operation_type as operationType,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY operation_type
        ORDER BY count DESC
    </select>

    <!-- 统计日志数量（按日期） -->
    <select id="statisticsLogCountByDate" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date ASC
    </select>

    <!-- 统计性能指标 -->
    <select id="statisticsPerformanceMetrics" resultType="java.util.Map">
        SELECT 
            COUNT(1) as totalCount,
            SUM(execution_time) as totalExecutionTime,
            AVG(execution_time) as avgExecutionTime,
            MIN(execution_time) as minExecutionTime,
            MAX(execution_time) as maxExecutionTime,
            SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failedCount,
            SUM(CASE WHEN execution_time > 2000 THEN 1 ELSE 0 END) as slowQueryCount
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询慢查询日志 -->
    <select id="selectSlowQueryLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND execution_time > #{threshold}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY execution_time DESC
    </select>

    <!-- 查询错误日志 -->
    <select id="selectErrorLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND success = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>


    <!-- 删除过期日志 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM sys_log
        WHERE deleted = 0 AND create_time &lt; #{expirationDate}
    </delete>

    <!-- 查询最近的日志 -->
    <select id="selectRecentLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询用户操作轨迹 -->
    <select id="selectUserOperationTrail" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM sys_log
        WHERE deleted = 0 AND user_id = #{userId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time ASC
    </select>

    <!-- 查询热门操作 -->
    <select id="selectPopularOperations" resultType="java.util.Map">
        SELECT 
            operation_type as operationType,
            COUNT(1) as count
        FROM sys_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY operation_type
        ORDER BY count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>