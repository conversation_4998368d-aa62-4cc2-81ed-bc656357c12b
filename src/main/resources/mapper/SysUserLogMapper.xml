<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysUserLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysUserLog">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="operation_type" property="operationType" />
        <result column="operation_desc" property="operationDesc" />
        <result column="request_method" property="requestMethod" />
        <result column="request_url" property="requestUrl" />
        <result column="request_params" property="requestParams" />
        <result column="response_result" property="responseResult" />
        <result column="ip_address" property="ipAddress" />
        <result column="user_agent" property="userAgent" />
        <result column="execution_time" property="executionTime" />
        <result column="status" property="status" />
        <result column="error_message" property="errorMessage" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, operation_type, operation_desc, request_method, request_url, request_params,
        response_result, ip_address, user_agent, execution_time, status, error_message,
        create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据用户ID分页获取操作日志 -->
    <select id="selectPageByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_log
        WHERE user_id = #{userId}
          AND deleted = 0
          <if test="operationType != null and operationType != ''">
              AND operation_type = #{operationType}
          </if>
          <if test="startTime != null">
              AND create_time >= #{startTime}
          </if>
          <if test="endTime != null">
              AND create_time &lt;= #{endTime}
          </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID获取操作日志列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user_log
        WHERE user_id = #{userId}
          AND deleted = 0
          <if test="operationType != null and operationType != ''">
              AND operation_type = #{operationType}
          </if>
          <if test="startTime != null">
              AND create_time >= #{startTime}
          </if>
          <if test="endTime != null">
              AND create_time &lt;= #{endTime}
          </if>
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据用户ID删除操作日志 -->
    <delete id="deleteByUserId">
        UPDATE sys_user_log 
        SET deleted = 1, update_time = NOW()
        WHERE user_id = #{userId}
          AND deleted = 0
    </delete>

    <!-- 删除指定时间之前的日志 -->
    <delete id="deleteBeforeTime">
        UPDATE sys_user_log 
        SET deleted = 1, update_time = NOW()
        WHERE create_time &lt; #{beforeTime}
          AND deleted = 0
    </delete>

    <!-- 统计用户操作次数 -->
    <select id="countByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_user_log
        WHERE user_id = #{userId}
          AND deleted = 0
          <if test="operationType != null and operationType != ''">
              AND operation_type = #{operationType}
          </if>
          <if test="startTime != null">
              AND create_time >= #{startTime}
          </if>
          <if test="endTime != null">
              AND create_time &lt;= #{endTime}
          </if>
    </select>

</mapper>
