<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.admin.mapper.SysPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.admin.entity.SysPermission">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="permission_name" property="permissionName" />
        <result column="permission_code" property="permissionCode" />
        <result column="permission_type" property="permissionType" />
        <result column="path" property="path" />
        <result column="sort_order" property="sortOrder" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="is_system" property="isSystem" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, permission_name, permission_code, permission_type, path,
        sort_order, description, status, is_system, remark, create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectPermissionsByRoleId" resultMap="BaseResultMap">
        SELECT DISTINCT p.id, p.parent_id, p.permission_name, p.permission_code, p.permission_type, p.path,
        p.sort_order, p.description, p.status, p.is_system, p.remark, p.create_time, p.update_time, p.create_by, p.update_by, p.deleted
        FROM sys_permission p
        LEFT JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.status = 1
          AND p.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectPermissionsByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.id, p.parent_id, p.permission_name, p.permission_code, p.permission_type, p.path,
        p.sort_order, p.description, p.status, p.is_system, p.remark, p.create_time, p.update_time, p.create_by, p.update_by, p.deleted
        FROM sys_permission p
        LEFT JOIN sys_role_permission rp ON p.id = rp.permission_id
        LEFT JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.status = 1
          AND p.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据父权限ID查询子权限列表 -->
    <select id="selectChildrenByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_permission
        WHERE parent_id = #{parentId}
          AND status = 1
          AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有权限的树形结构 -->
    <select id="selectPermissionTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_permission
        WHERE deleted = 0
        ORDER BY sort_order ASC
    </select>

</mapper>
