-- 快速修复权限分配的SQL脚本
-- <AUTHOR>
-- @since 2025-01-27

USE `demo_admin`;

-- 1. 确保admin用户拥有SUPER_ADMIN角色
DELETE FROM `sys_user_role` WHERE `user_id` = 1;
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `create_by`, `update_by`) 
VALUES (1, 1, 1, 1);

-- 2. 确保SUPER_ADMIN角色拥有所有权限
DELETE FROM `sys_role_permission` WHERE `role_id` = 1;
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `create_by`, `update_by`)
SELECT 1 as role_id, id as permission_id, 1 as create_by, 1 as update_by
FROM `sys_permission` WHERE `status` = 1 AND `deleted` = 0;

-- 3. 验证admin用户的权限
SELECT 
    u.username,
    u.real_name,
    r.role_name,
    COUNT(rp.permission_id) as permission_count
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role r ON ur.role_id = r.id
LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
WHERE u.username = 'admin'
GROUP BY u.id, u.username, u.real_name, r.role_name;

-- 4. 显示admin用户拥有的system:user:list权限
SELECT 
    u.username,
    p.permission_code,
    p.permission_name
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role_permission rp ON ur.role_id = rp.role_id
JOIN sys_permission p ON rp.permission_id = p.id
WHERE u.username = 'admin' 
  AND p.permission_code = 'system:user:list'
  AND p.status = 1 
  AND p.deleted = 0;

-- 5. 显示所有权限总数
SELECT COUNT(*) as total_enabled_permissions FROM `sys_permission` WHERE `status` = 1 AND `deleted` = 0;

-- 完成
SELECT '权限分配修复完成!' as message;