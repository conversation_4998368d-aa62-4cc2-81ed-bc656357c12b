-- 修复权限配置问题的SQL脚本
-- <AUTHOR>
-- @since 2025-08-01

USE `demo_admin`;

-- 删除现有的权限数据（保留角色数据）
DELETE FROM `sys_role_permission`;

-- 更新权限数据ID，避免与角色ID冲突
-- 先删除现有权限数据
DELETE FROM `sys_permission`;

-- 重新插入权限数据（使用新的ID范围）
INSERT INTO `sys_permission` (`id`, `permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `description`, `status`, `sort_order`, `is_system`, `remark`, `create_by`, `update_by`) VALUES
-- 系统管理
(100, 'system', '系统管理', 1, 0, '/system', '系统管理模块', 1, 1, 1, '系统内置权限', 1, 1),
(101, 'system:user', '用户管理', 1, 100, '/system/user', '用户管理页面', 1, 1, 1, '系统内置权限', 1, 1),
(102, 'system:user:list', '用户查询', 2, 101, NULL, '用户列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(103, 'system:user:add', '用户新增', 2, 101, NULL, '用户新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(104, 'system:user:edit', '用户修改', 2, 101, NULL, '用户修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(105, 'system:user:delete', '用户删除', 2, 101, NULL, '用户删除权限', 1, 4, 1, '系统内置权限', 1, 1),
(106, 'system:user:resetPassword', '重置密码', 2, 101, NULL, '重置用户密码权限', 1, 5, 1, '系统内置权限', 1, 1),
(107, 'system:user:export', '用户导出', 2, 101, NULL, '用户数据导出权限', 1, 6, 1, '系统内置权限', 1, 1),
(108, 'system:user:import', '用户导入', 2, 101, NULL, '用户数据导入权限', 1, 7, 1, '系统内置权限', 1, 1),
(109, 'system:user:updateStatus', '更新状态', 2, 101, NULL, '用户状态更新权限', 1, 8, 1, '系统内置权限', 1, 1),
(110, 'system:user:role', '分配角色', 2, 101, NULL, '用户角色分配权限', 1, 9, 1, '系统内置权限', 1, 1),

(111, 'system:role', '角色管理', 1, 100, '/system/role', '角色管理页面', 1, 2, 1, '系统内置权限', 1, 1),
(112, 'system:role:query', '角色查询', 2, 111, NULL, '角色列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(113, 'system:role:add', '角色新增', 2, 111, NULL, '角色新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(114, 'system:role:edit', '角色修改', 2, 111, NULL, '角色修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(115, 'system:role:delete', '角色删除', 2, 111, NULL, '角色删除权限', 1, 4, 1, '系统内置权限', 1, 1),
(116, 'system:role:permission', '角色授权', 2, 111, NULL, '角色权限分配权限', 1, 5, 1, '系统内置权限', 1, 1),

(117, 'system:menu', '菜单管理', 1, 100, '/system/menu', '菜单管理页面', 1, 3, 1, '系统内置权限', 1, 1),
(118, 'system:menu:list', '菜单查询', 2, 117, NULL, '菜单列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(119, 'system:menu:add', '菜单新增', 2, 117, NULL, '菜单新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(120, 'system:menu:edit', '菜单修改', 2, 117, NULL, '菜单修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(121, 'system:menu:delete', '菜单删除', 2, 117, NULL, '菜单删除权限', 1, 4, 1, '系统内置权限', 1, 1),

(122, 'system:config', '系统配置', 1, 100, '/system/config', '系统配置页面', 1, 5, 1, '系统内置权限', 1, 1),
(123, 'system:config:list', '配置查询', 2, 122, NULL, '配置列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(124, 'system:config:add', '配置新增', 2, 122, NULL, '配置新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(125, 'system:config:edit', '配置修改', 2, 122, NULL, '配置修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(126, 'system:config:delete', '配置删除', 2, 122, NULL, '配置删除权限', 1, 4, 1, '系统内置权限', 1, 1),

(127, 'system:log', '日志管理', 1, 100, '/system/log', '日志管理页面', 1, 7, 1, '系统内置权限', 1, 1),
(128, 'system:log:list', '日志查询', 2, 127, NULL, '日志列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(129, 'system:log:view', '日志查看', 2, 127, NULL, '日志详情查看权限', 1, 2, 1, '系统内置权限', 1, 1),
(130, 'system:log:export', '日志导出', 2, 127, NULL, '日志数据导出权限', 1, 3, 1, '系统内置权限', 1, 1),
(131, 'system:log:clean', '日志清理', 2, 127, NULL, '日志清理权限', 1, 4, 1, '系统内置权限', 1, 1),
(132, 'system:log:statistics', '日志统计', 2, 127, NULL, '日志统计权限', 1, 5, 1, '系统内置权限', 1, 1),

-- 系统监控
(200, 'monitor', '系统监控', 1, 0, '/monitor', '系统监控模块', 1, 2, 1, '系统内置权限', 1, 1),
(201, 'monitor:log', '日志管理', 1, 200, '/monitor/log', '日志管理页面', 1, 1, 1, '系统内置权限', 1, 1),
(202, 'monitor:log:list', '日志查询', 2, 201, NULL, '日志列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(203, 'monitor:log:delete', '日志删除', 2, 201, NULL, '日志删除权限', 1, 2, 1, '系统内置权限', 1, 1),

(204, 'monitor:online', '在线用户', 1, 200, '/monitor/online', '在线用户页面', 1, 2, 1, '系统内置权限', 1, 1),
(205, 'monitor:online:list', '在线用户查询', 2, 204, NULL, '在线用户列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(206, 'monitor:online:kick', '强制下线', 2, 204, NULL, '强制用户下线权限', 1, 2, 1, '系统内置权限', 1, 1),

-- 个人中心
(300, 'profile', '个人中心', 1, 0, '/profile', '个人中心模块', 1, 3, 1, '系统内置权限', 1, 1),
(301, 'profile:info', '个人信息', 1, 300, '/profile/info', '个人信息页面', 1, 1, 1, '系统内置权限', 1, 1),
(302, 'profile:info:edit', '修改信息', 2, 301, NULL, '修改个人信息权限', 1, 1, 1, '系统内置权限', 1, 1),
(303, 'profile:password', '修改密码', 1, 300, '/profile/password', '修改密码页面', 1, 2, 1, '系统内置权限', 1, 1),
(304, 'profile:password:edit', '修改密码', 2, 303, NULL, '修改个人密码权限', 1, 1, 1, '系统内置权限', 1, 1);

-- 重新分配角色权限关联（给超级管理员角色分配所有权限）
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_by`, `update_by`)
SELECT ROW_NUMBER() OVER (ORDER BY id) as id, 1 as role_id, id as permission_id, 1 as create_by, 1 as update_by
FROM `sys_permission` WHERE `status` = 1;

-- 给管理员角色分配基础权限
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_by`, `update_by`)
SELECT 
    ROW_NUMBER() OVER (ORDER BY permission_id) + (SELECT COUNT(*) FROM `sys_role_permission`) as id,
    2 as role_id, 
    id as permission_id, 
    1 as create_by, 
    1 as update_by
FROM `sys_permission` 
WHERE `status` = 1 
AND `permission_code` IN (
    'system:user:list',
    'system:user:add',
    'system:user:edit',
    'system:user:export',
    'system:user:updateStatus',
    'system:role:query',
    'system:menu:list',
    'system:config:list',
    'system:log:list',
    'system:log:view',
    'system:log:statistics',
    'monitor:log:list',
    'monitor:online:list',
    'profile:info:edit',
    'profile:password:edit'
);

-- 给普通用户角色分配只读权限
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_by`, `update_by`)
SELECT 
    ROW_NUMBER() OVER (ORDER BY permission_id) + (SELECT COUNT(*) FROM `sys_role_permission`) as id,
    3 as role_id, 
    id as permission_id, 
    1 as create_by, 
    1 as update_by
FROM `sys_permission` 
WHERE `status` = 1 
AND `permission_code` IN (
    'system:user:list',
    'system:role:query',
    'system:menu:list',
    'system:config:list',
    'system:log:list',
    'system:log:view',
    'monitor:log:list',
    'monitor:online:list',
    'profile:info:edit',
    'profile:password:edit'
);

-- 更新菜单数据中的权限引用
UPDATE `sys_menu` SET `perms` = 'system:user:list' WHERE `menu_name` = 'User' AND `perms` = 'system:user:list';
UPDATE `sys_menu` SET `perms` = 'system:role:query' WHERE `menu_name` = 'Role' AND `perms` = 'system:role:query';
UPDATE `sys_menu` SET `perms` = 'system:menu:list' WHERE `menu_name` = 'Menu' AND `perms` = 'system:menu:list';
UPDATE `sys_menu` SET `perms` = 'system:config:list' WHERE `menu_name` = 'Config' AND `perms` = 'system:config:view';
UPDATE `sys_menu` SET `perms` = 'system:dict:view' WHERE `menu_name` = 'Dict' AND `perms` = 'system:dict:view';

-- 添加日志管理菜单
INSERT INTO `sys_menu` (`id`, `menu_name`, `menu_title`, `parent_id`, `menu_type`, `path`, `component`, `icon`, `is_frame`, `is_cache`, `visible`, `status`, `perms`, `sort_order`, `is_system`, `remark`, `create_by`, `update_by`) VALUES
(15, 'Log', '日志管理', 1, 2, '/system/log', 'system/log/index', 'document', 0, 1, 1, 1, 'system:log:list', 8, 1, '日志管理菜单', 1, 1);

-- 更新其他菜单的排序
UPDATE `sys_menu` SET `sort_order` = 9 WHERE `menu_name` = 'Dict' AND `parent_id` = 1;

-- 显示修复结果
SELECT '权限配置修复完成!' as message;
SELECT COUNT(*) as total_permissions FROM `sys_permission` WHERE `status` = 1;
SELECT COUNT(*) as total_role_permissions FROM `sys_role_permission`;