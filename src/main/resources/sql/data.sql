-- Demo Admin 后台管理系统初始化数据脚本
-- <AUTHOR>
-- @since 2025-01-27

USE `demo_admin`;

-- 初始化系统角色数据
INSERT INTO `sys_role` (`id`, `role_code`, `role_name`, `description`, `status`, `sort_order`, `data_scope`, `is_system`, `remark`, `create_by`, `update_by`) VALUES
(1, 'SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1, 1, 1, 1, '系统内置角色，不可删除', 1, 1),
(2, 'ADMIN', '系统管理员', '系统管理员，拥有大部分权限', 1, 2, 2, 1, '系统内置角色，不可删除', 1, 1),
(3, 'USER', '普通用户', '普通用户，拥有基础权限', 1, 3, 4, 1, '系统内置角色，不可删除', 1, 1);

-- 初始化系统权限数据
INSERT INTO `sys_permission` (`id`, `permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `description`, `status`, `sort_order`, `is_system`, `remark`, `create_by`, `update_by`) VALUES
-- 系统管理
(100, 'system', '系统管理', 1, 0, '/system', '系统管理模块', 1, 1, 1, '系统内置权限', 1, 1),
(101, 'system:user', '用户管理', 1, 100, '/system/user', '用户管理页面', 1, 1, 1, '系统内置权限', 1, 1),
(102, 'system:user:list', '用户查询', 2, 101, NULL, '用户列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(103, 'system:user:add', '用户新增', 2, 101, NULL, '用户新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(104, 'system:user:edit', '用户修改', 2, 101, NULL, '用户修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(105, 'system:user:delete', '用户删除', 2, 101, NULL, '用户删除权限', 1, 4, 1, '系统内置权限', 1, 1),
(106, 'system:user:resetPassword', '重置密码', 2, 101, NULL, '重置用户密码权限', 1, 5, 1, '系统内置权限', 1, 1),
(107, 'system:user:export', '用户导出', 2, 101, NULL, '用户数据导出权限', 1, 6, 1, '系统内置权限', 1, 1),
(108, 'system:user:import', '用户导入', 2, 101, NULL, '用户数据导入权限', 1, 7, 1, '系统内置权限', 1, 1),
(109, 'system:user:updateStatus', '更新状态', 2, 101, NULL, '用户状态更新权限', 1, 8, 1, '系统内置权限', 1, 1),
(110, 'system:user:role', '分配角色', 2, 101, NULL, '用户角色分配权限', 1, 9, 1, '系统内置权限', 1, 1),

(111, 'system:role', '角色管理', 1, 100, '/system/role', '角色管理页面', 1, 2, 1, '系统内置权限', 1, 1),
(112, 'system:role:query', '角色查询', 2, 111, NULL, '角色列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(113, 'system:role:add', '角色新增', 2, 111, NULL, '角色新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(114, 'system:role:edit', '角色修改', 2, 111, NULL, '角色修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(115, 'system:role:delete', '角色删除', 2, 111, NULL, '角色删除权限', 1, 4, 1, '系统内置权限', 1, 1),
(116, 'system:role:permission', '角色授权', 2, 111, NULL, '角色权限分配权限', 1, 5, 1, '系统内置权限', 1, 1),

(117, 'system:menu', '菜单管理', 1, 100, '/system/menu', '菜单管理页面', 1, 3, 1, '系统内置权限', 1, 1),
(118, 'system:menu:list', '菜单查询', 2, 117, NULL, '菜单列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(119, 'system:menu:add', '菜单新增', 2, 117, NULL, '菜单新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(120, 'system:menu:edit', '菜单修改', 2, 117, NULL, '菜单修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(121, 'system:menu:delete', '菜单删除', 2, 117, NULL, '菜单删除权限', 1, 4, 1, '系统内置权限', 1, 1),

(122, 'system:config', '系统配置', 1, 100, '/system/config', '系统配置页面', 1, 5, 1, '系统内置权限', 1, 1),
(123, 'system:config:list', '配置查询', 2, 122, NULL, '配置列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(124, 'system:config:add', '配置新增', 2, 122, NULL, '配置新增权限', 1, 2, 1, '系统内置权限', 1, 1),
(125, 'system:config:edit', '配置修改', 2, 122, NULL, '配置修改权限', 1, 3, 1, '系统内置权限', 1, 1),
(126, 'system:config:delete', '配置删除', 2, 122, NULL, '配置删除权限', 1, 4, 1, '系统内置权限', 1, 1),

(127, 'system:log', '日志管理', 1, 100, '/system/log', '日志管理页面', 1, 7, 1, '系统内置权限', 1, 1),
(128, 'system:log:list', '日志查询', 2, 127, NULL, '日志列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(129, 'system:log:view', '日志查看', 2, 127, NULL, '日志详情查看权限', 1, 2, 1, '系统内置权限', 1, 1),
(130, 'system:log:export', '日志导出', 2, 127, NULL, '日志数据导出权限', 1, 3, 1, '系统内置权限', 1, 1),
(131, 'system:log:clean', '日志清理', 2, 127, NULL, '日志清理权限', 1, 4, 1, '系统内置权限', 1, 1),
(132, 'system:log:statistics', '日志统计', 2, 127, NULL, '日志统计权限', 1, 5, 1, '系统内置权限', 1, 1),

-- 系统监控
(200, 'monitor', '系统监控', 1, 0, '/monitor', '系统监控模块', 1, 2, 1, '系统内置权限', 1, 1),
(201, 'monitor:log', '日志管理', 1, 200, '/monitor/log', '日志管理页面', 1, 1, 1, '系统内置权限', 1, 1),
(202, 'monitor:log:list', '日志查询', 2, 201, NULL, '日志列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(203, 'monitor:log:delete', '日志删除', 2, 201, NULL, '日志删除权限', 1, 2, 1, '系统内置权限', 1, 1),

(204, 'monitor:online', '在线用户', 1, 200, '/monitor/online', '在线用户页面', 1, 2, 1, '系统内置权限', 1, 1),
(205, 'monitor:online:list', '在线用户查询', 2, 204, NULL, '在线用户列表查询权限', 1, 1, 1, '系统内置权限', 1, 1),
(206, 'monitor:online:kick', '强制下线', 2, 204, NULL, '强制用户下线权限', 1, 2, 1, '系统内置权限', 1, 1),

-- 个人中心
(300, 'profile', '个人中心', 1, 0, '/profile', '个人中心模块', 1, 3, 1, '系统内置权限', 1, 1),
(301, 'profile:info', '个人信息', 1, 300, '/profile/info', '个人信息页面', 1, 1, 1, '系统内置权限', 1, 1),
(302, 'profile:info:edit', '修改信息', 2, 301, NULL, '修改个人信息权限', 1, 1, 1, '系统内置权限', 1, 1),
(303, 'profile:password', '修改密码', 1, 300, '/profile/password', '修改密码页面', 1, 2, 1, '系统内置权限', 1, 1),
(304, 'profile:password:edit', '修改密码', 2, 303, NULL, '修改个人密码权限', 1, 1, 1, '系统内置权限', 1, 1);

-- 初始化系统菜单数据
INSERT INTO `sys_menu` (`id`, `menu_name`, `menu_title`, `parent_id`, `menu_type`, `path`, `component`, `icon`, `is_frame`, `is_cache`, `visible`, `status`, `perms`, `sort_order`, `is_system`, `remark`, `create_by`, `update_by`) VALUES
-- 一级菜单
(1, 'System', '系统管理', 0, 1, '/system', NULL, 'system', 0, 0, 1, 1, NULL, 1, 1, '系统管理目录', 1, 1),
(2, 'Monitor', '系统监控', 0, 1, '/monitor', NULL, 'monitor', 0, 0, 1, 1, NULL, 2, 1, '系统监控目录', 1, 1),
(3, 'Profile', '个人中心', 0, 1, '/profile', NULL, 'user', 0, 0, 1, 1, NULL, 3, 1, '个人中心目录', 1, 1),

-- 系统管理子菜单
(4, 'User', '用户管理', 1, 2, '/system/user', 'system/user/index', 'user', 0, 1, 1, 1, 'system:user:list', 1, 1, '用户管理菜单', 1, 1),
(5, 'Role', '角色管理', 1, 2, '/system/role', 'system/role/index', 'peoples', 0, 1, 1, 1, 'system:role:query', 2, 1, '角色管理菜单', 1, 1),
(6, 'Permission', '权限管理', 1, 2, '/system/permission', 'system/permission/index', 'lock', 0, 1, 1, 1, 'system:permission:list', 3, 1, '权限管理菜单', 1, 1),
(7, 'Menu', '菜单管理', 1, 2, '/system/menu', 'system/menu/index', 'tree-table', 0, 1, 1, 1, 'system:menu:list', 4, 1, '菜单管理菜单', 1, 1),
(12, 'Log', '日志管理', 1, 2, '/system/log', 'system/log/index', 'document', 0, 1, 1, 1, 'system:log:list', 5, 1, '日志管理菜单', 1, 1),
(13, 'Config', '系统配置', 1, 2, '/system/config', 'system/config/index', 'setting', 0, 1, 1, 1, 'system:config:list', 6, 1, '系统配置菜单', 1, 1),
(14, 'Dict', '数据字典', 1, 2, '/system/dict', 'system/dict/index', 'dict', 0, 1, 1, 1, 'system:dict:view', 7, 1, '数据字典菜单', 1, 1),

-- 系统监控子菜单
(8, 'Log', '日志管理', 2, 2, '/monitor/log', 'monitor/log/index', 'log', 0, 1, 1, 1, 'monitor:log:list', 1, 1, '日志管理菜单', 1, 1),
(9, 'Online', '在线用户', 2, 2, '/monitor/online', 'monitor/online/index', 'online', 0, 1, 1, 1, 'monitor:online:list', 2, 1, '在线用户菜单', 1, 1),

-- 个人中心子菜单
(10, 'Info', '个人信息', 3, 2, '/profile/info', 'profile/info/index', 'user', 0, 1, 1, 1, 'profile:info:edit', 1, 1, '个人信息菜单', 1, 1),
(11, 'Password', '修改密码', 3, 2, '/profile/password', 'profile/password/index', 'password', 0, 1, 1, 1, 'profile:password:edit', 2, 1, '修改密码菜单', 1, 1);

-- 初始化超级管理员用户（密码：123456）
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `nickname`, `email`, `phone`, `gender`, `avatar`, `status`, `dept_id`, `password_update_time`, `remark`, `create_by`, `update_by`) VALUES
(1, 'admin', '$2a$10$MgaZgBZumFT4RDSPhxxffuo6LNBYI6TI4iH2XR6GHgjP.qj5wObw2', '系统管理员', '超级管理员', '<EMAIL>', '13800138000', 1, NULL, 1, NULL, NOW(), '系统内置超级管理员账户', 1, 1);

-- 初始化测试用户
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `nickname`, `email`, `phone`, `gender`, `avatar`, `status`, `dept_id`, `password_update_time`, `remark`, `create_by`, `update_by`) VALUES
(2, 'testuser', '$2a$10$MgaZgBZumFT4RDSPhxxffuo6LNBYI6TI4iH2XR6GHgjP.qj5wObw2', '测试用户', '测试用户', '<EMAIL>', '13800138001', 1, NULL, 1, NULL, NOW(), '测试用户账户', 1, 1),
(3, 'developer', '$2a$10$MgaZgBZumFT4RDSPhxxffuo6LNBYI6TI4iH2XR6GHgjP.qj5wObw2', '开发工程师', '开发者', '<EMAIL>', '13800138002', 1, NULL, 1, NULL, NOW(), '开发工程师账户', 1, 1),
(4, 'designer', '$2a$10$MgaZgBZumFT4RDSPhxxffuo6LNBYI6TI4iH2XR6GHgjP.qj5wObw2', 'UI设计师', '设计师', '<EMAIL>', '13800138003', 2, NULL, 1, NULL, NOW(), 'UI设计师账户', 1, 1);

-- 初始化用户角色关联
INSERT INTO `sys_user_role` (`id`, `user_id`, `role_id`, `create_by`, `update_by`) VALUES
(1, 1, 1, 1, 1),  -- admin -> SUPER_ADMIN
(2, 2, 3, 1, 1),  -- testuser -> USER
(3, 3, 2, 1, 1),  -- developer -> ADMIN
(4, 4, 3, 1, 1);  -- designer -> USER

-- 初始化角色权限关联（给超级管理员角色分配所有权限）
INSERT INTO `sys_role_permission` (`id`, `role_id`, `permission_id`, `create_by`, `update_by`)
SELECT ROW_NUMBER() OVER (ORDER BY id) as id, 1 as role_id, id as permission_id, 1 as create_by, 1 as update_by
FROM `sys_permission` WHERE `status` = 1;

-- 初始化系统配置数据
INSERT INTO `sys_config` (`id`, `config_key`, `config_value`, `config_type`, `description`, `is_encrypted`, `is_system`, `status`, `create_by`, `update_by`) VALUES
-- 系统基础配置
(1, 'system.name', 'Demo Admin', 'STRING', '系统名称', 0, 1, 1, 1, 1),
(2, 'system.version', '1.0.0', 'STRING', '系统版本', 0, 1, 1, 1, 1),
(3, 'system.description', '后台管理系统', 'STRING', '系统描述', 0, 1, 1, 1, 1),
(4, 'system.logo', '', 'STRING', '系统Logo', 0, 1, 1, 1, 1),
(5, 'system.copyright', '© 2025 Demo Admin', 'STRING', '版权信息', 0, 1, 1, 1, 1),

-- 安全配置
(6, 'security.password.minLength', '6', 'NUMBER', '密码最小长度', 0, 1, 1, 1, 1),
(7, 'security.password.requireSpecial', 'false', 'BOOLEAN', '密码是否要求特殊字符', 0, 1, 1, 1, 1),
(8, 'security.login.failMaxCount', '5', 'NUMBER', '登录失败最大次数', 0, 1, 1, 1, 1),
(9, 'security.login.lockTime', '30', 'NUMBER', '账户锁定时间（分钟）', 0, 1, 1, 1, 1),
(10, 'security.session.timeout', '30', 'NUMBER', '会话超时时间（分钟）', 0, 1, 1, 1, 1),

-- 文件上传配置
(11, 'file.upload.maxSize', '10', 'NUMBER', '文件上传最大大小（MB）', 0, 1, 1, 1, 1),
(12, 'file.upload.allowedTypes', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx', 'STRING', '允许上传的文件类型', 0, 1, 1, 1, 1),
(13, 'file.upload.path', '/uploads', 'STRING', '文件上传路径', 0, 1, 1, 1, 1),

-- 邮件配置
(14, 'mail.host', '', 'STRING', '邮件服务器地址', 0, 0, 1, 1, 1),
(15, 'mail.port', '587', 'NUMBER', '邮件服务器端口', 0, 0, 1, 1, 1),
(16, 'mail.username', '', 'STRING', '邮件用户名', 0, 0, 1, 1, 1),
(17, 'mail.password', '', 'STRING', '邮件密码', 1, 0, 1, 1, 1),
(18, 'mail.from', '', 'STRING', '邮件发送者', 0, 0, 1, 1, 1),

-- 个人中心模块配置
(19, 'profile.module.enabled', 'true', 'BOOLEAN', '个人中心模块是否启用', 0, 1, 1, 1, 1),
(20, 'profile.avatar.maxSize', '5', 'NUMBER', '头像文件最大大小（MB）', 0, 1, 1, 1, 1),
(21, 'profile.avatar.allowedTypes', 'jpg,jpeg,png,gif', 'STRING', '允许的头像文件类型', 0, 1, 1, 1, 1),
(22, 'profile.password.historySize', '5', 'NUMBER', '密码历史记录保存数量', 0, 1, 1, 1, 1),
(23, 'profile.device.maxDevices', '10', 'NUMBER', '用户最大设备数量', 0, 1, 1, 1, 1),
(24, 'profile.notification.enabled', 'true', 'BOOLEAN', '是否启用个人中心通知', 0, 1, 1, 1, 1);

-- 初始化数据字典
INSERT INTO `sys_dict` (`id`, `dict_code`, `dict_name`, `description`, `status`, `create_by`, `update_by`) VALUES
(1, 'user_status', '用户状态', '用户账户状态字典', 1, 1, 1),
(2, 'user_gender', '用户性别', '用户性别字典', 1, 1, 1),
(3, 'yes_no', '是否', '通用是否字典', 1, 1, 1),
(4, 'menu_type', '菜单类型', '菜单类型字典', 1, 1, 1),
(5, 'permission_type', '权限类型', '权限类型字典', 1, 1, 1),
(6, 'data_scope', '数据权限', '数据权限范围字典', 1, 1, 1),
(7, 'config_type', '配置类型', '系统配置类型字典', 1, 1, 1);

-- 初始化字典项数据
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_code`, `item_name`, `item_value`, `sort_order`, `status`, `create_by`, `update_by`) VALUES
-- 用户状态
(1, 1, 'DISABLED', '禁用', '0', 1, 1, 1, 1),
(2, 1, 'ENABLED', '启用', '1', 2, 1, 1, 1),

-- 用户性别
(3, 2, 'FEMALE', '女', '0', 1, 1, 1, 1),
(4, 2, 'MALE', '男', '1', 2, 1, 1, 1),
(5, 2, 'UNKNOWN', '未知', '2', 3, 1, 1, 1),

-- 是否
(6, 3, 'NO', '否', '0', 1, 1, 1, 1),
(7, 3, 'YES', '是', '1', 2, 1, 1, 1),

-- 菜单类型
(8, 4, 'DIRECTORY', '目录', '1', 1, 1, 1, 1),
(9, 4, 'MENU', '菜单', '2', 2, 1, 1, 1),
(10, 4, 'BUTTON', '按钮', '3', 3, 1, 1, 1),

-- 权限类型
(11, 5, 'MENU', '菜单', '1', 1, 1, 1, 1),
(12, 5, 'BUTTON', '按钮', '2', 2, 1, 1, 1),
(13, 5, 'API', '接口', '3', 3, 1, 1, 1),

-- 数据权限
(14, 6, 'ALL', '全部数据', '1', 1, 1, 1, 1),
(15, 6, 'DEPT_AND_SUB', '本部门及以下数据', '2', 2, 1, 1, 1),
(16, 6, 'DEPT_ONLY', '本部门数据', '3', 3, 1, 1, 1),
(17, 6, 'SELF_ONLY', '仅本人数据', '4', 4, 1, 1, 1),
(18, 6, 'CUSTOM', '自定义数据', '5', 5, 1, 1, 1),

-- 配置类型
(19, 7, 'STRING', '字符串', 'STRING', 1, 1, 1, 1),
(20, 7, 'NUMBER', '数字', 'NUMBER', 2, 1, 1, 1),
(21, 7, 'BOOLEAN', '布尔值', 'BOOLEAN', 3, 1, 1, 1),
(22, 7, 'JSON', 'JSON对象', 'JSON', 4, 1, 1, 1);

-- 个人中心模块数据字典
INSERT INTO `sys_dict` (`id`, `dict_code`, `dict_name`, `description`, `status`, `create_by`, `update_by`) VALUES
(8, 'user_log_type', '用户日志类型', '用户操作日志类型字典', 1, 1, 1),
(9, 'device_type', '设备类型', '设备类型字典', 1, 1, 1),
(10, 'theme_type', '主题类型', '主题类型字典', 1, 1, 1),
(11, 'password_change_type', '密码修改类型', '密码修改类型字典', 1, 1, 1);

-- 个人中心模块字典项数据
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_code`, `item_name`, `item_value`, `sort_order`, `status`, `create_by`, `update_by`) VALUES
-- 用户日志类型
(23, 8, 'LOGIN', '登录', 'login', 1, 1, 1, 1),
(24, 8, 'LOGOUT', '登出', 'logout', 2, 1, 1, 1),
(25, 8, 'UPDATE', '更新', 'update', 3, 1, 1, 1),
(26, 8, 'UPLOAD', '上传', 'upload', 4, 1, 1, 1),
(27, 8, 'DOWNLOAD', '下载', 'download', 5, 1, 1, 1),
(28, 8, 'DELETE', '删除', 'delete', 6, 1, 1, 1),
-- 设备类型
(29, 9, 'DESKTOP', '桌面设备', 'desktop', 1, 1, 1, 1),
(30, 9, 'MOBILE', '移动设备', 'mobile', 2, 1, 1, 1),
(31, 9, 'TABLET', '平板设备', 'tablet', 3, 1, 1, 1),
-- 主题类型
(32, 10, 'LIGHT', '浅色主题', 'light', 1, 1, 1, 1),
(33, 10, 'DARK', '深色主题', 'dark', 2, 1, 1, 1),
(34, 10, 'AUTO', '自动主题', 'auto', 3, 1, 1, 1),
-- 密码修改类型
(35, 11, 'MANUAL', '手动修改', 'manual', 1, 1, 1, 1),
(36, 11, 'RESET', '重置密码', 'reset', 2, 1, 1, 1),
(37, 11, 'FORCE', '强制修改', 'force', 3, 1, 1, 1);
