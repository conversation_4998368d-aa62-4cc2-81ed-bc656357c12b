# SQL脚本说明

## 文件结构
- `schema.sql` - 核心数据库表结构
- `data.sql` - 基础初始化数据
- `modules/` - 各功能模块专用SQL脚本

## 模块说明
- `modules/profile_module.sql` - 个人中心模块表结构
- `modules/log_module.sql` - 日志模块表结构

## 执行顺序
1. 先执行 `schema.sql` 创建核心表结构
2. 执行 `data.sql` 插入基础数据
3. 按需执行各模块SQL脚本

## 数据库版本控制
建议采用Flyway或Liquibase进行数据库版本管理，格式：
- `V1__Initial_schema.sql`
- `V2__Add_profile_module.sql`
- `V3__Add_log_module.sql`

## ID分配策略
- 用户ID：1-1000 保留给系统用户和测试用户
- 配置项ID：1001-2000 保留给系统配置
- 字典ID：2001-3000 保留给系统字典
- 模块数据ID：3001+ 用于各功能模块的业务数据