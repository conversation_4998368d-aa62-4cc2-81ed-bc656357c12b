-- 系统日志表
CREATE TABLE `sys_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新者',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标志（0代表未删除，1代表已删除）',
    `log_type` VARCHAR(50) DEFAULT NULL COMMENT '日志类型 (BIZ_LOG, EXCEPTION_LOG, PERFORMANCE_LOG)',
    `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
    `username` VARCHAR(100) DEFAULT NULL COMMENT '用户名',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '操作描述',
    `request_uri` VARCHAR(500) DEFAULT NULL COMMENT '请求URI',
    `method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法 (GET, POST, PUT, DELETE)',
    `request_params` LONGTEXT DEFAULT NULL COMMENT '请求参数',
    `response` LONGTEXT DEFAULT NULL COMMENT '响应结果',
    `execution_time` BIGINT DEFAULT NULL COMMENT '执行时间 (毫秒)',
    `ip` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `success` TINYINT DEFAULT NULL COMMENT '操作状态 (1:成功, 0:失败)',
    `error_msg` TEXT DEFAULT NULL COMMENT '错误信息',
    `class_name` VARCHAR(200) DEFAULT NULL COMMENT '类名',
    `method_name` VARCHAR(100) DEFAULT NULL COMMENT '方法名',
    `module_name` VARCHAR(100) DEFAULT NULL COMMENT '模块名称',
    `operation_type` VARCHAR(100) DEFAULT NULL COMMENT '操作类型',
    `browser` VARCHAR(100) DEFAULT NULL COMMENT '浏览器类型',
    `os` VARCHAR(100) DEFAULT NULL COMMENT '操作系统',
    `device_info` VARCHAR(200) DEFAULT NULL COMMENT '设备信息',
    `session_id` VARCHAR(100) DEFAULT NULL COMMENT '会话ID',
    `referer` VARCHAR(500) DEFAULT NULL COMMENT '请求来源',
    `exception_type` VARCHAR(200) DEFAULT NULL COMMENT '异常类型',
    `stack_trace` LONGTEXT DEFAULT NULL COMMENT '异常堆栈',
    `level` VARCHAR(20) DEFAULT NULL COMMENT '日志级别 (INFO, WARN, ERROR, DEBUG)',
    `thread_name` VARCHAR(100) DEFAULT NULL COMMENT '线程名称',
        `ext_info` LONGTEXT DEFAULT NULL COMMENT '扩展字段 (JSON格式)',
    PRIMARY KEY (`id`),
    KEY `idx_log_type` (`log_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_success` (`success`),
    KEY `idx_module_name` (`module_name`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_ip` (`ip`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 系统日志详情表
CREATE TABLE `sys_log_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新者',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标志（0代表未删除，1代表已删除）',
    `log_id` BIGINT DEFAULT NULL COMMENT '关联的日志ID',
    `field_name` VARCHAR(100) DEFAULT NULL COMMENT '字段名称',
    `field_value` LONGTEXT DEFAULT NULL COMMENT '字段值',
    `field_type` VARCHAR(50) DEFAULT NULL COMMENT '字段类型 (STRING, NUMBER, BOOLEAN, JSON)',
    `field_desc` VARCHAR(500) DEFAULT NULL COMMENT '字段描述',
    `is_sensitive` TINYINT DEFAULT 0 COMMENT '是否敏感信息',
    `is_masked` TINYINT DEFAULT 0 COMMENT '是否已脱敏',
    `masked_value` VARCHAR(500) DEFAULT NULL COMMENT '脱敏后的值',
    `field_status` VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '字段状态 (ACTIVE, INACTIVE)',
    `sort_order` INT DEFAULT 0 COMMENT '排序序号',
    PRIMARY KEY (`id`),
    KEY `idx_log_id` (`log_id`),
    KEY `idx_field_name` (`field_name`),
    KEY `idx_field_type` (`field_type`),
    KEY `idx_is_sensitive` (`is_sensitive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志详情表';

-- 系统日志统计表
CREATE TABLE `sys_log_statistics` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新者',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标志（0代表未删除，1代表已删除）',
    `stat_date` DATE DEFAULT NULL COMMENT '统计日期',
    `stat_type` VARCHAR(20) DEFAULT NULL COMMENT '统计类型 (DAILY, WEEKLY, MONTHLY)',
    `module_name` VARCHAR(100) DEFAULT NULL COMMENT '模块名称',
    `operation_type` VARCHAR(100) DEFAULT NULL COMMENT '操作类型',
    `total_log_count` BIGINT DEFAULT 0 COMMENT '总日志数量',
    `success_log_count` BIGINT DEFAULT 0 COMMENT '成功日志数量',
    `failed_log_count` BIGINT DEFAULT 0 COMMENT '失败日志数量',
    `exception_log_count` BIGINT DEFAULT 0 COMMENT '异常日志数量',
    `performance_log_count` BIGINT DEFAULT 0 COMMENT '性能日志数量',
    `avg_execution_time` DECIMAL(10,2) DEFAULT NULL COMMENT '平均执行时间 (毫秒)',
    `min_execution_time` BIGINT DEFAULT NULL COMMENT '最小执行时间 (毫秒)',
    `max_execution_time` BIGINT DEFAULT NULL COMMENT '最大执行时间 (毫秒)',
    `total_execution_time` BIGINT DEFAULT 0 COMMENT '总执行时间 (毫秒)',
    `uv_count` BIGINT DEFAULT 0 COMMENT 'UV (独立用户数)',
    `pv_count` BIGINT DEFAULT 0 COMMENT 'PV (页面访问量)',
    `ip_count` BIGINT DEFAULT 0 COMMENT 'IP数量',
    `user_count` BIGINT DEFAULT 0 COMMENT '用户数量',
    `slow_query_count` BIGINT DEFAULT 0 COMMENT '慢查询数量 (执行时间 > 2秒)',
    `error_rate` DECIMAL(5,2) DEFAULT NULL COMMENT '错误率 (%)',
    `success_rate` DECIMAL(5,2) DEFAULT NULL COMMENT '成功率 (%)',
    `exception_rate` DECIMAL(5,2) DEFAULT NULL COMMENT '异常率 (%)',
    `response_time_distribution` LONGTEXT DEFAULT NULL COMMENT '响应时间分布 (JSON格式)',
    `user_activity_distribution` LONGTEXT DEFAULT NULL COMMENT '用户活跃度分布 (JSON格式)',
    `operation_frequency_distribution` LONGTEXT DEFAULT NULL COMMENT '操作频率分布 (JSON格式)',
    `geo_location_distribution` LONGTEXT DEFAULT NULL COMMENT '地理位置分布 (JSON格式)',
    `device_type_distribution` LONGTEXT DEFAULT NULL COMMENT '设备类型分布 (JSON格式)',
    `browser_type_distribution` LONGTEXT DEFAULT NULL COMMENT '浏览器类型分布 (JSON格式)',
    `os_distribution` LONGTEXT DEFAULT NULL COMMENT '操作系统分布 (JSON格式)',
    `ext_info` LONGTEXT DEFAULT NULL COMMENT '统计扩展信息 (JSON格式)',
    PRIMARY KEY (`id`),
    KEY `idx_stat_date` (`stat_date`),
    KEY `idx_stat_type` (`stat_type`),
    KEY `idx_module_name` (`module_name`),
    KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志统计表';

-- 系统日志配置表
CREATE TABLE `sys_log_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新者',
    `deleted` TINYINT DEFAULT 0 COMMENT '删除标志（0代表未删除，1代表已删除）',
    `config_name` VARCHAR(100) DEFAULT NULL COMMENT '配置名称',
    `config_code` VARCHAR(100) DEFAULT NULL COMMENT '配置代码',
    `config_type` VARCHAR(50) DEFAULT NULL COMMENT '配置类型 (MODULE, OPERATION, LEVEL, ALERT)',
    `module_name` VARCHAR(100) DEFAULT NULL COMMENT '模块名称',
    `operation_type` VARCHAR(100) DEFAULT NULL COMMENT '操作类型',
    `log_level` VARCHAR(20) DEFAULT NULL COMMENT '日志级别 (DEBUG, INFO, WARN, ERROR)',
    `enabled` TINYINT DEFAULT 1 COMMENT '是否启用',
    `record_request` TINYINT DEFAULT 1 COMMENT '是否记录请求参数',
    `record_response` TINYINT DEFAULT 1 COMMENT '是否记录响应结果',
    `record_execution_time` TINYINT DEFAULT 1 COMMENT '是否记录执行时间',
    `record_exception` TINYINT DEFAULT 1 COMMENT '是否记录异常信息',
    `record_user_info` TINYINT DEFAULT 1 COMMENT '是否记录用户信息',
    `record_request_info` TINYINT DEFAULT 1 COMMENT '是否记录请求信息',
        `send_real_time_push` TINYINT DEFAULT 1 COMMENT '是否发送实时推送',
    `enable_performance_alert` TINYINT DEFAULT 1 COMMENT '是否启用性能告警',
    `performance_alert_threshold` BIGINT DEFAULT 2000 COMMENT '性能告警阈值 (毫秒)',
    `enable_exception_alert` TINYINT DEFAULT 1 COMMENT '是否启用异常告警',
    `exception_alert_types` TEXT DEFAULT NULL COMMENT '异常告警类型',
    `enable_statistics` TINYINT DEFAULT 1 COMMENT '是否启用统计报告',
    `statistics_period` VARCHAR(20) DEFAULT 'DAILY' COMMENT '统计报告周期 (DAILY, WEEKLY, MONTHLY)',
    `log_retention_days` INT DEFAULT 30 COMMENT '日志保留天数',
    `sensitive_fields` LONGTEXT DEFAULT NULL COMMENT '敏感字段列表 (JSON格式)',
    `masking_rules` LONGTEXT DEFAULT NULL COMMENT '脱敏规则 (JSON格式)',
    `filter_rules` LONGTEXT DEFAULT NULL COMMENT '过滤规则 (JSON格式)',
    `alert_config` LONGTEXT DEFAULT NULL COMMENT '告警配置 (JSON格式)',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    `sort_order` INT DEFAULT 0 COMMENT '排序序号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_code` (`config_code`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_module_name` (`module_name`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_log_level` (`log_level`),
    KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志配置表';

-- 插入默认日志配置数据
INSERT INTO `sys_log_config` (`config_name`, `config_code`, `config_type`, `module_name`, `operation_type`, `log_level`, `enabled`, `record_request`, `record_response`, `record_execution_time`, `record_exception`, `record_user_info`, `record_request_info`, `send_real_time_push`, `enable_performance_alert`, `performance_alert_threshold`, `enable_exception_alert`, `exception_alert_types`, `enable_statistics`, `statistics_period`, `log_retention_days`, `sensitive_fields`, `description`, `sort_order`) VALUES
('用户模块日志配置', 'USER_MODULE_CONFIG', 'MODULE', '用户管理', NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'NullPointerException,SQLException', 1, 'DAILY', 30, '["password", "email", "phone"]', '用户模块的日志配置', 1),
('角色模块日志配置', 'ROLE_MODULE_CONFIG', 'MODULE', '角色管理', NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'NullPointerException,SQLException', 1, 'DAILY', 30, '["password"]', '角色模块的日志配置', 2),
('菜单模块日志配置', 'MENU_MODULE_CONFIG', 'MODULE', '菜单管理', NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'NullPointerException,SQLException', 1, 'DAILY', 30, NULL, '菜单模块的日志配置', 3),
('系统配置模块日志配置', 'CONFIG_MODULE_CONFIG', 'MODULE', '系统配置', NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'NullPointerException,SQLException', 1, 'DAILY', 30, '["config_value"]', '系统配置模块的日志配置', 4),
('个人中心模块日志配置', 'PROFILE_MODULE_CONFIG', 'MODULE', '个人中心', NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'NullPointerException,SQLException', 1, 'DAILY', 30, '["password", "email", "phone"]', '个人中心模块的日志配置', 5),
('性能监控配置', 'PERFORMANCE_MONITOR_CONFIG', 'LEVEL', NULL, NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'OutOfMemoryError,StackOverflowError', 1, 'DAILY', 30, NULL, '性能监控的日志配置', 6),
('异常监控配置', 'EXCEPTION_MONITOR_CONFIG', 'LEVEL', NULL, NULL, 'ERROR', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'Exception,Error', 1, 'DAILY', 30, NULL, '异常监控的日志配置', 7),
('操作日志配置', 'OPERATION_LOG_CONFIG', 'LEVEL', NULL, NULL, 'INFO', 1, 1, 1, 1, 1, 1, 1, 1, 1, 2000, 1, 'BusinessException', 1, 'DAILY', 30, NULL, '操作日志的配置', 8);

-- 插入默认日志统计数据
INSERT INTO `sys_log_statistics` (`stat_date`, `stat_type`, `module_name`, `operation_type`, `total_log_count`, `success_log_count`, `failed_log_count`, `exception_log_count`, `performance_log_count`, `avg_execution_time`, `min_execution_time`, `max_execution_time`, `total_execution_time`, `uv_count`, `pv_count`, `ip_count`, `user_count`, `slow_query_count`, `error_rate`, `success_rate`, `exception_rate`) VALUES
(CURDATE(), 'DAILY', '用户管理', '用户登录', 1000, 950, 50, 10, 1000, 150.50, 50, 5000, 150500, 100, 1000, 50, 100, 20, 5.00, 95.00, 1.00),
(CURDATE(), 'DAILY', '用户管理', '用户注册', 100, 90, 10, 5, 100, 200.00, 100, 1000, 20000, 80, 100, 30, 90, 10, 10.00, 90.00, 5.00),
(CURDATE(), 'DAILY', '角色管理', '角色分配', 200, 190, 10, 2, 200, 100.00, 50, 800, 20000, 50, 200, 20, 50, 5, 5.00, 95.00, 1.00),
(CURDATE(), 'DAILY', '菜单管理', '菜单配置', 150, 145, 5, 1, 150, 80.00, 40, 600, 12000, 30, 150, 15, 30, 3, 3.33, 96.67, 0.67),
(CURDATE(), 'DAILY', '系统配置', '配置更新', 80, 75, 5, 1, 80, 120.00, 60, 900, 9600, 20, 80, 10, 20, 2, 6.25, 93.75, 1.25),
(CURDATE(), 'DAILY', '个人中心', '信息修改', 300, 280, 20, 5, 300, 180.00, 80, 1200, 54000, 150, 300, 80, 150, 30, 6.67, 93.33, 1.67);