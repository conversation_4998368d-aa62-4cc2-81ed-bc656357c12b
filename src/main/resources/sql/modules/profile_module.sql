-- 个人中心模块数据库表结构
-- <AUTHOR>
-- @since 2025-01-27

USE `demo_admin`;

-- 1. 用户个人资料表
CREATE TABLE `sys_user_profile` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `bio` VARCHAR(500) DEFAULT NULL COMMENT '个人简介',
    `birthday` DATE DEFAULT NULL COMMENT '生日',
    `gender` TINYINT DEFAULT NULL COMMENT '性别：0-女，1-男，2-未知',
    `location` VARCHAR(100) DEFAULT NULL COMMENT '所在地',
    `website` VARCHAR(255) DEFAULT NULL COMMENT '个人网站',
    `github` VARCHAR(255) DEFAULT NULL COMMENT 'GitHub地址',
    `twitter` VARCHAR(255) DEFAULT NULL COMMENT 'Twitter地址',
    `linkedin` VARCHAR(255) DEFAULT NULL COMMENT 'LinkedIn地址',
    `timezone` VARCHAR(50) DEFAULT NULL COMMENT '时区',
    `language` VARCHAR(10) DEFAULT NULL COMMENT '语言偏好',
    `theme` VARCHAR(20) DEFAULT 'light' COMMENT '主题偏好：light-浅色，dark-深色',
    `email_notifications` TINYINT DEFAULT 1 COMMENT '邮件通知：0-关闭，1-开启',
    `sms_notifications` TINYINT DEFAULT 1 COMMENT '短信通知：0-关闭，1-开启',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `last_login_device` VARCHAR(100) DEFAULT NULL COMMENT '最后登录设备',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_nickname` (`nickname`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户个人资料表';

-- 2. 用户设备表
CREATE TABLE `sys_user_device` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备唯一标识',
    `device_name` VARCHAR(100) DEFAULT NULL COMMENT '设备名称',
    `device_type` VARCHAR(50) DEFAULT NULL COMMENT '设备类型：desktop-桌面，mobile-移动，tablet-平板',
    `device_brand` VARCHAR(50) DEFAULT NULL COMMENT '设备品牌',
    `device_model` VARCHAR(50) DEFAULT NULL COMMENT '设备型号',
    `os_name` VARCHAR(50) DEFAULT NULL COMMENT '操作系统名称',
    `os_version` VARCHAR(50) DEFAULT NULL COMMENT '操作系统版本',
    `browser_name` VARCHAR(50) DEFAULT NULL COMMENT '浏览器名称',
    `browser_version` VARCHAR(50) DEFAULT NULL COMMENT '浏览器版本',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `location` VARCHAR(100) DEFAULT NULL COMMENT '地理位置',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理信息',
    `is_trusted` TINYINT DEFAULT 0 COMMENT '是否信任设备：0-否，1-是',
    `last_active_time` DATETIME DEFAULT NULL COMMENT '最后活跃时间',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_device` (`user_id`, `device_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_last_active_time` (`last_active_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备表';

-- 3. 用户日志表
CREATE TABLE `sys_user_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    `log_type` VARCHAR(20) NOT NULL COMMENT '日志类型：login-登录，logout-登出，update-更新，upload-上传，download-下载，other-其他',
    `action` VARCHAR(100) NOT NULL COMMENT '操作描述',
    `module` VARCHAR(50) DEFAULT NULL COMMENT '操作模块',
    `request_url` VARCHAR(255) DEFAULT NULL COMMENT '请求URL',
    `request_method` VARCHAR(10) DEFAULT NULL COMMENT '请求方法',
    `request_params` TEXT DEFAULT NULL COMMENT '请求参数',
    `response_data` TEXT DEFAULT NULL COMMENT '响应数据',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `location` VARCHAR(100) DEFAULT NULL COMMENT '地理位置',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理信息',
    `device_id` VARCHAR(100) DEFAULT NULL COMMENT '设备ID',
    `execution_time` INT DEFAULT NULL COMMENT '执行时间（毫秒）',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-失败，1-成功',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_log_type` (`log_type`),
    KEY `idx_action` (`action`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户日志表';

-- 4. 用户偏好设置表
CREATE TABLE `sys_user_preference` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    `preference_key` VARCHAR(100) NOT NULL COMMENT '偏好键',
    `preference_value` TEXT DEFAULT NULL COMMENT '偏好值',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '偏好描述',
    `data_type` VARCHAR(20) DEFAULT 'STRING' COMMENT '数据类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔值，JSON-JSON对象',
    `is_system` TINYINT DEFAULT 0 COMMENT '是否系统默认：0-否，1-是',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_preference` (`user_id`, `preference_key`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_preference_key` (`preference_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户偏好设置表';

-- 5. 用户密码修改记录表
CREATE TABLE `sys_user_password_history` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID，关联sys_user表',
    `old_password` VARCHAR(255) NOT NULL COMMENT '旧密码（加密）',
    `new_password` VARCHAR(255) NOT NULL COMMENT '新密码（加密）',
    `change_type` VARCHAR(20) DEFAULT 'manual' COMMENT '修改类型：manual-手动修改，reset-重置，force-强制修改',
    `change_reason` VARCHAR(255) DEFAULT NULL COMMENT '修改原因',
    `ip_address` VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    `device_id` VARCHAR(100) DEFAULT NULL COMMENT '设备ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` BIGINT DEFAULT NULL COMMENT '创建人',
    `update_by` BIGINT DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户密码修改记录表';

-- 创建外键约束
ALTER TABLE `sys_user_profile` ADD CONSTRAINT `fk_profile_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `sys_user_device` ADD CONSTRAINT `fk_device_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `sys_user_log` ADD CONSTRAINT `fk_log_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `sys_user_preference` ADD CONSTRAINT `fk_preference_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `sys_user_password_history` ADD CONSTRAINT `fk_password_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 添加注释
ALTER TABLE `sys_user_profile` COMMENT '用户个人资料表：存储用户的个人资料和偏好设置';
ALTER TABLE `sys_user_device` COMMENT '用户设备表：记录用户登录设备信息，支持多设备管理';
ALTER TABLE `sys_user_log` COMMENT '用户日志表：记录用户的所有操作日志，用于审计和追踪';
ALTER TABLE `sys_user_preference` COMMENT '用户偏好设置表：存储用户的个性化偏好配置';
ALTER TABLE `sys_user_password_history` COMMENT '用户密码修改记录表：记录用户密码修改历史，用于安全审计';

-- 创建索引优化查询性能
CREATE INDEX `idx_profile_birthday` ON `sys_user_profile` (`birthday`);
CREATE INDEX `idx_profile_gender` ON `sys_user_profile` (`gender`);
CREATE INDEX `idx_device_type` ON `sys_user_device` (`device_type`);
CREATE INDEX `idx_device_trusted` ON `sys_user_device` (`is_trusted`);
CREATE INDEX `idx_log_module` ON `sys_user_log` (`module`);
CREATE INDEX `idx_log_status` ON `sys_user_log` (`status`);
CREATE INDEX `idx_preference_data_type` ON `sys_user_preference` (`data_type`);
CREATE INDEX `idx_password_change_type` ON `sys_user_password_history` (`change_type`);