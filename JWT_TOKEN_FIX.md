# JWT令牌格式错误修复指南

## 问题描述
访问 `/api/system/user/page` 接口返回403错误，后端日志显示：`JWT令牌格式错误: Compact JWT strings may not contain whitespace`

## 根本原因
JWT令牌中包含了空格字符，而JWT规范不允许令牌中包含空格。

## 修复方案

### 1. 前端修复（推荐）
在 `request.ts` 的请求拦截器中添加token清理逻辑：

```typescript
// 添加认证token，并清理空格
if (authStore.token) {
  config.headers = config.headers || {}
  // 清理token前后的空格
  const cleanToken = authStore.token.trim()
  config.headers.Authorization = `Bearer ${cleanToken}`
}
```

### 2. 后端修复（防御性编程）
在 `JwtUtils.java` 的 `getTokenFromHeader` 方法中添加清理逻辑：

```java
public String getTokenFromHeader(String authHeader) {
    if (authHeader != null && authHeader.startsWith(prefix)) {
        String token = authHeader.substring(prefix.length());
        // 清理token前后的空格
        return token != null ? token.trim() : null;
    }
    return null;
}
```

## 使用诊断工具

### 访问诊断页面
在浏览器中访问：`http://localhost:3000/debug/token`

### 诊断工具功能
1. **查看当前Token状态**
   - Token是否存在
   - Token长度
   - 是否包含空格
   - Token前20字符和后20字符

2. **操作功能**
   - 清理Token空格
   - 清除Token
   - 刷新状态

3. **测试API**
   - 测试用户列表API调用
   - 查看API调用结果

### 建议的修复步骤
1. 首先使用诊断工具检查当前token状态
2. 如果发现token包含空格，点击"清理Token空格"按钮
3. 重新测试API调用
4. 如果问题仍然存在，清除token后重新登录

## 预防措施
1. 在前端所有处理token的地方都添加trim()操作
2. 在后端添加防御性的token清理逻辑
3. 定期检查存储的token是否被污染

## 注意事项
- JWT令牌格式要求非常严格，任何空格都会导致验证失败
- 在开发调试时，可以使用诊断工具快速定位问题
- 在生产环境中，建议添加更多的token验证和清理逻辑