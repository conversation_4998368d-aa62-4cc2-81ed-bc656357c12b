# 403 Forbidden 错误修复指南

## 问题描述
访问 `/api/system/user/page` 接口时返回 403 Forbidden 错误，表示权限不足。

## 诊断步骤

### 1. 测试基本连接
首先测试基本连接是否正常：
```
GET http://localhost:8080/api/public/test/hello
```

### 2. 测试JWT认证
测试JWT token是否正确传递：
```
GET http://localhost:8080/api/auth/debug/status
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
```

### 3. 测试无权限版本接口
测试接口基本功能是否正常：
```
GET http://localhost:8080/api/test/user/page?pageNum=1&pageSize=10
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
```

### 4. 测试权限验证
测试带权限验证的接口：
```
GET http://localhost:8080/api/public/test/auth/user
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
```

## 修复方法

### 方法1：使用SQL脚本修复权限
执行以下SQL脚本修复权限分配：

```bash
# 连接到MySQL
mysql -u root -p

# 执行修复脚本
USE demo_admin;
SOURCE src/main/resources/sql/quick_fix_permissions.sql;
```

### 方法2：手动修复权限
如果SQL脚本不执行，可以手动执行以下SQL：

```sql
-- 1. 确保admin用户拥有SUPER_ADMIN角色
DELETE FROM sys_user_role WHERE user_id = 1;
INSERT INTO sys_user_role (user_id, role_id, create_by, update_by) 
VALUES (1, 1, 1, 1);

-- 2. 确保SUPER_ADMIN角色拥有所有权限
DELETE FROM sys_role_permission WHERE role_id = 1;
INSERT INTO sys_role_permission (role_id, permission_id, create_by, update_by)
SELECT 1, id, 1, 1
FROM sys_permission WHERE status = 1 AND deleted = 0;
```

### 方法3：临时绕过权限验证（仅用于测试）
如果需要验证接口功能，可以暂时注释掉权限验证注解：

在 `SysUserController.java` 中：
```java
// 暂时注释掉这行
// @PreAuthorize("hasAuthority('system:user:list')")
public Result<Page<UserResponse>> getUserPage(@Valid UserQueryRequest request) {
    // ...
}
```

## 验证修复结果

### 1. 验证权限分配
```sql
-- 检查admin用户的权限
SELECT u.username, p.permission_code, p.permission_name
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role_permission rp ON ur.role_id = rp.role_id
JOIN sys_permission p ON rp.permission_id = p.id
WHERE u.username = 'admin' AND p.permission_code = 'system:user:list';
```

### 2. 测试接口访问
修复后，使用admin账户登录测试：
```
GET http://localhost:8080/api/system/user/page?pageNum=1&pageSize=10
Headers:
  Authorization: Bearer YOUR_JWT_TOKEN
```

### 3. 检查日志
查看后端日志，确认：
- JWT token是否正确解析
- 用户权限是否正确加载
- 权限验证是否通过

## 常见问题

### Q: 仍然返回403错误
A: 检查：
1. 是否使用admin账户登录
2. JWT token是否有效
3. 数据库权限是否正确分配
4. 重启应用后重试

### Q: 数据库权限分配正确但仍无权限
A: 可能是缓存问题，尝试：
1. 重启应用
2. 清除Redis缓存
3. 重新登录获取新的JWT token

### Q: 测试接口正常但原接口仍报错
A: 确认：
1. 请求路径是否正确（/api/system/user/page）
2. 请求方法是否正确（GET）
3. 请求参数是否正确

## 联系信息
如果问题仍然存在，请检查：
1. 后端日志输出
2. 数据库连接状态
3. Redis连接状态
4. Spring Security配置