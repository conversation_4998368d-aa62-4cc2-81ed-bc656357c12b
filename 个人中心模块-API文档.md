# 个人中心模块 API 接口文档

## 模块概述
个人中心模块提供了用户个人信息管理、密码修改、头像上传、偏好设置、设备管理和操作日志等功能，为用户提供个性化的账户管理体验。

## 1. 个人信息管理 API

### 1.1 获取个人信息
- **URL**: `GET /api/profile/info`
- **权限**: `isAuthenticated()`
- **响应**:
```json
{
  "code": 200,
  "message": "获取个人信息成功",
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "nickname": "超级管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "gender": 1,
    "genderDesc": "男",
    "avatar": "https://example.com/avatar.jpg",
    "bio": "热爱技术，专注于企业级应用开发",
    "birthday": "1990-01-01",
    "location": "北京市",
    "website": "https://example.com",
    "github": "https://github.com/username",
    "twitter": "https://twitter.com/username",
    "linkedin": "https://linkedin.com/in/username",
    "timezone": "Asia/Shanghai",
    "language": "zh-CN",
    "theme": "light",
    "emailNotifications": 1,
    "smsNotifications": 1,
    "lastLoginTime": "2025-01-27 10:00:00",
    "lastLoginIp": "*************",
    "lastLoginDevice": "Chrome on Windows",
    "loginCount": 150,
    "passwordUpdateTime": "2025-01-20 15:30:00",
    "preferences": {
      "pageSize": 20,
      "menuCollapsed": false,
      "enableNotifications": true
    },
    "devices": [
      {
        "id": 1,
        "deviceName": "Chrome on Windows",
        "deviceType": "desktop",
        "lastActiveTime": "2025-01-27 10:00:00",
        "isTrusted": true,
        "status": 1,
        "statusDesc": "在线"
      }
    ],
    "createTime": "2025-01-01 09:00:00",
    "updateTime": "2025-01-27 10:00:00"
  }
}
```

### 1.2 更新个人信息
- **URL**: `PUT /api/profile/info`
- **权限**: `isAuthenticated()`
- **请求体**:
```json
{
  "nickname": "新昵称",
  "bio": "个人简介更新",
  "birthday": "1990-01-01",
  "gender": 1,
  "location": "北京市",
  "website": "https://example.com",
  "github": "https://github.com/username",
  "twitter": "https://twitter.com/username",
  "linkedin": "https://linkedin.com/in/username",
  "timezone": "Asia/Shanghai",
  "language": "zh-CN",
  "theme": "light",
  "emailNotifications": 1,
  "smsNotifications": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "个人信息更新成功",
  "data": {
    // 更新后的完整个人信息
  }
}
```

## 2. 密码管理 API

### 2.1 修改密码
- **URL**: `PUT /api/profile/password`
- **权限**: `isAuthenticated()`
- **请求体**:
```json
{
  "oldPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "confirmPassword": "newPassword456"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

### 2.2 获取密码修改历史
- **URL**: `GET /api/profile/password/history`
- **权限**: `isAuthenticated()`
- **参数**: `limit` - 限制数量（默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "获取密码修改历史成功",
  "data": [
    {
      "id": 1,
      "changeType": "manual",
      "changeTypeDesc": "手动修改",
      "changeReason": "用户自行修改",
      "ipAddress": "*************",
      "deviceInfo": "Chrome on Windows",
      "createTime": "2025-01-27 10:00:00"
    }
  ]
}
```

## 3. 头像管理 API

### 3.1 上传头像（文件方式）
- **URL**: `POST /api/profile/avatar`
- **权限**: `isAuthenticated()`
- **参数**: `file` - 头像文件
- **文件要求**:
  - 格式：jpg、jpeg、png、gif
  - 大小：最大5MB
- **响应**:
```json
{
  "code": 200,
  "message": "头像上传成功",
  "data": "https://example.com/uploads/avatar_1643289600000.jpg"
}
```

### 3.2 上传头像（Base64方式）
- **URL**: `POST /api/profile/avatar/base64`
- **权限**: `isAuthenticated()`
- **请求体**:
```json
{
  "avatarBase64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
  "fileType": "image/jpeg",
  "fileName": "avatar.jpg"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "头像上传成功",
  "data": "https://example.com/uploads/avatar_1643289600000.jpg"
}
```

## 4. 个人设置 API

### 4.1 获取个人设置
- **URL**: `GET /api/profile/settings`
- **权限**: `isAuthenticated()`
- **响应**:
```json
{
  "code": 200,
  "message": "获取个人设置成功",
  "data": {
    "id": 1,
    "userId": 1,
    "theme": "light",
    "language": "zh-CN",
    "timezone": "Asia/Shanghai",
    "emailNotifications": 1,
    "smsNotifications": 1,
    "createTime": "2025-01-01 09:00:00",
    "updateTime": "2025-01-27 10:00:00"
  }
}
```

### 4.2 更新个人设置
- **URL**: `PUT /api/profile/settings`
- **权限**: `isAuthenticated()`
- **请求体**:
```json
{
  "theme": "dark",
  "language": "en-US",
  "timezone": "America/New_York",
  "emailNotifications": 0,
  "smsNotifications": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "个人设置更新成功",
  "data": {
    // 更新后的完整个人设置
  }
}
```

## 5. 偏好设置 API

### 5.1 获取用户偏好设置
- **URL**: `GET /api/profile/preferences`
- **权限**: `isAuthenticated()`
- **响应**:
```json
{
  "code": 200,
  "message": "获取用户偏好设置成功",
  "data": {
    "pageSize": 20,
    "menuCollapsed": false,
    "enableNotifications": true,
    "defaultView": "list",
    "autoSave": true
  }
}
```

### 5.2 设置用户偏好
- **URL**: `POST /api/profile/preferences`
- **权限**: `isAuthenticated()`
- **请求体**:
```json
{
  "preferenceKey": "pageSize",
  "preferenceValue": "50",
  "dataType": "NUMBER",
  "description": "每页显示数量"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "用户偏好设置成功",
  "data": null
}
```

### 5.3 获取指定偏好设置
- **URL**: `GET /api/profile/preferences/{preferenceKey}`
- **权限**: `isAuthenticated()`
- **参数**: `preferenceKey` - 偏好键
- **响应**:
```json
{
  "code": 200,
  "message": "获取指定偏好设置成功",
  "data": 50
}
```

### 5.4 删除偏好设置
- **URL**: `DELETE /api/profile/preferences/{preferenceKey}`
- **权限**: `isAuthenticated()`
- **参数**: `preferenceKey` - 偏好键
- **响应**:
```json
{
  "code": 200,
  "message": "偏好设置删除成功",
  "data": null
}
```

## 6. 设备管理 API

### 6.1 获取登录设备列表
- **URL**: `GET /api/profile/devices`
- **权限**: `isAuthenticated()`
- **响应**:
```json
{
  "code": 200,
  "message": "获取登录设备列表成功",
  "data": [
    {
      "id": 1,
      "deviceId": "device_123",
      "deviceName": "Chrome on Windows",
      "deviceType": "desktop",
      "deviceBrand": "Windows",
      "deviceModel": "Chrome 120.0.0.0",
      "osName": "Windows",
      "osVersion": "10",
      "browserName": "Chrome",
      "browserVersion": "120.0.0.0",
      "ipAddress": "*************",
      "location": "北京市",
      "isTrusted": 1,
      "lastActiveTime": "2025-01-27 10:00:00",
      "loginCount": 50,
      "status": 1,
      "statusDesc": "在线",
      "createTime": "2025-01-01 09:00:00",
      "updateTime": "2025-01-27 10:00:00"
    }
  ]
}
```

### 6.2 删除登录设备
- **URL**: `DELETE /api/profile/devices/{deviceId}`
- **权限**: `isAuthenticated()`
- **参数**: `deviceId` - 设备ID
- **响应**:
```json
{
  "code": 200,
  "message": "登录设备删除成功",
  "data": null
}
```

### 6.3 设置设备信任状态
- **URL**: `PUT /api/profile/devices/{deviceId}/trust`
- **权限**: `isAuthenticated()`
- **参数**: 
  - `deviceId` - 设备ID
  - `isTrust` - 是否信任（true/false）
- **响应**:
```json
{
  "code": 200,
  "message": "设备信任状态设置成功",
  "data": null
}
```

## 7. 操作日志 API

### 7.1 分页获取个人操作日志
- **URL**: `GET /api/profile/logs`
- **权限**: `isAuthenticated()`
- **参数**:
  - `pageNum`: 当前页码
  - `pageSize`: 页面大小
  - `logType`: 日志类型（可选）
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "获取个人操作日志成功",
  "data": {
    "records": [
      {
        "id": 1,
        "logType": "update",
        "action": "更新个人信息",
        "module": "profile",
        "requestUrl": "/api/profile/info",
        "requestMethod": "PUT",
        "requestParams": "{\"nickname\":\"新昵称\"}",
        "responseData": "SUCCESS",
        "ipAddress": "*************",
        "location": "北京市",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "deviceId": "device_123",
        "executionTime": 150,
        "status": 1,
        "errorMessage": null,
        "createTime": "2025-01-27 10:00:00",
        "updateTime": "2025-01-27 10:00:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 8. 数据模型

### 8.1 个人信息数据模型
```json
{
  "id": 1,
  "username": "admin",
  "realName": "系统管理员",
  "nickname": "超级管理员",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "gender": 1,
  "genderDesc": "男",
  "avatar": "https://example.com/avatar.jpg",
  "bio": "个人简介",
  "birthday": "1990-01-01",
  "location": "北京市",
  "website": "https://example.com",
  "github": "https://github.com/username",
  "twitter": "https://twitter.com/username",
  "linkedin": "https://linkedin.com/in/username",
  "timezone": "Asia/Shanghai",
  "language": "zh-CN",
  "theme": "light",
  "emailNotifications": 1,
  "smsNotifications": 1,
  "lastLoginTime": "2025-01-27 10:00:00",
  "lastLoginIp": "*************",
  "lastLoginDevice": "Chrome on Windows",
  "loginCount": 150,
  "passwordUpdateTime": "2025-01-20 15:30:00",
  "preferences": {},
  "devices": [],
  "createTime": "2025-01-01 09:00:00",
  "updateTime": "2025-01-27 10:00:00"
}
```

### 8.2 设备数据模型
```json
{
  "id": 1,
  "deviceId": "device_123",
  "deviceName": "Chrome on Windows",
  "deviceType": "desktop",
  "deviceBrand": "Windows",
  "deviceModel": "Chrome 120.0.0.0",
  "osName": "Windows",
  "osVersion": "10",
  "browserName": "Chrome",
  "browserVersion": "120.0.0.0",
  "ipAddress": "*************",
  "location": "北京市",
  "isTrusted": 1,
  "lastActiveTime": "2025-01-27 10:00:00",
  "loginCount": 50,
  "status": 1,
  "statusDesc": "在线",
  "createTime": "2025-01-01 09:00:00",
  "updateTime": "2025-01-27 10:00:00"
}
```

## 9. 错误码说明
- **400**: 请求参数错误
- **401**: 未授权访问
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

## 10. 权限说明
所有个人中心API都需要用户登录认证（`isAuthenticated()`），部分操作可能需要特定权限。

## 11. 安全特性
- 密码修改需要验证旧密码
- 密码历史记录检查，防止重复使用旧密码
- 设备管理支持信任状态设置
- 所有操作都有详细的日志记录
- 支持文件上传安全验证